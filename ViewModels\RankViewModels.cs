using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    public class RankViewModel
    {
        public int RankId { get; set; }

        [Display(Name = "اسم الرتبة")]
        public string RankName { get; set; } = string.Empty;

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; }

        [Display(Name = "الحالة")]
        public bool IsActive { get; set; } = true;
    }

    public class CreateRankViewModel
    {
        [Required(ErrorMessage = "اسم الرتبة مطلوب")]
        [Display(Name = "اسم الرتبة")]
        [StringLength(100, ErrorMessage = "اسم الرتبة يجب أن يكون أقل من 100 حرف")]
        public string RankName { get; set; } = string.Empty;

        [Display(Name = "ترتيب العرض")]
        [Range(1, 1000, ErrorMessage = "ترتيب العرض يجب أن يكون بين 1 و 1000")]
        public int DisplayOrder { get; set; } = 1;

        [Display(Name = "الحالة")]
        public bool IsActive { get; set; } = true;
    }

    public class EditRankViewModel
    {
        public int RankId { get; set; }

        [Required(ErrorMessage = "اسم الرتبة مطلوب")]
        [Display(Name = "اسم الرتبة")]
        [StringLength(100, ErrorMessage = "اسم الرتبة يجب أن يكون أقل من 100 حرف")]
        public string RankName { get; set; } = string.Empty;

        [Display(Name = "ترتيب العرض")]
        [Range(1, 1000, ErrorMessage = "ترتيب العرض يجب أن يكون بين 1 و 1000")]
        public int DisplayOrder { get; set; }

        [Display(Name = "الحالة")]
        public bool IsActive { get; set; }
    }
} 
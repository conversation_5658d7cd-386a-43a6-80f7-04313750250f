@model RafoEvaluation.ViewModels.EvaluationCreateViewModel

@{
    ViewData["Title"] = "إنشاء تقييم جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus ms-2"></i>إنشاء تقييم جديد
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label asp-for="CandidateId" class="form-label"></label>
                                    <select asp-for="CandidateId" class="form-select" required>
                                        <option value="">اختر المرشح</option>
                                        @foreach (var candidate in Model.AvailableCandidates)
                                        {
                                            <option value="@candidate.CandidateId">
                                                @candidate.FullName - @candidate.ServiceNumber
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="CandidateId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label asp-for="CommitteeId" class="form-label"></label>
                                    <select asp-for="CommitteeId" class="form-select" required>
                                        <option value="">اختر اللجنة</option>
                                        @foreach (var committee in Model.AvailableCommittees)
                                        {
                                            <option value="@committee.CommitteeId">
                                                @committee.CommitteeName
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="CommitteeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label asp-for="EvaluationFormId" class="form-label"></label>
                                    <select asp-for="EvaluationFormId" class="form-select" required>
                                        <option value="">اختر نموذج التقييم</option>
                                        @foreach (var form in Model.AvailableForms)
                                        {
                                            <option value="@form.EvaluationFormId">
                                                @form.Title
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="EvaluationFormId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="GeneralNotes" class="form-label"></label>
                            <textarea asp-for="GeneralNotes" class="form-control" rows="3" placeholder="ملاحظات عامة (اختياري)"></textarea>
                            <span asp-validation-for="GeneralNotes" class="text-danger"></span>
                        </div>

                        <div class="form-actions mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save ms-1"></i> إنشاء التقييم
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-times ms-1"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Form validation
            $('form').on('submit', function(e) {
                let isValid = true;
                
                // Check required fields
                $('select[required]').each(function() {
                    if (!$(this).val()) {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                }
            });
            
            // Remove validation classes when user starts typing/selecting
            $('select, textarea').on('input change', function() {
                $(this).removeClass('is-invalid');
            });
        });
    </script>
} 
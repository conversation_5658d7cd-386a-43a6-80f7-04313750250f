@model RafoEvaluation.ViewModels.RankViewModel
@{
    ViewData["Title"] = "تفاصيل الرتبة";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-medal breadcrumb-icon"></i>
                    تفاصيل الرتبة
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Rank">
                                <i class="fas fa-medal"></i> الرتب
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-eye"></i> تفاصيل
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-eye ms-2"></i>تفاصيل الرتبة
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-medal ms-1"></i>اسم الرتبة
                            </label>
                            <div class="form-control-plaintext">
                                <strong class="fs-5">@Model.RankName</strong>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-sort-numeric-up ms-1"></i>ترتيب العرض
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Model.DisplayOrder</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on ms-1"></i>الحالة
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-times-circle me-1"></i>غير نشط
                                    </span>
                                }
                            </div>
                        </div>


                    </div>



                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.RankId" class="btn btn-warning me-2">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.RankId" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 
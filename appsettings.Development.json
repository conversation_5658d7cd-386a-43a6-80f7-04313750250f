{"ConnectionStrings": {"DefaultConnection": "Server=ALMAZIDI\\MSSQLSERVER2022;Database=RafoEvaluation;Trusted_Connection=true;TrustServerCertificate=true;"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.EntityFrameworkCore.Infrastructure": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Development": {"EnableSwagger": true, "EnableDetailedErrors": true, "EnableDeveloperExceptionPage": true, "EnableDatabaseErrorPage": true, "SeedData": true, "LogSqlQueries": true}, "AllowedHosts": "*"}
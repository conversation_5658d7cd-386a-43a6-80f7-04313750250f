@model RafoEvaluation.ViewModels.EditRankViewModel
@{
    ViewData["Title"] = "تعديل الرتبة";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-medal breadcrumb-icon"></i>
                    تعديل الرتبة
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Rank">
                                <i class="fas fa-medal"></i> الرتب
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-edit"></i> تعديل
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-edit ms-2 text-dark"></i>تعديل الرتبة
                    </h6>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" id="editRankForm">
                        <input type="hidden" asp-for="RankId" />
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="RankName" class="form-label">
                                    <i class="fas fa-medal ms-1"></i>اسم الرتبة <span class="text-danger">*</span>
                                </label>
                                <input asp-for="RankName" class="form-control" placeholder="أدخل اسم الرتبة" />
                                <span asp-validation-for="RankName" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="DisplayOrder" class="form-label">
                                    <i class="fas fa-sort-numeric-up ms-1"></i>ترتيب العرض
                                </label>
                                <input asp-for="DisplayOrder" class="form-control" type="number" min="1" max="1000" />
                                <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                                <div class="form-text">ترتيب ظهور الرتبة في القوائم</div>
                            </div>
                        </div>



                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        <i class="fas fa-toggle-on ms-1"></i>رتبة نشطة
                                    </label>
                                    <div class="form-text">الرتب النشطة فقط تظهر في قوائم الاختيار</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 
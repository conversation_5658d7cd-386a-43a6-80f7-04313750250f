# نظام دليل الاستخدام للمقيمين - User Guide System

## نظرة عامة

تم إنشاء نظام شامل لدليل الاستخدام مخصص للمقيمين في نظام تقييم المرشحين. النظام يوفر شرحاً تفاعلياً مع الصور والإرشادات لتسهيل استخدام النظام.

## الميزات الجديدة

### 🎯 1. دليل الاستخدام في لوحة التحكم
- **القسم التفاعلي**: متاح للمقيمين فقط في لوحة التحكم الرئيسية
- **التبويبات المنظمة**: أربعة تبويبات منظمة للمحتوى
- **التفاعل المتقدم**: تأثيرات بصرية وتفاعل مع العناصر

### 📖 2. صفحة دليل المستخدم الشامل
- **صفحة منفصلة**: `/Home/UserGuide` للوصول المباشر للدليل
- **رابط في القائمة**: متاح في القائمة الجانبية للمقيمين
- **المحتوى التفصيلي**: دليل شامل خطوة بخطوة

### 🎨 3. التصميم والتفاعل
- **CSS مخصص**: ملف منفصل لتنسيق دليل الاستخدام
- **تأثيرات بصرية**: حركات وتحولات مميزة
- **دعم RTL**: تصميم كامل للغة العربية

## التبويبات الرئيسية

### 📋 1. نظرة عامة
- تعريف بالنظام وأهدافه
- شعار سلاح الجو السلطاني العماني
- الميزات الأساسية للنظام
- مقدمة للمقيمين الجدد

### 🔄 2. عملية التقييم
- خطوات التقييم بالتفصيل (4 خطوات)
- صور توضيحية لكل خطوة
- نصائح وإرشادات مهمة
- تحذيرات ونقاط انتباه

### 📊 3. التقارير والنتائج
- أنواع التقارير المتاحة
- كيفية الوصول للتقارير
- شرح مؤشرات الأداء
- التقارير المطبوعة

### 💡 4. نصائح مهمة
- أفضل الممارسات للمقيمين
- التحذيرات الأمنية
- نصائح الأداء
- معلومات الدعم الفني

## الملفات المضافة/المحدثة

### 1. Views/Home/Index.cshtml
```razor
// إضافة قسم دليل الاستخدام للمقيمين
@if(isEvaluator && !isAdmin)
{
    <!-- User Guide for Evaluators -->
    // قسم تفاعلي بأربعة تبويبات
}
```

### 2. Views/Home/UserGuide.cshtml (جديد)
```razor
// صفحة دليل المستخدم الشامل
// محتوى تفصيلي مع خطوات العمل
// أسئلة شائعة وإجابات
// معلومات التواصل والدعم
```

### 3. Controllers/HomeController.cs
```csharp
// إضافة Action جديد
public IActionResult UserGuide()
{
    return View();
}
```

### 4. Views/Shared/_Layout.cshtml
```razor
// إضافة رابط دليل الاستخدام في القائمة الجانبية
@if (User.IsInRole("Evaluator") || User.IsInRole("ChairmanMember") || User.IsInRole("CommitteeMember"))
{
    <li class="nav-item">
        <a class="nav-link" href="@Url.Action("UserGuide", "Home")">
            <i class="fas fa-question-circle ms-2"></i>
            <span>دليل الاستخدام</span>
        </a>
    </li>
}
```

### 5. wwwroot/css/user-guide.css (جديد)
```css
// تنسيقات مخصصة لدليل الاستخدام
// تأثيرات بصرية وحركات
// دعم RTL والطباعة
// تصميم متجاوب
```

### 6. wwwroot/Images/guide/ (مجلد جديد)
```
// مجلد للصور التوضيحية
step1-dashboard.png
step2-candidates.png
step3-evaluation.png
step4-save.png
```

## خطوات عملية التقييم المشروحة

### الخطوة 1: تسجيل الدخول والوصول للنظام
- إدخال رقم الخدمة العسكري
- إدخال كلمة المرور
- الانتقال إلى لوحة التحكم
- التحقق من الصلاحيات

### الخطوة 2: الوصول إلى قائمة المرشحين
- النقر على "عرض النتائج"
- استخدام البحث والفلترة
- عرض المرشحين المسندين فقط
- التحقق من حالة التقييم

### الخطوة 3: بدء عملية التقييم
- اختيار المرشح المطلوب
- النقر على زر "تقييم"
- مراجعة معلومات المرشح
- فتح نموذج التقييم

### الخطوة 4: ملء نموذج التقييم
- قراءة معايير التقييم
- إدخال الدرجات حسب المقياس
- إضافة ملاحظات توضيحية
- مراجعة البيانات

### الخطوة 5: حفظ واعتماد التقييم
- التأكد من اكتمال الحقول
- المراجعة النهائية
- حفظ التقييم
- تأكيد النجاح

## المعايير والدرجات

### مقياس التقييم
- **ممتاز**: 90-100
- **جيد جداً**: 80-89
- **جيد**: 70-79
- **مقبول**: 60-69
- **ضعيف**: أقل من 60

### أنواع المعايير
1. **المعايير الشخصية**: الانضباط، الأخلاق، القيادة
2. **المعايير المهنية**: الخبرة، المهارات، الأداء
3. **المعايير التعليمية**: التأهيل، التدريب، التطوير

## الأسئلة الشائعة

### س: ماذا أفعل إذا نسيت كلمة المرور؟
ج: تواصل مع مدير النظام أو الدعم الفني لإعادة تعيين كلمة المرور.

### س: هل يمكنني تعديل التقييم بعد الحفظ؟
ج: لا، لا يمكن تعديل التقييم بعد الحفظ النهائي للحفاظ على نزاهة العملية.

### س: ماذا أفعل إذا واجهت مشكلة تقنية؟
ج: تأكد من الاتصال بالإنترنت، حدث الصفحة، وتواصل مع الدعم الفني.

### س: كيف أعرف أن التقييم تم حفظه بنجاح؟
ج: ستظهر رسالة تأكيد خضراء وتتغير حالة المرشح إلى "مكتمل".

## ميزات JavaScript المضافة

### التفاعل مع الصور
```javascript
// تكبير الصور عند النقر
$('.guide-screenshot').on('click', function() {
    // إنشاء مودال لعرض الصورة بحجم أكبر
});
```

### تأثيرات التبويبات
```javascript
// إضافة تأثيرات عند تغيير التبويبات
$('#userGuideTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
    // إضافة كلاس الحركة
});
```

### تفاعل البطاقات
```javascript
// تأثيرات hover للبطاقات
$('.guide-feature-card').hover(function() {
    // تأثيرات بصرية
});
```

## إمكانية الوصول والتجاوب

### دعم الأجهزة المختلفة
- **الكمبيوتر المكتبي**: تخطيط كامل مع جميع الميزات
- **الأجهزة اللوحية**: تخطيط متكيف مع التبويبات
- **الهواتف الذكية**: واجهة مبسطة ومحسنة

### دعم الطباعة
- **أنماط طباعة مخصصة**: تخطيط محسن للطباعة
- **إخفاء العناصر التفاعلية**: في وضع الطباعة
- **تحسين النص**: خطوط وألوان مناسبة للطباعة

## معلومات التواصل والدعم

### الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: 1234-5678
- **ساعات العمل**: 8:00 ص - 4:00 م

### الاستفسارات الإدارية
- منسق اللجنة
- رئيس اللجنة
- مدير النظام

## تحسينات مستقبلية مقترحة

### المرحلة القادمة
1. **فيديوهات تعليمية**: إضافة مقاطع فيديو تفاعلية
2. **جولة تفاعلية**: نظام guided tour للمستخدمين الجدد
3. **اختبارات معرفية**: اختبارات قصيرة لتأكيد الفهم
4. **نظام ردود الفعل**: تقييم فائدة الدليل

### تحسينات تقنية
1. **تحميل تدريجي**: تحسين أداء تحميل الصور
2. **وضع عدم الاتصال**: إمكانية عرض الدليل بدون إنترنت
3. **بحث داخلي**: إمكانية البحث في محتوى الدليل
4. **إشعارات التحديثات**: تنبيه المستخدمين بالتحديثات الجديدة

---

**تم الإنجاز**: نظام دليل الاستخدام الشامل للمقيمين  
**التاريخ**: يوليو 2025  
**الحالة**: جاهز للاستخدام  
**الاختبار**: مطلوب اختبار شامل مع المستخدمين النهائيين

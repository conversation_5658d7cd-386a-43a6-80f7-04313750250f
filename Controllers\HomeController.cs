using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using RafoEvaluation.Models;
using Microsoft.AspNetCore.Authorization;
using RafoEvaluation.Data;
using Microsoft.EntityFrameworkCore;

namespace RafoEvaluation.Controllers;

[Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public IActionResult Index()
    {
        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    public IActionResult UserGuide()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }

    // API endpoint لجلب الإحصائيات
    [HttpGet]
    public async Task<IActionResult> GetStatistics()
    {
        try
        {
            _logger.LogInformation("جاري جلب الإحصائيات...");
            
            var totalCandidates = await _context.Candidates.CountAsync(c => c.IsActive);
            var completedEvaluations = await _context.CandidateEvaluations.CountAsync(ce => ce.Status == EvaluationStatus.Completed);
            var pendingEvaluations = await _context.CandidateEvaluations.CountAsync(ce => ce.Status != EvaluationStatus.Completed);
            var activeCommittees = await _context.Committees.CountAsync(c => c.Status == CommitteeStatus.Active);
            
            var statistics = new
            {
                totalCandidates = totalCandidates,
                completedEvaluations = completedEvaluations,
                pendingEvaluations = pendingEvaluations,
                activeCommittees = activeCommittees
            };

            _logger.LogInformation($"الإحصائيات: المرشحين={totalCandidates}, التقييمات المكتملة={completedEvaluations}, التقييمات المعلقة={pendingEvaluations}, اللجان النشطة={activeCommittees}");
            
            return Json(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب الإحصائيات");
            return Json(new { error = "خطأ في جلب الإحصائيات" });
        }
    }

    // API endpoint لجلب النشاطات الأخيرة
    [HttpGet]
    public async Task<IActionResult> GetRecentActivity()
    {
        try
        {
            var recentEvaluations = await _context.CandidateEvaluations
                .Include(ce => ce.Candidate)
                .Include(ce => ce.Committee)
                .OrderByDescending(ce => ce.CreatedAt)
                .Take(5)
                .Select(ce => new
                {
                    CandidateName = ce.Candidate.FullName,
                    CommitteeName = ce.Committee.CommitteeName,
                    Status = ce.Status == EvaluationStatus.Completed ? "مكتمل" : "قيد التقييم",
                    CreatedAt = ce.CreatedAt,
                    TimeAgo = GetTimeAgo(ce.CreatedAt)
                })
                .ToListAsync();

            var activeCommittees = await _context.Committees
                .Include(c => c.CommitteeMembers)
                .Include(c => c.CandidateCommitteeAssignments)
                .Where(c => c.Status == CommitteeStatus.Active)
                .Take(5)
                .Select(c => new
                {
                    CommitteeName = c.CommitteeName,
                    MemberCount = c.CommitteeMembers.Count(cm => cm.IsActive),
                    CandidateCount = c.CandidateCommitteeAssignments.Count(cca => cca.IsActive),
                    Status = "نشطة"
                })
                .ToListAsync();

            return Json(new
            {
                RecentEvaluations = recentEvaluations,
                ActiveCommittees = activeCommittees
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب النشاطات الأخيرة");
            return Json(new { error = "خطأ في جلب النشاطات الأخيرة" });
        }
    }

    private string GetTimeAgo(DateTime dateTime)
    {
        var timeSpan = DateTime.Now - dateTime;
        
        if (timeSpan.TotalDays >= 1)
        {
            var days = (int)timeSpan.TotalDays;
            return $"منذ {days} يوم";
        }
        else if (timeSpan.TotalHours >= 1)
        {
            var hours = (int)timeSpan.TotalHours;
            return $"منذ {hours} ساعة";
        }
        else if (timeSpan.TotalMinutes >= 1)
        {
            var minutes = (int)timeSpan.TotalMinutes;
            return $"منذ {minutes} دقيقة";
        }
        else
        {
            return "الآن";
        }
    }
}

using RafoEvaluation.Models.Auth;
using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    public class UserCreateViewModel
    {
        [Required(ErrorMessage = "رقم الخدمة مطلوب")]
        [Display(Name = "رقم الخدمة")]
        [StringLength(50, ErrorMessage = "رقم الخدمة لا يمكن أن يتجاوز 50 حرف")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [Display(Name = "الاسم الكامل")]
        [StringLength(200, ErrorMessage = "الاسم الكامل لا يمكن أن يتجاوز 200 حرف")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [Display(Name = "كلمة المرور")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيدها غير متطابقين")]
        [DataType(DataType.Password)]
        public string ConfirmPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرتبة مطلوبة")]
        [Display(Name = "الرتبة")]
        public int RankId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "الصلاحيات")]
        public List<int>? SelectedRoleIds { get; set; }

        // For dropdowns
        public List<Rank> AvailableRanks { get; set; } = new();
        public List<Role> AvailableRoles { get; set; } = new();
    }

    public class UserEditViewModel
    {
        public int UserId { get; set; }

        [Required(ErrorMessage = "رقم الخدمة مطلوب")]
        [Display(Name = "رقم الخدمة")]
        [StringLength(50, ErrorMessage = "رقم الخدمة لا يمكن أن يتجاوز 50 حرف")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [Display(Name = "الاسم الكامل")]
        [StringLength(200, ErrorMessage = "الاسم الكامل لا يمكن أن يتجاوز 200 حرف")]
        public string FullName { get; set; } = string.Empty;

        [Display(Name = "كلمة المرور الجديدة")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل")]
        [DataType(DataType.Password)]
        public string? Password { get; set; }

        [Display(Name = "تأكيد كلمة المرور الجديدة")]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيدها غير متطابقين")]
        [DataType(DataType.Password)]
        public string? ConfirmPassword { get; set; }

        [Required(ErrorMessage = "الرتبة مطلوبة")]
        [Display(Name = "الرتبة")]
        public int RankId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; }

        [Display(Name = "الصلاحيات")]
        public List<int>? SelectedRoleIds { get; set; }

        // For dropdowns
        public List<Rank> AvailableRanks { get; set; } = new();
        public List<Role> AvailableRoles { get; set; } = new();
    }

    public class UserDetailsViewModel
    {
        public int UserId { get; set; }
        public string ServiceNumber { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public List<string> RoleNames { get; set; } = new();
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    public class UserListItemViewModel
    {
        public int UserId { get; set; }
        public string ServiceNumber { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public List<string> RoleNames { get; set; } = new();
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    public class UserListViewModel
    {
        public List<UserListItemViewModel> Users { get; set; } = new();
        public string SearchTerm { get; set; } = string.Empty;
        public int? RankFilter { get; set; }
        public bool? ActiveFilter { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 10;

        // For filters
        public List<Rank> AvailableRanks { get; set; } = new();

        // Statistics
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
    }
} 
@model RafoEvaluation.ViewModels.CommitteeMemberDetailsViewModel
@{
    ViewData["Title"] = "تفاصيل عضو اللجنة";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-user breadcrumb-icon"></i>
                    تفاصيل عضو اللجنة
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="CommitteeMember">
                                <i class="fas fa-users-cog"></i> أعضاء اللجان
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-user"></i> تفاصيل العضو
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user ms-2"></i>تفاصيل العضو: @Model.UserName
                    </h3>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.CommitteeMemberId" class="btn btn-warning">
                            <i class="fas fa-edit ms-1"></i>تعديل
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-right ms-1"></i>رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">رقم العضو:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-secondary fs-6">@Model.MemberNumber</span>
                                </dd>

                                <dt class="col-sm-4">رقم الخدمة:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.ServiceNumber</strong>
                                </dd>

                                <dt class="col-sm-4">الاسم الكامل:</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.RankName) && !string.IsNullOrEmpty(Model.UserName))
                                    {
                                        <div>
                                            <span class="badge bg-primary me-2">@Model.RankName</span>
                                            <strong>@Model.UserName</strong>
                                        </div>
                                    }
                                    else if (!string.IsNullOrEmpty(Model.UserName))
                                    {
                                        <strong>@Model.UserName</strong>
                                    }
                                    else
                                    {
                                        <strong class="text-muted">@Model.ServiceNumber</strong>
                                    }
                                </dd>

                                <dt class="col-sm-4">اللجنة:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-info">@Model.CommitteeName</span>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">الدور:</dt>
                                <dd class="col-sm-8">
                                    @{
                                        var roleBadgeClass = Model.Role switch
                                        {
                                            CommitteeMemberRole.رئيس_اللجنة => "bg-danger",
                                            CommitteeMemberRole.عضو => "bg-primary",
                                            CommitteeMemberRole.منسق => "bg-success",
                                            _ => "bg-secondary"
                                        };
                                    }
                                    <span class="badge @roleBadgeClass">@Model.RoleDisplayName</span>
                                </dd>

                                <dt class="col-sm-4">قابلية التقييم:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.CanEvaluate)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>يمكنه التقييم
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-times me-1"></i>لا يمكنه التقييم
                                        </span>
                                    }
                                </dd>

                                <dt class="col-sm-4">الحالة:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غير نشط</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">تاريخ الإضافة:</dt>
                                <dd class="col-sm-8">
                                    <span class="text-muted">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                                </dd>

                                @if (Model.UpdatedAt.HasValue)
                                {
                                    <dt class="col-sm-4">آخر تحديث:</dt>
                                    <dd class="col-sm-8">
                                        <span class="text-muted">@Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                    </dd>
                                }
                            </dl>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <h5>ملاحظات:</h5>
                                <div class="alert alert-info">
                                    @Model.Notes
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div> 
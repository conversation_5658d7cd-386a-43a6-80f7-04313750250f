﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class UpdateCategoryWeights : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // تحديث أوزان الفئات حسب نوع الفئة
            migrationBuilder.Sql(@"
                UPDATE Categories 
                SET EvaluationWeight = 
                    CASE 
                        WHEN CategoryName LIKE '%طيار%' OR CategoryName LIKE '%Pilot%' THEN 40.00
                        WHEN CategoryName LIKE '%مهندس%' OR CategoryName LIKE '%Engineer%' THEN 35.00
                        WHEN CategoryName LIKE '%فني%' OR CategoryName LIKE '%Technician%' THEN 25.00
                        WHEN CategoryName LIKE '%إداري%' OR CategoryName LIKE '%Administrative%' THEN 20.00
                        WHEN CategoryName LIKE '%طبيب%' OR CategoryName LIKE '%Doctor%' THEN 45.00
                        WHEN CategoryName LIKE '%ممرض%' OR CategoryName LIKE '%Nurse%' THEN 30.00
                        WHEN CategoryName LIKE '%محامي%' OR CategoryName LIKE '%Lawyer%' THEN 35.00
                        WHEN CategoryName LIKE '%محاسب%' OR CategoryName LIKE '%Accountant%' THEN 25.00
                        WHEN CategoryName LIKE '%مترجم%' OR CategoryName LIKE '%Translator%' THEN 20.00
                        WHEN CategoryName LIKE '%مدرب%' OR CategoryName LIKE '%Trainer%' THEN 30.00
                        ELSE 30.00
                    END
            ");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 40, 3, 993, DateTimeKind.Utc).AddTicks(3823));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 40, 3, 993, DateTimeKind.Utc).AddTicks(4490));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 40, 3, 993, DateTimeKind.Utc).AddTicks(4492));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 40, 3, 993, DateTimeKind.Utc).AddTicks(4493));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 40, 3, 993, DateTimeKind.Utc).AddTicks(4494));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 40, 3, 993, DateTimeKind.Utc).AddTicks(4495));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // إعادة تعيين جميع الأوزان إلى 30%
            migrationBuilder.Sql(@"
                UPDATE Categories 
                SET EvaluationWeight = 30.00
            ");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(3161));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4416));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4420));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4421));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4422));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4424));
        }
    }
}

@{
    ViewData["Title"] = "إضافة مرشحين";
    var evaluationForm = ViewBag.EvaluationForm as RafoEvaluation.Models.EvaluationForm;
    var availableCandidates = ViewBag.AvailableCandidates as List<RafoEvaluation.Models.Candidate>;
    var existingCandidates = ViewBag.ExistingCandidates as List<RafoEvaluation.Models.CandidateEvaluation>;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة مرشحين - @(evaluationForm?.Title ?? "غير محدد")</h3>
                    <div class="card-tools">
                        <a asp-controller="CandidateEvaluation" asp-action="Index" asp-route-evaluationFormId="@(evaluationForm?.EvaluationFormId ?? 0)" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            @if (availableCandidates?.Any() == true)
                            {
                                <form asp-action="AddCandidates" method="post">
                                    <input type="hidden" name="evaluationFormId" value="@(evaluationForm?.EvaluationFormId ?? 0)" />
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="committeeId" class="form-label">اختر اللجنة المسؤولة عن التقييم:</label>
                                            <select name="committeeId" id="committeeId" class="form-select" required>
                                                <option value="">-- اختر اللجنة --</option>
                                                @foreach (var committee in ViewBag.AvailableCommittees)
                                                {
                                                    <option value="@committee.CommitteeId">@committee.CommitteeName</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <h5>المرشحين المتاحين</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th style="width: 50px;">
                                                        <input type="checkbox" id="selectAll" />
                                                    </th>
                                                    <th>الاسم</th>
                                                    <th>الرتبة</th>
                                                    <th>القاعدة الجوية</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for (int i = 0; i < availableCandidates.Count; i++)
                                                {
                                                    var candidate = availableCandidates[i];
                                                    <tr>
                                                        <td>
                                                            <input type="checkbox" name="candidateIds" value="@candidate.CandidateId" class="candidate-checkbox" />
                                                        </td>
                                                        <td>@candidate.FullName</td>
                                                        <td>@candidate.Rank?.RankName</td>
                                                        <td>@candidate.Airbase?.AirbaseName</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary" id="addSelectedBtn" disabled>
                                            <i class="fas fa-plus"></i> إضافة المرشحين المحددين
                                        </button>
                                    </div>
                                </form>
                            }
                            else
                            {
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    جميع المرشحين موجودين بالفعل في هذا النموذج.
                                </div>
                            }
                        </div>
                        
                        <div class="col-md-4">
                            <h5>المرشحين الموجودين</h5>
                            @if (existingCandidates?.Any() == true)
                            {
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>الاسم</th>
                                                <th>الحالة</th>
                                                <th>الإجراء</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var evaluation in existingCandidates)
                                            {
                                                <tr>
                                                    <td>@evaluation.Candidate.FullName</td>
                                                    <td>
                                                        @switch (evaluation.Status)
                                                        {
                                                            case RafoEvaluation.Models.EvaluationStatus.Pending:
                                                                <span class="badge badge-secondary">في الانتظار</span>
                                                                break;
                                                            case RafoEvaluation.Models.EvaluationStatus.InProgress:
                                                                <span class="badge badge-warning">قيد التقييم</span>
                                                                break;
                                                            case RafoEvaluation.Models.EvaluationStatus.Completed:
                                                                <span class="badge badge-success">مكتمل</span>
                                                                break;
                                                        }
                                                    </td>
                                                    <td>
                                                        <a asp-action="RemoveCandidate" asp-route-evaluationFormId="@(evaluationForm?.EvaluationFormId ?? 0)" asp-route-candidateId="@evaluation.CandidateId" 
                                                           class="btn btn-danger btn-sm" 
                                                           onclick="return confirm('هل أنت متأكد من إزالة هذا المرشح؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    لا يوجد مرشحين في هذا النموذج.
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Select all functionality
            $('#selectAll').change(function () {
                $('.candidate-checkbox').prop('checked', $(this).is(':checked'));
                updateAddButton();
            });

            // Individual checkbox change
            $(document).on('change', '.candidate-checkbox', function () {
                updateAddButton();
                
                // Update select all checkbox
                var totalCheckboxes = $('.candidate-checkbox').length;
                var checkedCheckboxes = $('.candidate-checkbox:checked').length;
                
                if (checkedCheckboxes === 0) {
                    $('#selectAll').prop('indeterminate', false).prop('checked', false);
                } else if (checkedCheckboxes === totalCheckboxes) {
                    $('#selectAll').prop('indeterminate', false).prop('checked', true);
                } else {
                    $('#selectAll').prop('indeterminate', true);
                }
            });

            function updateAddButton() {
                var checkedCount = $('.candidate-checkbox:checked').length;
                var addBtn = $('#addSelectedBtn');
                
                if (checkedCount > 0) {
                    addBtn.prop('disabled', false);
                    addBtn.html('<i class="fas fa-plus"></i> إضافة المرشحين المحددين (' + checkedCount + ')');
                } else {
                    addBtn.prop('disabled', true);
                    addBtn.html('<i class="fas fa-plus"></i> إضافة المرشحين المحددين');
                }
            }

            // Form validation
            $('form').submit(function (e) {
                var checkedCount = $('.candidate-checkbox:checked').length;
                if (checkedCount === 0) {
                    alert('يرجى تحديد مرشح واحد على الأقل');
                    e.preventDefault();
                    return false;
                }
            });
        });
    </script>
} 
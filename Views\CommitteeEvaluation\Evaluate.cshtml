@model RafoEvaluation.ViewModels.CandidateEvaluationViewModel

@{
    ViewData["Title"] = "تقييم المرشح";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-dark">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-clipboard-check me-2"></i>
                                تقييم المرشح: @Model.CandidateName
                            </h3>
                            <small class="text-dark">@Model.ServiceNumber - @Model.RankName</small>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a asp-controller="CommitteeEvaluation" asp-action="EvaluationSession" 
                               asp-route-committeeId="@ViewBag.CommitteeId" 
                               asp-route-evaluationFormId="@Model.EvaluationFormId" 
                               class="btn btn-outline-dark btn-sm">
                                <i class="fas fa-arrow-right"></i> رجوع
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-user fa-2x text-primary mb-2"></i>
                                <h6>المرشح</h6>
                                <strong>@Model.CandidateName</strong>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-building fa-2x text-info mb-2"></i>
                                <h6>القاعدة الجوية</h6>
                                <strong>@Model.AirbaseName</strong>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-clipboard-list fa-2x text-success mb-2"></i>
                                <h6>نموذج التقييم</h6>
                                <strong>@Model.EvaluationFormTitle</strong>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-chart-bar fa-2x text-warning mb-2"></i>
                                <h6>الدرجة الحالية</h6>
                                @if (Model.TotalScore.HasValue)
                                {
                                    <strong class="text-success">@Model.TotalScore.Value.ToString("F1")</strong>
                                }
                                else
                                {
                                    <strong class="text-muted">-</strong>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- نموذج التقييم المبسط -->
                    <form id="evaluationForm" method="post" asp-action="SubmitEvaluation">
                        <input type="hidden" name="evaluationId" value="@Model.CandidateEvaluationId" />
                        
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-star me-2"></i>
                                    معايير التقييم - أدخل الدرجات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th style="width: 50px;">#</th>
                                                <th>المعيار</th>
                                                <th style="width: 150px;">الدرجة القصوى</th>
                                                <th style="width: 200px;">الدرجة الممنوحة</th>
                                                <th style="width: 100px;">النسبة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (int i = 0; i < Model.Results.Count; i++)
                                            {
                                                var result = Model.Results[i];
                                                <tr class="evaluation-row">
                                                    <td class="text-center">
                                                        <span class="badge bg-primary">@(i + 1)</span>
                                                    </td>
                                                    <td>
                                                        <strong>@result.Criteria</strong>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-secondary">@result.MaxScore</span>
                                                    </td>
                                                    <td>
                                                        <div class="input-group input-group-sm">
                                                            <input type="number" 
                                                                   name="scores[@i].Score" 
                                                                   class="form-control score-input" 
                                                                   min="0" 
                                                                   max="@result.MaxScore" 
                                                                   step="0.1" 
                                                                   placeholder="0"
                                                                   value="0"
                                                                   data-max-score="@result.MaxScore"
                                                                   data-index="@i"
                                                                   required>
                                                            <span class="input-group-text">/ @result.MaxScore</span>
                                                        </div>
                                                        <div class="mt-1">
                                                            <button type="button" class="btn btn-sm btn-outline-primary quick-score" 
                                                                    data-target="@i" data-score="@(result.MaxScore * 0.8)">80%</button>
                                                            <button type="button" class="btn btn-sm btn-outline-success quick-score" 
                                                                    data-target="@i" data-score="@(result.MaxScore * 0.9)">90%</button>
                                                            <button type="button" class="btn btn-sm btn-outline-warning quick-score" 
                                                                    data-target="@i" data-score="@result.MaxScore">100%</button>
                                                        </div>
                                                        <input type="hidden" name="scores[@i].Criteria" value="@result.Criteria" />
                                                        <input type="hidden" name="scores[@i].MaxScore" value="@result.MaxScore" />
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="percentage-display" data-index="@i">0%</span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                        <tfoot class="table-info">
                                            <tr>
                                                <td colspan="2" class="text-end">
                                                    <strong>المجموع:</strong>
                                                </td>
                                                <td class="text-center">
                                                    <strong id="totalMaxScore">@Model.Results.Sum(r => r.MaxScore)</strong>
                                                </td>
                                                <td class="text-center">
                                                    <strong id="totalScore" class="text-success">0</strong>
                                                </td>
                                                <td class="text-center">
                                                    <strong id="totalPercentage" class="text-success">0%</strong>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                <!-- ملاحظات عامة -->
                                <div class="form-group mt-4">
                                    <label for="generalNotes" class="form-label fw-bold">
                                        <i class="fas fa-comment me-2"></i>
                                        ملاحظات عامة
                                    </label>
                                    <textarea name="generalNotes" id="generalNotes" class="form-control" rows="3" 
                                              placeholder="أدخل أي ملاحظات إضافية حول التقييم...">@Model.GeneralNotes</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" id="saveDraftBtn" class="btn btn-warning btn-lg">
                                        <i class="fas fa-save me-2"></i> حفظ مسودة
                                    </button>
                                    <button type="submit" id="submitBtn" class="btn btn-success btn-lg">
                                        <i class="fas fa-check me-2"></i> إرسال التقييم
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الحالة -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                                    <ul class="mb-0">
                                        <li><strong>حفظ مسودة:</strong> يحفظ التقييم كمسودة ويغير الحالة إلى "قيد التقييم" - يمكنك العودة لاحقاً لاستكمال التقييم</li>
                                        <li><strong>إرسال التقييم:</strong> ينهي التقييم نهائياً ويغير الحالة إلى "مكتمل" - سيتم توجيهك إلى صفحة درجات المرشحين</li>
                                        <li><strong>بعد الإرسال:</strong> ستتمكن من رؤية جميع درجات المرشحين في صفحة "درجات المرشحين"</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .evaluation-row:hover {
        background-color: #f8f9fa;
    }
    
    .score-input:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .percentage-display {
        font-weight: bold;
        color: #28a745;
    }
    
    .table th {
        background-color: #343a40;
        color: white;
        font-weight: 600;
    }
    
    .table tfoot {
        background-color: #e3f2fd;
    }
</style>

@section Scripts {
    <script>
        $(document).ready(function() {
            // حساب الدرجات تلقائياً
            $('.score-input').on('input', function() {
                calculateScores();
            });

            // الأزرار السريعة للدرجات
            $('.quick-score').on('click', function() {
                const targetIndex = $(this).data('target');
                const score = $(this).data('score');
                $(`.score-input[data-index="${targetIndex}"]`).val(score.toFixed(1));
                calculateScores();
            });

            // حفظ المسودة
            $('#saveDraftBtn').on('click', function() {
                saveDraft();
            });

            // التحقق من صحة النموذج
            $('#evaluationForm').on('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    alert('يرجى التأكد من إدخال جميع الدرجات المطلوبة');
                }
            });

            // الحساب الأولي
            calculateScores();
        });

        function calculateScores() {
            let totalScore = 0;
            let totalMaxScore = 0;

            $('.score-input').each(function() {
                const score = parseFloat($(this).val()) || 0;
                const maxScore = parseFloat($(this).data('max-score'));
                const index = $(this).data('index');

                totalScore += score;
                totalMaxScore += maxScore;

                // حساب النسبة لكل معيار
                const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
                $(`.percentage-display[data-index="${index}"]`).text(percentage + '%');
            });

            const totalPercentage = totalMaxScore > 0 ? Math.round((totalScore / totalMaxScore) * 100) : 0;

            $('#totalScore').text(totalScore.toFixed(1));
            $('#totalPercentage').text(totalPercentage + '%');
        }

        function validateForm() {
            let isValid = true;
            $('.score-input').each(function() {
                const score = parseFloat($(this).val());
                const maxScore = parseFloat($(this).data('max-score'));
                
                if (isNaN(score) || score < 0 || score > maxScore) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            return isValid;
        }

        function saveDraft() {
            const formData = new FormData($('#evaluationForm')[0]);
            formData.append('evaluationId', @Model.CandidateEvaluationId);

            $.ajax({
                url: '@Url.Action("SaveDraft", "CommitteeEvaluation")',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        alert('تم حفظ المسودة بنجاح');
                    } else {
                        alert('حدث خطأ أثناء حفظ المسودة: ' + response.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ في الاتصال بالخادم');
                }
            });
        }
    </script>
} 
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly ApplicationDbContext _context;

        public AuthenticationService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<bool> ValidateUserAsync(string serviceNumber, string password)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.ServiceNumber == serviceNumber && u.IsActive);

            if (user == null)
                return false;

            return BCrypt.Net.BCrypt.Verify(password, user.Password);
        }

        public async Task<User?> GetUserByServiceNumberAsync(string serviceNumber)
        {
            return await _context.Users
                .Include(u => u.Rank)
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.ServiceNumber == serviceNumber && u.IsActive);
        }

        public async Task<List<string>> GetUserRolesAsync(int userId)
        {
            return await _context.UserRoles
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role.RoleName)
                .ToListAsync();
        }

        public async Task<bool> IsUserInCommitteeAsync(string serviceNumber, int committeeId)
        {
            return await _context.CommitteeMembers
                .AnyAsync(cm => cm.ServiceNumber == serviceNumber && 
                               cm.CommitteeId == committeeId && 
                               cm.IsActive);
        }

        public async Task<bool> CanUserEvaluateInCommitteeAsync(string serviceNumber, int committeeId)
        {
            var member = await _context.CommitteeMembers
                .FirstOrDefaultAsync(cm => cm.ServiceNumber == serviceNumber && 
                                         cm.CommitteeId == committeeId && 
                                         cm.IsActive);
            
            if (member == null)
                return false;

            // رئيس اللجنة وأعضاء اللجنة يمكنهم التقييم افتراضياً
            if (member.Role == CommitteeMemberRole.رئيس_اللجنة || member.Role == CommitteeMemberRole.عضو)
                return true;

            // المنسقون يمكنهم التقييم فقط إذا كان CanEvaluate = true
            if (member.Role == CommitteeMemberRole.منسق)
                return member.CanEvaluate;

            return false;
        }

        public async Task<bool> IsUserAdminAsync(int userId)
        {
            return await _context.UserRoles
                .AnyAsync(ur => ur.UserId == userId && 
                               ur.Role.RoleName == "Admin");
        }

        public async Task UpdateLastLoginAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.LastLoginAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }
    }
}

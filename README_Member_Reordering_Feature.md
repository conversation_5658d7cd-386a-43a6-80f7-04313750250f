# ميزة إعادة ترتيب أعضاء اللجان

## نظرة عامة

تم إضافة ميزة جديدة تسمح بإعادة ترتيب أعضاء اللجان يدوياً، مما يوفر مرونة أكبر في تحديد ترتيب المقيمين في التقارير والتقييمات.

## الميزات الجديدة

### 1. **إعادة الترتيب اليدوي**
- إمكانية تغيير ترتيب الأعضاء عبر واجهة سحب وإفلات سهلة الاستخدام
- حفظ الترتيب الجديد في قاعدة البيانات
- عرض الترتيب الجديد في جميع التقارير والتقييمات

### 2. **الترتيب التلقائي (المطبق سابقاً)**
- **رئيس اللجنة**: دائماً العضو الأول (MemberNumber = 1)
- **أعضاء اللجنة**: بالترتيب حسب تاريخ الإضافة
- **المنسقون**: في النهاية

## كيفية الاستخدام

### الوصول إلى ميزة إعادة الترتيب

1. **من صفحة أعضاء اللجان**:
   - اذهب إلى `أعضاء اللجان`
   - اضغط على زر `إعادة ترتيب الأعضاء` في شريط الأدوات
   - اختر اللجنة من القائمة المنسدلة
   - اضغط `فتح صفحة الترتيب`

2. **الرابط المباشر**:
   - `/CommitteeMember/Reorder/{committeeId}`

### خطوات إعادة الترتيب

1. **اختيار اللجنة**: اختر اللجنة التي تريد إعادة ترتيب أعضائها
2. **عرض الأعضاء**: ستظهر قائمة بجميع أعضاء اللجنة النشطين
3. **السحب والإفلات**: اسحب الأعضاء لتغيير ترتيبهم
4. **حفظ الترتيب**: اضغط `حفظ الترتيب الجديد`
5. **تأكيد الحفظ**: سيتم حفظ الترتيب الجديد في قاعدة البيانات

## الواجهة الجديدة

### صفحة إعادة الترتيب (`/Views/CommitteeMember/Reorder.cshtml`)

#### الميزات:
- **قائمة قابلة للسحب**: عرض الأعضاء في قائمة يمكن إعادة ترتيبها
- **معلومات شاملة**: عرض اسم العضو، الرتبة، الدور، وصلاحيات التقييم
- **ألوان مميزة**: ألوان مختلفة لكل دور (رئيس اللجنة، عضو، منسق)
- **إحصائيات سريعة**: عرض عدد الأعضاء وعدد المقيمين
- **أزرار التحكم**: حفظ، إعادة تعيين، إلغاء

#### العناصر المعروضة:
- **اسم العضو**: الاسم الكامل مع الرتبة
- **الدور**: رئيس اللجنة، عضو، منسق
- **صلاحيات التقييم**: يمكنه التقييم / لا يمكنه التقييم
- **رقم العضو**: الترتيب الحالي

## التحكم في الصلاحيات

### من يمكنه إعادة الترتيب:
- **المديرون (Admin)**: يمكنهم إعادة ترتيب أي لجنة
- **أعضاء اللجنة**: يمكنهم إعادة ترتيب اللجان التي ينتمون إليها فقط

### التحقق من الصلاحيات:
```csharp
// التحقق من الصلاحيات في Controller
var serviceNumber = User.FindFirst("ServiceNumber")?.Value;
var isAdmin = User.IsInRole("Admin");

if (!isAdmin && !string.IsNullOrEmpty(serviceNumber))
{
    var isInCommittee = await _authService.IsUserInCommitteeAsync(serviceNumber, committeeId);
    if (!isInCommittee)
    {
        return Json(new { success = false, message = "ليس لديك صلاحية لتعديل ترتيب أعضاء هذه اللجنة" });
    }
}
```

## API الجديدة

### 1. **GET: CommitteeMember/Reorder/{committeeId}**
- **الوصف**: عرض صفحة إعادة ترتيب أعضاء لجنة معينة
- **المعاملات**: `committeeId` - معرف اللجنة
- **الاستجابة**: صفحة HTML مع قائمة الأعضاء

### 2. **POST: CommitteeMember/ReorderMembers**
- **الوصف**: حفظ الترتيب الجديد للأعضاء
- **المعاملات**: 
  - `committeeId` - معرف اللجنة
  - `memberIds` - قائمة معرفات الأعضاء بالترتيب الجديد
- **الاستجابة**: JSON مع رسالة نجاح أو فشل

## التحديثات في قاعدة البيانات

### جدول CommitteeMembers:
- **MemberNumber**: يتم تحديثه عند إعادة الترتيب
- **UpdatedAt**: يتم تحديثه عند إعادة الترتيب

### مثال على التحديث:
```sql
-- تحديث ترتيب الأعضاء
UPDATE CommitteeMembers 
SET MemberNumber = @newOrder, UpdatedAt = @timestamp
WHERE CommitteeMemberId = @memberId
```

## التأثير على النظام

### 1. **التقارير**:
- جميع التقارير تستخدم الترتيب الجديد
- ترتيب المقيمين في التقارير التفصيلية
- ترتيب الأعضاء في تقارير اللجان

### 2. **التقييمات**:
- ترتيب المقيمين في نماذج التقييم
- ترتيب التقييمات الفردية في النتائج

### 3. **عرض البيانات**:
- ترتيب الأعضاء في قوائم اللجان
- ترتيب المقيمين في صفحات التقييم

## الأمان والتحقق

### التحقق من صحة البيانات:
1. **وجود الأعضاء**: التأكد من أن جميع الأعضاء موجودين في اللجنة
2. **عدم التكرار**: التأكد من عدم تكرار معرفات الأعضاء
3. **الصلاحيات**: التحقق من صلاحيات المستخدم

### معالجة الأخطاء:
- رسائل خطأ واضحة للمستخدم
- تسجيل الأخطاء في السجلات
- حماية من الهجمات الأمنية

## الملفات المضافة/المحدثة

### Controllers:
- `CommitteeMemberController.cs` - إضافة دوال إعادة الترتيب

### ViewModels:
- `CommitteeMemberViewModels.cs` - إضافة نماذج إعادة الترتيب

### Views:
- `Views/CommitteeMember/Reorder.cshtml` - صفحة إعادة الترتيب
- `Views/CommitteeMember/Index.cshtml` - إضافة رابط إعادة الترتيب

### Services:
- `CommitteeMemberOrderService.cs` - تحسين منطق الترتيب

## الاستخدام الأمثل

### نصائح لإعادة الترتيب:
1. **رئيس اللجنة**: يفضل أن يبقى في المقدمة
2. **الأعضاء المهمون**: ضعهم في بداية القائمة
3. **المنسقون**: يمكن وضعهم في النهاية
4. **الترتيب المنطقي**: رتب الأعضاء حسب الأهمية أو الخبرة

### أفضل الممارسات:
- إعادة الترتيب قبل بدء التقييمات
- إبلاغ الأعضاء بالتغييرات
- توثيق أسباب إعادة الترتيب
- مراجعة الترتيب بانتظام

## الدعم والمساعدة

### في حالة المشاكل:
1. **ترتيب لا يتم حفظه**: تحقق من الصلاحيات
2. **أعضاء لا يظهرون**: تحقق من حالة العضو (نشط/غير نشط)
3. **أخطاء في الواجهة**: تحقق من JavaScript في المتصفح

### للتواصل:
- استخدم نظام التقارير للإبلاغ عن المشاكل
- راجع السجلات للحصول على تفاصيل الأخطاء
- تواصل مع فريق الدعم الفني

---

**ملاحظة**: هذه الميزة متوافقة مع جميع المتصفحات الحديثة وتدعم الأجهزة اللوحية والهواتف الذكية. 
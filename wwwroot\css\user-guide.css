/* دليل الاستخدام - User Guide Styles */

.user-guide-container {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.guide-step {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
    transition: transform 0.2s ease-in-out;
}

.guide-step:hover {
    transform: translateX(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.guide-step-number {
    width: 30px;
    height: 30px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-left: 10px;
}

.guide-screenshot {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.guide-screenshot:hover {
    transform: scale(1.05);
    cursor: pointer;
}

.guide-feature-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.2s ease-in-out;
}

.guide-feature-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.guide-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.guide-tabs .nav-link {
    color: #495057;
    font-weight: 500;
    border: none;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease-in-out;
}

.guide-tabs .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: none;
}

.guide-tabs .nav-link:hover {
    color: #007bff;
    border-bottom-color: #007bff;
}

.process-step {
    position: relative;
    padding: 2rem;
    margin-bottom: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.process-step::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #28a745, #20c997);
    border-radius: 2px;
}

.process-number {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.tip-box {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: none;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.warning-box {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: none;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.success-box {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: none;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.info-box {
    background: linear-gradient(135deg, #d1ecf1 0%, #b8daff 100%);
    border: none;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
}

/* Animation for guide elements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.guide-animate {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .guide-step {
        padding: 1rem;
    }
    
    .process-step {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .guide-icon {
        font-size: 2rem;
    }
    
    .process-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* Print styles for guide */
@media print {
    .guide-tabs,
    .nav-tabs {
        display: none;
    }
    
    .tab-content {
        display: block !important;
    }
    
    .tab-pane {
        display: block !important;
        opacity: 1 !important;
    }
    
    .guide-step,
    .process-step {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* RTL Support */
[dir="rtl"] .guide-step {
    border-left: none;
    border-right: 4px solid #007bff;
}

[dir="rtl"] .guide-step:hover {
    transform: translateX(5px);
}

[dir="rtl"] .guide-step-number {
    margin-left: 0;
    margin-right: 10px;
}

[dir="rtl"] .process-step::before {
    right: auto;
    left: 0;
}

[dir="rtl"] .process-number {
    right: auto;
    left: -10px;
}

@model RafoEvaluation.ViewModels.AirbaseEditViewModel
@{
    ViewData["Title"] = "Edit Airbase";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Edit Airbase
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="AirbaseId" type="hidden" />
                        
                        <div class="mb-3">
                            <label asp-for="AirbaseName" class="form-label"></label>
                            <input asp-for="AirbaseName" class="form-control" placeholder="Enter airbase name" />
                            <span asp-validation-for="AirbaseName" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a asp-action="Index" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>Update Airbase
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}

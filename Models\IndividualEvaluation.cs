using System.ComponentModel.DataAnnotations;
using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Models
{
    public class IndividualEvaluation
    {
        [Key]
        public int IndividualEvaluationId { get; set; }

        [Required]
        public int CandidateEvaluationId { get; set; }

        [Required]
        public int EvaluatorId { get; set; }

        [Display(Name = "الدرجة الإجمالية")]
        [Range(0, 100, ErrorMessage = "الدرجة الإجمالية يجب أن تكون بين 0 و 100")]
        public decimal TotalScore { get; set; }

        [Display(Name = "ملاحظات المقيم")]
        [StringLength(2000, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 2000 حرف")]
        public string? EvaluatorNotes { get; set; }

        [Display(Name = "حضر التقييم")]
        public bool IsPresent { get; set; } = true;

        [Display(Name = "تاريخ التقييم")]
        public DateTime EvaluatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual CandidateEvaluation CandidateEvaluation { get; set; } = null!;
        public virtual User Evaluator { get; set; } = null!;
        public virtual ICollection<IndividualEvaluationCriteria> CriteriaScores { get; set; } = new List<IndividualEvaluationCriteria>();
    }
} 
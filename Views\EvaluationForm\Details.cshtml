@model RafoEvaluation.ViewModels.EvaluationFormDetailsViewModel

@{
    ViewData["Title"] = "تفاصيل نموذج التقييم";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تفاصيل نموذج التقييم</h3>
                    <div class="card-tools">
                        <a asp-action="Edit" asp-route-id="@Model.EvaluationFormId" class="btn btn-warning">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a asp-controller="CandidateEvaluation" asp-action="Index" asp-route-evaluationFormId="@Model.EvaluationFormId" class="btn btn-primary">
                            <i class="fas fa-users"></i> إدارة المرشحين
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>معلومات النموذج</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>العنوان:</strong></td>
                                    <td>@Model.Title</td>
                                </tr>
                                <tr>
                                    <td><strong>الوصف:</strong></td>
                                    <td>@(string.IsNullOrEmpty(Model.Description) ? "-" : Model.Description)</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">غير نشط</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>إحصائيات</h5>
                            <div class="row">
                                <div class="col-6">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4>@Model.ItemCount</h4>
                                            <p class="mb-0">عدد المعايير</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4>@Model.TotalEvaluations</h4>
                                            <p class="mb-0">إجمالي التقييمات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4>@Model.InProgressEvaluations</h4>
                                            <p class="mb-0">قيد التقييم</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4>@Model.CompletedEvaluations</h4>
                                            <p class="mb-0">مكتمل</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr />

                    <h5>معايير التقييم</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>المعيار</th>
                                    <th>الوصف</th>
                                    <th>الدرجة القصوى</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.Items.Count; i++)
                                {
                                    var item = Model.Items.ElementAt(i);
                                    <tr>
                                        <td>@(i + 1)</td>
                                        <td>@item.Criteria</td>
                                        <td>@(string.IsNullOrEmpty(item.Description) ? "-" : item.Description)</td>
                                        <td>@item.MaxScore</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .info-box {
        display: block;
        min-height: 80px;
        background: #fff;
        width: 100%;
        box-shadow: 0 1px 1px rgba(0,0,0,0.1);
        border-radius: 2px;
        margin-bottom: 15px;
    }
    .info-box-icon {
        border-radius: 2px 0 0 2px;
        display: block;
        float: left;
        height: 80px;
        width: 80px;
        text-align: center;
        font-size: 40px;
        line-height: 80px;
        background: rgba(0,0,0,0.2);
    }
    .info-box-content {
        padding: 5px 10px;
        margin-left: 80px;
    }
    .info-box-text {
        display: block;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .info-box-number {
        display: block;
        font-weight: bold;
        font-size: 18px;
    }
</style> 
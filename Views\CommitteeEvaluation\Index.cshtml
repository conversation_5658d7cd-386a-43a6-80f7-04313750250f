@model List<RafoEvaluation.ViewModels.CommitteeEvaluationViewModel>
@{
    ViewData["Title"] = "تقييم المرشحين";
    var isAdmin = User.IsInRole("Admin");
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-clipboard-check breadcrumb-icon"></i>
                    تقييم المرشحين
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-clipboard-check"></i> تقييم المرشحين
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-dark">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        الإجراءات السريعة
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="quick-action-card text-center p-3 border rounded">
                                <i class="fas fa-plus-circle fa-3x text-success mb-3"></i>
                                <h5>إنشاء تقييم جديد</h5>
                                <p class="text-muted">إنشاء تقييم فردي لمرشح واحد</p>
                                <a asp-controller="CandidateEvaluation" asp-action="Create" class="btn btn-success">
                                    <i class="fas fa-plus me-1"></i>إنشاء تقييم
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="quick-action-card text-center p-3 border rounded">
                                <i class="fas fa-users fa-3x text-info mb-3"></i>
                                <h5>تقييم جماعي</h5>
                                <p class="text-muted">تقييم مجموعة من المرشحين في لجنة واحدة</p>
                                <a href="#committees-section" class="btn btn-info">
                                    <i class="fas fa-users me-1"></i>اختيار لجنة
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="quick-action-card text-center p-3 border rounded">
                                <i class="fas fa-chart-bar fa-3x text-warning mb-3"></i>
                                <h5>عرض النتائج</h5>
                                <p class="text-muted">عرض وطباعة نتائج التقييمات</p>
                                <a asp-controller="CandidateEvaluation" asp-action="Index" class="btn btn-warning">
                                    <i class="fas fa-chart-bar me-1"></i>عرض النتائج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Committees Section -->
<div class="container-fluid" id="committees-section">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users-cog me-2"></i>
                        اللجان المتاحة للتقييم
                    </h3>
                    <div class="card-tools">
                        @if (isAdmin)
                        {
                            <a asp-controller="Committee" asp-action="Create" class="btn btn-success btn-sm">
                                <i class="fas fa-plus"></i> إنشاء لجنة جديدة
                            </a>
                        }
                    </div>
                </div>
                <div class="card-body">
                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد لجان متاحة</h4>
                            <p class="text-muted">لا توجد لجان نشطة أو أنت لست عضواً في أي لجنة</p>
                            @if (isAdmin)
                            {
                                <a asp-controller="Committee" asp-action="Create" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>إنشاء لجنة جديدة
                                </a>
                            }
                            else
                            {
                                <p class="text-muted">يرجى التواصل مع مدير النظام لإضافتك إلى لجنة</p>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="row">
                            @foreach (var committee in Model)
                            {
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="committee-card card h-100 border-0 shadow-sm">
                                        <div class="card-header bg-gradient-primary text-dark">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="card-title mb-0">
                                                    <i class="fas fa-users me-2"></i>
                                                    @committee.CommitteeName
                                                </h5>
                                                <span class="badge bg-light text-dark">
                                                    @committee.ActiveMemberCount عضو
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            @if (!string.IsNullOrEmpty(committee.Description))
                                            {
                                                <p class="text-muted mb-3">@committee.Description</p>
                                            }
                                            
                                            <!-- Committee Members -->
                                            <div class="mb-3">
                                                <h6 class="text-primary">
                                                    <i class="fas fa-user-friends me-1"></i>
                                                    أعضاء اللجنة
                                                </h6>
                                                <div class="committee-members">
                                                    @foreach (var member in committee.Members.Take(3))
                                                    {
                                                        <span class="badge bg-light text-dark me-1 mb-1">
                                                            @member.RankName @member.UserName (@member.ServiceNumber)
                                                        </span>
                                                    }
                                                    @if (committee.Members.Count > 3)
                                                    {
                                                        <span class="badge bg-secondary">+@(committee.Members.Count - 3) آخرين</span>
                                                    }
                                                </div>
                                            </div>
                                            
                                            <!-- Action Buttons -->
                                            <div class="d-grid gap-2">
                                                <a asp-action="SelectCandidates" asp-route-committeeId="@committee.CommitteeId" 
                                                   class="btn btn-primary">
                                                    <i class="fas fa-clipboard-check me-1"></i>
                                                    بدء التقييم
                                                </a>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a asp-action="EvaluationSession" asp-route-committeeId="@committee.CommitteeId" 
                                                       class="btn btn-outline-info">
                                                        <i class="fas fa-play me-1"></i>جلسات التقييم
                                                    </a>
                                                    <a asp-action="ViewResults" asp-route-committeeId="@committee.CommitteeId" 
                                                       class="btn btn-outline-success">
                                                        <i class="fas fa-chart-bar me-1"></i>النتائج
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Section -->
@if (Model.Any())
{
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card border-info">
                    <div class="card-header bg-info text-dark">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            إحصائيات سريعة
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h3 class="text-primary">@Model.Count</h3>
                                    <p class="text-muted">إجمالي اللجان</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <i class="fas fa-user-friends fa-2x text-success mb-2"></i>
                                    <h3 class="text-success">@Model.Sum(c => c.ActiveMemberCount)</h3>
                                    <p class="text-muted">إجمالي الأعضاء النشطين</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <i class="fas fa-clipboard-check fa-2x text-warning mb-2"></i>
                                    <h3 class="text-warning">@Model.Count</h3>
                                    <p class="text-muted">لجان جاهزة للتقييم</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                    <h3 class="text-info">100%</h3>
                                    <p class="text-muted">جاهزية النظام</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .quick-action-card {
        transition: transform 0.2s ease-in-out;
        height: 100%;
    }
    
    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .committee-card {
        transition: transform 0.2s ease-in-out;
    }
    
    .committee-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
    }
    
    .committee-members {
        max-height: 80px;
        overflow-y: auto;
    }
    
    .stat-card {
        padding: 1rem;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }
    
    .card-header {
        border-bottom: none;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
    }
    
    .btn-group .btn:first-child {
        border-top-left-radius: 0.375rem !important;
        border-bottom-left-radius: 0.375rem !important;
    }
    
    .btn-group .btn:last-child {
        border-top-right-radius: 0.375rem !important;
        border-bottom-right-radius: 0.375rem !important;
    }
</style> 
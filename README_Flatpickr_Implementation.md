# Flatpickr Date Picker Implementation

## Overview
This document describes the implementation of Flatpickr date picker for the RafoEvaluation system, specifically for the date of birth field in candidate forms.

## Features Implemented

### 1. **Offline Library Installation**
- Flatpickr library files are stored locally in `wwwroot/lib/flatpickr/`
- No external CDN dependencies required
- Files included:
  - `flatpickr.min.js` - Main JavaScript library
  - `flatpickr.min.css` - Main CSS styles
  - `ar.js` - Arabic locale file

### 2. **RTL (Right-to-Left) Support**
- Full Arabic language support
- RTL calendar layout
- Arabic month and day names
- Proper text direction for Arabic interface

### 3. **Enhanced User Experience**
- **Searchable**: Users can type dates directly or use the calendar
- **Age Calculation**: Automatically calculates and displays age when date is selected
- **Age Validation**: Ensures minimum age of 18 years
- **Clear Button**: Easy way to clear the selected date
- **Mobile Friendly**: Works well on mobile devices
- **Visual Feedback**: Age display shows below the date field

### 4. **Custom Styling**
- Matches the system's color scheme (#161D6F primary color)
- Consistent with Bootstrap styling
- Responsive design
- Custom RTL arrow adjustments

## Implementation Details

### Files Modified

1. **Views/Shared/_Layout.cshtml**
   - Added Flatpickr CSS and JS files
   - Added custom CSS for RTL support and styling

2. **Views/Candidate/Create.cshtml**
   - Replaced HTML5 date input with Flatpickr
   - Added age calculation and validation
   - Enhanced user experience features

3. **Views/Candidate/Edit.cshtml**
   - Same implementation as Create view
   - Added support for existing date values
   - Age display for pre-filled dates

### Configuration Options

```javascript
flatpickr("#dateOfBirth", {
    locale: "ar",                    // Arabic locale
    dateFormat: "Y-m-d",            // ISO date format
    maxDate: new Date().fp_incr(-18), // Minimum age 18
    minDate: new Date().fp_incr(-100), // Maximum age 100
    allowInput: true,                // Allow typing
    clickOpens: true,                // Click to open
    clearButton: true,               // Show clear button
    disableMobile: false,            // Enable on mobile
    time_24hr: false,                // 12-hour format
    weekNumbers: false               // Hide week numbers
});
```

### Features

#### Age Calculation
- Automatically calculates age when date is selected
- Displays age in Arabic: "العمر: XX سنة"
- Updates in real-time as user changes date

#### Validation
- Minimum age validation (18 years)
- Maximum age validation (100 years)
- Clear error messages in Arabic
- Automatic field clearing for invalid dates

#### Accessibility
- Keyboard navigation support
- Screen reader friendly
- High contrast colors
- Clear visual indicators

## Usage

### For Users
1. Click on the date of birth field
2. Either:
   - Type the date directly (YYYY-MM-DD format)
   - Use the calendar to select a date
3. Age will be automatically calculated and displayed
4. Use the clear button (×) to reset the field

### For Developers
To add Flatpickr to other date fields:

1. Add the input with `readonly` attribute:
```html
<input type="text" id="myDateField" class="form-control" readonly />
```

2. Initialize Flatpickr:
```javascript
flatpickr("#myDateField", {
    locale: "ar",
    dateFormat: "Y-m-d",
    // ... other options
});
```

## Browser Support
- Chrome 55+
- Firefox 52+
- Safari 10+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance
- Lightweight library (~50KB minified)
- Fast initialization
- Minimal impact on page load time
- Efficient memory usage

## Future Enhancements
- Add time picker support for other fields
- Implement date range picker
- Add more validation rules
- Enhance mobile experience
- Add keyboard shortcuts

## Troubleshooting

### Common Issues
1. **Calendar not opening**: Check if Flatpickr JS is loaded
2. **RTL not working**: Ensure Arabic locale file is loaded
3. **Styling issues**: Check CSS conflicts with Bootstrap
4. **Age calculation wrong**: Verify date format consistency

### Debug Mode
To enable debug mode, add this before Flatpickr initialization:
```javascript
flatpickr.localize(require("flatpickr/dist/l10n/ar.js"));
```

## Dependencies
- jQuery (already included in the project)
- Bootstrap 5 (already included)
- No additional dependencies required

## License
Flatpickr is licensed under MIT License. See the library's license file for details.
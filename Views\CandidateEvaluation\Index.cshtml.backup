@model RafoEvaluation.ViewModels.SimpleEvaluationListViewModel
@{
    ViewData["Title"] = "تقييم المرشحين";
    ViewData["ActivePage"] = "CandidateEvaluation";
}

<div class="container-fluid">
    <!-- Enhanced Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-body-secondary text-dark shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-clipboard-check me-2"></i>
                                تقييم المرشحين
                            </h4>
                            <p class="card-text mt-2 mb-0 text-dark">
                                قائمة المرشحين المطلوب تقييمهم في اللجان التي أنت عضو فيها
                                @if (Model.IsCommitteeChair)
                                {
                                    <span class="badge bg-warning text-dark ms-2">
                                        <i class="fas fa-crown me-1"></i>رئيس اللجنة
                                    </span>
                                }
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h3 class="mb-0 text-dark">@Model.TotalCandidates</h3>
                                        <span class="badge bg-info text-dark">إجمالي المرشحين</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                            <h3 class="mb-0 text-dark">@Model.EvaluatedCandidates</h3>
                                        <span class="badge bg-warning text-dark">تم التقييم</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h3 class="mb-0 text-dark">@Model.PendingCandidates</h3>
                                        <span class="badge bg-success text-dark">في الانتظار</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-chart-line me-2"></i>
                                تقدم التقييم
                            </h6>
                            <div class="progress" style="height: 20px;">
                                @{
                                    var progressPercentage = Model.TotalCandidates > 0 ? (Model.EvaluatedCandidates * 100.0 / Model.TotalCandidates) : 0;
                                }
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: @progressPercentage%" 
                                     aria-valuenow="@progressPercentage" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                    @progressPercentage.ToString("F1")%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="completion-rate">
                                <h4 class="text-primary mb-0">@progressPercentage.ToString("F1")%</h4>
                                <small class="text-muted">معدل الإنجاز</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header modern-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث والتصفية
                        </h6>
                        <button class="btn btn-outline-secondary btn-sm" type="button" id="toggleFilters">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body filters-body" id="filtersContainer">
                    <form method="get" class="row g-3">
                        <div class="col-lg-4 col-md-6">
                            <label for="searchTerm" class="form-label fw-bold">
                                <i class="fas fa-search me-1 text-primary"></i>البحث العام
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control modern-input" 
                                       id="searchTerm" name="searchTerm" 
                                       value="@Model.SearchTerm" 
                                       placeholder="اسم المرشح أو الرقم العسكري">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label for="categoryFilter" class="form-label fw-bold">
                                <i class="fas fa-tags me-1 text-primary"></i>الفئة
                            </label>
                            <select class="form-select modern-input" id="categoryFilter" name="categoryFilter">
                                <option value="">جميع الفئات</option>
                                @foreach (var category in Model.AvailableCategories)
                                {
                                    <option value="@category.CategoryId" selected="@(Model.CategoryFilter == category.CategoryId)">
                                        @category.CategoryName
                                    </option>
                                }
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-6">
                            <label for="statusFilter" class="form-label fw-bold">
                                <i class="fas fa-filter me-1 text-primary"></i>الحالة
                            </label>
                            <select class="form-select modern-input" id="statusFilter" name="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="evaluated">تم التقييم</option>
                                <option value="pending">في الانتظار</option>
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-bold">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary flex-fill">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="fas fa-refresh"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Chairman Quick Access -->
    @if (Model.IsCommitteeChair)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card chairman-card shadow">
                    <div class="card-header bg-gradient-warning">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="card-title mb-0 text-dark">
                                    <i class="fas fa-crown me-2"></i>
                                    أدوات رئيس اللجنة
                                </h5>
                                <small class="text-dark-50">وصول سريع للميزات الخاصة برئيس اللجنة</small>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <span class="badge bg-warning text-dark fs-6 pulse">
                                    <i class="fas fa-crown me-1"></i>رئيس اللجنة
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-lg-6">
                                <a href="@Url.Action("CommitteeMembersStatus")" class="btn btn-gradient-warning btn-lg w-100 modern-btn">
                                    <i class="fas fa-user-tie me-2"></i>
                                    حالة تقييم أعضاء اللجنة
                                    <span class="btn-badge">@Model.TotalCandidates</span>
                                </a>
                            </div>
                            <div class="col-lg-6">
                                <div class="chairman-stats">
                                    <h6 class="text-muted mb-3">إحصائيات سريعة</h6>
                                    <div class="row g-2">
                                        <div class="col-4">
                                            <div class="mini-stat">
                                                <h5 class="text-primary mb-0">@Model.TotalCandidates</h5>
                                                <small class="text-muted">إجمالي</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="mini-stat">
                                                <h5 class="text-success mb-0">@Model.EvaluatedCandidates</h5>
                                                <small class="text-muted">مكتمل</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="mini-stat">
                                                <h5 class="text-warning mb-0">@Model.PendingCandidates</h5>
                                                <small class="text-muted">متبقي</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Enhanced Candidates List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-users me-2"></i>
                                المرشحين (@Model.TotalCount)
                            </h5>
                        </div>
                        <div class="col-md-6 text-md-end">
                            @if (Model.Candidates.Any())
                            {
                                <div class="btn-group modern-btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary" id="exportBtn">
                                        <i class="fas fa-download me-1"></i>تصدير Excel
                                    </button>
                                    <button type="button" class="btn btn-outline-info" id="exportPdfBtn">
                                        <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="printBtn">
                                        <i class="fas fa-print me-1"></i>طباعة
                                    </button>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (!Model.Candidates.Any())
                    {
                        <div class="empty-state text-center py-5">
                            <div class="empty-illustration mb-4">
                                <i class="fas fa-users fa-4x text-muted"></i>
                            </div>
                            <h5 class="text-muted mb-3">لا توجد مرشحين للتقييم</h5>
                            <p class="text-muted mb-4">لا توجد مرشحين مخصصين للجان التي أنت عضو فيها حالياً</p>
                            <div class="empty-actions">
                                <a href="@Url.Action("Index")" class="btn btn-primary me-2">
                                    <i class="fas fa-refresh me-1"></i>تحديث الصفحة
                                </a>
                                <a href="@Url.Action("Index", "Committee")" class="btn btn-outline-secondary">
                                    <i class="fas fa-users me-1"></i>إدارة اللجان
                                </a>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover modern-table" id="candidatesTable">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th style="width: 25%">
                                            <i class="fas fa-user me-1"></i>المرشح
                                        </th>
                                        <th style="width: 12%">
                                            <i class="fas fa-tags me-1"></i>الفئة
                                        </th>
                                        <th style="width: 15%">
                                            <i class="fas fa-users me-1"></i>اللجنة
                                        </th>
                                        <th style="width: 20%">
                                            <i class="fas fa-clipboard-list me-1"></i>نموذج التقييم
                                        </th>
                                        <th style="width: 12%">
                                            <i class="fas fa-chart-pie me-1"></i>حالة التقييم
                                        </th>
                                        <th style="width: 8%">
                                            <i class="fas fa-star me-1"></i>الدرجة
                                        </th>
                                        <th style="width: 8%">
                                            <i class="fas fa-cogs me-1"></i>الإجراءات
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var candidate in Model.Candidates)
                                    {
                                        <tr class="candidate-row" data-candidate-id="@candidate.AssignmentId">
                                            <td>
                                                <div class="candidate-info">
                                                    <div class="d-flex align-items-center">
                                                        <div class="candidate-avatar me-3">
                                                            @candidate.CandidateName.Substring(0, 1)
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-0 fw-bold candidate-toggle" 
                                                               style="cursor:pointer;" 
                                                               data-bs-toggle="collapse" 
                                                               data-bs-target="#<EMAIL>" 
                                                               aria-expanded="false" 
                                                               aria-controls="<EMAIL>">
                                                                @candidate.CandidateName
                                                                <i class="fas fa-chevron-down text-primary ms-2 transition-all"></i>
                                                            </h6>
                                                            <div class="collapse candidate-details mt-2" id="<EMAIL>">
                                                                <div class="candidate-meta">
                                                                    <div class="meta-item">
                                                                        <i class="fas fa-id-card text-primary me-1"></i>
                                                                        <span class="text-muted">@candidate.RankName - @candidate.ServiceNumber</span>
                                                                    </div>
                                                                    <div class="meta-item">
                                                                        <i class="fas fa-map-marker-alt text-success me-1"></i>
                                                                        <span class="text-muted">@candidate.AirbaseName</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-gradient-info modern-badge">
                                                    @candidate.CategoryName
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-gradient-secondary modern-badge">
                                                    @candidate.CommitteeName
                                                </span>
                                            </td>
                                            <td>
                                                <div class="evaluation-form-info">
                                                    <span class="text-muted fw-bold small">@candidate.EvaluationFormTitle</span>
                                                </div>
                                            </td>
                                            <td>
                                                @if (candidate.HasEvaluation)
                                                {
                                                    <span class="badge bg-gradient-success modern-badge">
                                                        <i class="fas fa-check me-1"></i>تم التقييم
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-gradient-warning modern-badge">
                                                        <i class="fas fa-clock me-1"></i>في الانتظار
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                @if (candidate.TotalScore.HasValue)
                                                {
                                                    <div class="score-display">
                                                        <span class="badge bg-gradient-primary score-badge">
                                                            @candidate.TotalScore.Value.ToString("F1")
                                                        </span>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm modern-btn-group" role="group">
                                                    <a href="@Url.Action("Evaluate", new { assignmentId = candidate.AssignmentId })" 
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if (candidate.HasEvaluation)
                                                    {
                                                        <a href="@Url.Action("Print", new { assignmentId = candidate.AssignmentId })" 
                                                           class="btn btn-outline-secondary btn-sm" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Enhanced Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Page navigation" class="mt-4">
                                <div class="d-flex justify-content-between align-items-center flex-wrap">
                                    <div class="pagination-info mb-2 mb-md-0">
                                        <small class="text-muted">
                                            عرض @((Model.CurrentPage - 1) * Model.PageSize + 1) إلى @Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount) من @Model.TotalCount نتيجة
                                        </small>
                                    </div>
                                    <ul class="pagination mb-0 modern-pagination">
                                        @if (Model.CurrentPage > 1)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" href="@Url.Action("Index", new { 
                                                    searchTerm = Model.SearchTerm, 
                                                    categoryFilter = Model.CategoryFilter, 
                                                    page = Model.CurrentPage - 1 })">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        }

                                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                <a class="page-link" href="@Url.Action("Index", new { 
                                                    searchTerm = Model.SearchTerm, 
                                                    categoryFilter = Model.CategoryFilter, 
                                                    page = i })">@i</a>
                                            </li>
                                        }

                                        @if (Model.CurrentPage < Model.TotalPages)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" href="@Url.Action("Index", new { 
                                                    searchTerm = Model.SearchTerm, 
                                                    categoryFilter = Model.CategoryFilter, 
                                                    page = Model.CurrentPage + 1 })">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </nav>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable with enhanced features - disable internal pagination
            const table = $('#candidatesTable').DataTable({
                "language": {
                    "url": "/lib/datatables/js/ar.json"
                },
                "pageLength": -1, // Show all records
                "paging": false, // Disable DataTable pagination (use server-side)
                "order": [[0, "asc"]],
                "columnDefs": [
                    { "orderable": false, "targets": [6] }
                ],
                "dom": 'rt', // Only show table and info
                "responsive": true,
                "autoWidth": false
            });

            // Enhanced Export to Excel
            $('#exportBtn').click(function() {
                try {
                    const data = [];
                    const headers = ['اسم المرشح', 'الرقم العسكري', 'الرتبة', 'القاعدة الجوية', 'الفئة', 'اللجنة', 'نموذج التقييم', 'حالة التقييم', 'الدرجة'];
                    data.push(headers);

                    @foreach (var candidate in Model.Candidates)
                    {
                        data.push([
                            '@candidate.CandidateName',
                            '@candidate.ServiceNumber',
                            '@candidate.RankName', 
                            '@candidate.AirbaseName',
                            '@candidate.CategoryName',
                            '@candidate.CommitteeName',
                            '@candidate.EvaluationFormTitle',
                            '@(candidate.HasEvaluation ? "تم التقييم" : "في الانتظار")',
                            '@(candidate.TotalScore?.ToString("F1") ?? "-")'
                        ]);
                    }

                    const ws = XLSX.utils.aoa_to_sheet(data);
                    const wb = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(wb, ws, "تقييم المرشحين");
                    
                    // Set column widths
                    ws['!cols'] = [
                        {wch: 20}, {wch: 15}, {wch: 15}, {wch: 20},
                        {wch: 15}, {wch: 20}, {wch: 25}, {wch: 15}, {wch: 10}
                    ];
                    
                    XLSX.writeFile(wb, `تقييم_المرشحين_${new Date().toISOString().split('T')[0]}.xlsx`);
                    
                    showToast('success', 'تم تصدير البيانات بنجاح');
                } catch (error) {
                    console.error('Export error:', error);
                    showToast('error', 'حدث خطأ أثناء التصدير');
                }
            });

            // Export to PDF (simplified)
            $('#exportPdfBtn').click(function() {
                showToast('info', 'جاري تحضير ملف PDF...');
                setTimeout(() => {
                    window.print(); // For now, use print as PDF
                }, 500);
            });

            // Enhanced Print
            $('#printBtn').click(function() {
                const printWindow = window.open('', '_blank');
                const printContent = `
                    <html dir="rtl">
                    <head>
                        <title>تقييم المرشحين</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            body { font-family: Arial, sans-serif; font-size: 12px; }
                            .print-header { text-align: center; margin-bottom: 20px; }
                            .print-table { width: 100%; border-collapse: collapse; font-size: 10px; }
                            .print-table th, .print-table td { border: 1px solid #ddd; padding: 5px; text-align: right; }
                            .print-table th { background-color: #f8f9fa; font-weight: bold; }
                            .badge { padding: 2px 6px; border-radius: 3px; }
                            .bg-info { background-color: #d1ecf1 !important; }
                            .bg-secondary { background-color: #d6d8db !important; }
                            .bg-success { background-color: #d4edda !important; }
                            .bg-warning { background-color: #fff3cd !important; }
                        </style>
                    </head>
                    <body>
                        <div class="print-header">
                            <h2>تقييم المرشحين</h2>
                            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                            <p>إجمالي المرشحين: @Model.TotalCount</p>
                        </div>
                        <table class="print-table">
                            <thead>
                                <tr>
                                    <th>المرشح</th>
                                    <th>الرقم العسكري</th>
                                    <th>الرتبة</th>
                                    <th>القاعدة</th>
                                    <th>الفئة</th>
                                    <th>اللجنة</th>
                                    <th>الحالة</th>
                                    <th>الدرجة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var candidate in Model.Candidates)
                                {
                                    <tr>
                                        <td>@candidate.CandidateName</td>
                                        <td>@candidate.ServiceNumber</td>
                                        <td>@candidate.RankName</td>
                                        <td>@candidate.AirbaseName</td>
                                        <td><span class="badge bg-info">@candidate.CategoryName</span></td>
                                        <td><span class="badge bg-secondary">@candidate.CommitteeName</span></td>
                                        <td>
                                            @if (candidate.HasEvaluation)
                                            {
                                                <span class="badge bg-success">تم التقييم</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">في الانتظار</span>
                                            }
                                        </td>
                                        <td>@(candidate.TotalScore?.ToString("F1") ?? "-")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </body>
                    </html>
                `;
                
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            });

            // Enhanced hover effects
            $('.candidate-row').hover(
                function() {
                    $(this).addClass('table-active');
                },
                function() {
                    $(this).removeClass('table-active');
                }
            );

            // Enhanced collapse functionality
            $('#candidatesTable').on('show.bs.collapse', '.candidate-details', function () {
                $(this).closest('tr').find('.candidate-toggle i')
                    .removeClass('fa-chevron-down').addClass('fa-chevron-up');
            });
            
            $('#candidatesTable').on('hide.bs.collapse', '.candidate-details', function () {
                $(this).closest('tr').find('.candidate-toggle i')
                    .removeClass('fa-chevron-up').addClass('fa-chevron-down');
            });

            // Toggle filters
            $('#toggleFilters').click(function() {
                const container = $('#filtersContainer');
                const icon = $(this).find('i');
                
                container.slideToggle();
                icon.toggleClass('fa-chevron-up fa-chevron-down');
            });

            // Clear search
            $('#clearSearch').click(function() {
                $('#searchTerm').val('').focus();
            });

            // Add keyboard shortcuts
            $(document).keydown(function(e) {
                if (e.ctrlKey) {
                    switch(e.which) {
                        case 70: // Ctrl+F for search
                            e.preventDefault();
                            $('#searchTerm').focus();
                            break;
                        case 80: // Ctrl+P for print
                            e.preventDefault();
                            $('#printBtn').click();
                            break;
                        case 69: // Ctrl+E for export
                            e.preventDefault();
                            $('#exportBtn').click();
                            break;
                    }
                }
            });
        });

        // Toast notification helper
        function showToast(type, message) {
            // Simple toast implementation
            const toastHtml = `
                <div class="toast show position-fixed bottom-0 end-0 m-3" role="alert">
                    <div class="toast-header bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} text-white">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}-circle me-2"></i>
                        <strong class="me-auto">${type === 'success' ? 'نجح' : type === 'error' ? 'خطأ' : 'معلومات'}</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                </div>
            `;
            
            $('body').append(toastHtml);
            
            setTimeout(() => {
                $('.toast').fadeOut(() => {
                    $('.toast').remove();
                });
            }, 3000);
        }
    </script>
}

<style>
 
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
        --shadow-light: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --shadow-medium: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --shadow-heavy: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        --border-radius: 12px;
        --transition: all 0.3s ease;
    }

    /* Gradient Classes */
    .bg-gradient-primary { background: var(--primary-gradient) !important; }
    .bg-gradient-success { background: var(--success-gradient) !important; }
    .bg-gradient-warning { background: var(--warning-gradient) !important; }
    .bg-gradient-info { background: var(--info-gradient) !important; }
    .bg-gradient-secondary { background: var(--dark-gradient) !important; }

    /* Chairman Card Styles */
    .chairman-card {
        border-left: 4px solid #ffc107;
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .chairman-card .card-header {
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .chairman-stats .mini-stat {
        text-align: center;
        padding: 0.75rem;
        background: white;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        box-shadow: var(--shadow-light);
        transition: var(--transition);
    }

    .chairman-stats .mini-stat:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

    /* Modern Button Styles */
    .modern-btn {
        border-radius: 8px;
        font-weight: 500;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .modern-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-medium);
    }

    .btn-gradient-warning {
        background: var(--warning-gradient);
        border: none;
        color: #333;
    }

    .btn-gradient-warning:hover {
        background: linear-gradient(135deg, #ff6b9d 0%, #ffc837 100%);
        color: #333;
    }

    .btn-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 22px;
        height: 22px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .modern-btn-group .btn {
        border-radius: 6px;
        margin: 0 2px;
        font-weight: 500;
    }

    /* Enhanced Statistics */
    .stat-item {
        padding: 0.5rem;
        transition: var(--transition);
    }

    .stat-item:hover {
        transform: scale(1.05);
    }

    .stat-item h3 {
        font-weight: 700;
        font-size: 2rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .completion-rate h4 {
        font-weight: bold;
    }

    /* Modern Cards */
    .modern-card {
        border: none;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
    }

    .modern-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-heavy);
    }

    .modern-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
    }

    /* Enhanced Table */
    .modern-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-light);
    }

    .modern-table thead th {
        background: var(--dark-gradient);
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem 0.75rem;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .modern-table tbody tr {
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
        transform: scale(1.01);
        box-shadow: var(--shadow-medium);
    }

    .modern-table td {
        vertical-align: middle;
        padding: 0.75rem;
    }

    /* Candidate Info */
    .candidate-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-gradient);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.1rem;
        box-shadow: var(--shadow-light);
    }

    .candidate-details {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 0.5rem;
        border-left: 3px solid #007bff;
    }

    .candidate-meta .meta-item {
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
    }

    .candidate-toggle i {
        transition: transform 0.3s ease;
    }

    .candidate-toggle[aria-expanded="true"] i {
        transform: rotate(180deg);
    }

    .candidate-row {
        transition: var(--transition);
    }

    .candidate-row:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Modern Badges */
    .modern-badge {
        border-radius: 20px;
        padding: 0.375rem 0.75rem;
        font-weight: 500;
        font-size: 0.75rem;
        box-shadow: var(--shadow-light);
    }

    .score-badge {
        font-size: 1rem;
        padding: 0.5rem 0.75rem;
        border-radius: 25px;
        box-shadow: var(--shadow-medium);
    }

    .score-display {
        text-align: center;
    }

    /* Modern Progress */
    .progress {
        border-radius: 50px;
        background: #e9ecef;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
    }

    .progress-bar {
        border-radius: 50px;
        font-weight: bold;
        transition: width 1s ease-in-out;
    }

    /* Empty State */
    .empty-state {
        padding: 3rem 0;
        max-width: 400px;
        margin: 0 auto;
    }

    .empty-illustration {
        opacity: 0.6;
    }

    .empty-actions .btn {
        margin: 0 0.25rem;
    }

    /* Pagination */
    .modern-pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        color: #667eea;
        border: 1px solid #dee2e6;
        transition: var(--transition);
    }

    .modern-pagination .page-link:hover {
        background: var(--primary-gradient);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--shadow-light);
    }

    .modern-pagination .active .page-link {
        background: var(--primary-gradient);
        border-color: transparent;
        box-shadow: var(--shadow-medium);
    }

    .pagination-info {
        font-size: 0.875rem;
    }

    /* Modern Inputs */
    .modern-input {
        border-radius: 8px;
        border: 1px solid #ced4da;
        transition: var(--transition);
    }

    .modern-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* Filter Toggle */
    .filters-body {
        transition: var(--transition);
    }

    /* Utility Classes */
    .shadow-sm {
        box-shadow: var(--shadow-light) !important;
    }

    .shadow {
        box-shadow: var(--shadow-medium) !important;
    }

    .evaluation-form-info {
        max-width: 200px;
    }

    /* Animations */
    .pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .transition-all {
        transition: var(--transition);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-container {
            margin-top: 1rem;
        }

        .stats-container .col-4 {
            margin-bottom: 0.5rem;
        }

        .modern-btn-group {
            flex-direction: column;
            width: 100%;
        }

        .modern-btn-group .btn {
            margin: 2px 0;
            font-size: 0.75rem;
        }

        .candidate-avatar {
            width: 35px;
            height: 35px;
            font-size: 1rem;
        }

        .modern-table {
            font-size: 0.875rem;
        }

        .modern-table th,
        .modern-table td {
            padding: 0.5rem 0.25rem;
        }

        .candidate-info h6 {
            font-size: 0.9rem;
        }

        .meta-item {
            font-size: 0.75rem !important;
        }
    }

    @media (max-width: 576px) {
        .container-fluid {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        .modern-card {
            margin-bottom: 1rem;
        }

        .modern-table th,
        .modern-table td {
            padding: 0.25rem;
            font-size: 0.75rem;
        }
    }

    /* Print Styles */
    @media print {
        .btn, .pagination, .card-header, .modern-btn-group, .chairman-card {
            display: none !important;
        }
        
        .modern-card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
            page-break-inside: avoid;
        }

        .modern-table {
            font-size: 0.8rem;
        }

        .candidate-row:hover {
            background: transparent !important;
            transform: none !important;
        }
    }

    /* Dark Mode Support (Future Enhancement) */
    @media (prefers-color-scheme: dark) {
        :root {
            --shadow-light: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.075);
            --shadow-medium: 0 0.5rem 1rem rgba(255, 255, 255, 0.15);
            --shadow-heavy: 0 1rem 3rem rgba(255, 255, 255, 0.175);
        }
    }
</style> 
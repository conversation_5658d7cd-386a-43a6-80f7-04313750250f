@model RafoEvaluation.ViewModels.UserDetailsViewModel
@{
    ViewData["Title"] = "تفاصيل المستخدم";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-user breadcrumb-icon"></i>
                    تفاصيل المستخدم
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="User">
                                <i class="fas fa-users"></i> المستخدمين
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-user"></i> تفاصيل المستخدم
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user ms-2 text-dark"></i>تفاصيل المستخدم: @Model.ServiceNumber
                    </h6>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.UserId" class="btn btn-warning">
                            <i class="fas fa-edit ms-1"></i>تعديل
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-right ms-1"></i>رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">رقم الخدمة:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.ServiceNumber</strong>
                                </dd>

                                <dt class="col-sm-4">الرتبة:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-warning text-dark">@Model.RankName</span>
                                </dd>

                                <dt class="col-sm-4">الاسم الكامل:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.FullName</strong>
                                </dd>

                                <dt class="col-sm-4">الصلاحيات:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.RoleNames.Any())
                                    {
                                        @foreach (var role in Model.RoleNames)
                                        {
                                            <span class="badge bg-primary me-1">@role</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">لا توجد صلاحيات</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">الحالة:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غير نشط</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                                <dd class="col-sm-8">
                                    <span class="text-muted">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                                </dd>

                                <dt class="col-sm-4">آخر تسجيل دخول:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.LastLoginAt.HasValue)
                                    {
                                        <span class="text-muted">@Model.LastLoginAt.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">لم يسجل دخول</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">
                                <i class="fas fa-info-circle ms-2"></i>معلومات إضافية
                            </h5>
                            <div class="alert alert-info">
                                <ul class="mb-0">
                                    <li>يمكن للمستخدم الوصول للنظام باستخدام رقم الخدمة وكلمة المرور</li>
                                    <li>الصلاحيات تحدد ما يمكن للمستخدم رؤيته والقيام به في النظام</li>
                                    <li>يمكن تفعيل أو إلغاء تفعيل المستخدم من صفحة التعديل</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 
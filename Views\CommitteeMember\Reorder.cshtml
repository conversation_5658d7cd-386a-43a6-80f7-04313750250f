@model RafoEvaluation.ViewModels.CommitteeMemberReorderViewModel
@{
    ViewData["Title"] = "إعادة ترتيب أعضاء اللجنة";
}

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-controller="CommitteeMember" asp-action="Index">أعضاء اللجان</a></li>
            <li class="breadcrumb-item active" aria-current="page">إعادة ترتيب الأعضاء</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sort me-2"></i>
                                إعادة ترتيب أعضاء اللجنة
                            </h5>
                            <small>@Model.CommitteeName</small>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a asp-action="Index" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تعليمات:</strong> اسحب وأفلت الأعضاء لتغيير ترتيبهم. سيظهر الترتيب الجديد في التقارير والتقييمات.
                    </div>

                    <form id="reorderForm">
                        @Html.AntiForgeryToken()
                        <input type="hidden" id="committeeId" value="@Model.CommitteeId" />
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-list me-2"></i>
                                            ترتيب الأعضاء
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="sortableMembers" class="list-group">
                                            @foreach (var member in Model.Members)
                                            {
                                                <div class="list-group-item d-flex justify-content-between align-items-center" 
                                                     data-member-id="@member.CommitteeMemberId">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-grip-vertical me-3 text-muted handle"></i>
                                                        <div>
                                                            <h6 class="mb-1">@member.FormattedDisplayName</h6>
                                                            <small class="text-muted">
                                                                رقم الخدمة: @member.ServiceNumber
                                                            </small>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        @{
                                                            var roleBadgeClass = member.Role switch
                                                            {
                                                                CommitteeMemberRole.رئيس_اللجنة => "bg-danger",
                                                                CommitteeMemberRole.عضو => "bg-primary",
                                                                CommitteeMemberRole.منسق => "bg-success",
                                                                _ => "bg-secondary"
                                                            };
                                                        }
                                                        <span class="badge @roleBadgeClass me-2">@member.RoleDisplayName</span>
                                                        
                                                        @if (member.CanEvaluate)
                                                        {
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check me-1"></i>يمكنه التقييم
                                                            </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-warning text-dark">
                                                                <i class="fas fa-times me-1"></i>لا يمكنه التقييم
                                                            </span>
                                                        }
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-cog me-2"></i>
                                            الإجراءات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="button" id="saveOrderBtn" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>
                                                حفظ الترتيب الجديد
                                            </button>
                                            <button type="button" id="resetOrderBtn" class="btn btn-secondary">
                                                <i class="fas fa-undo me-2"></i>
                                                إعادة تعيين الترتيب
                                            </button>
                                            <a asp-action="Index" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-2"></i>
                                                إلغاء
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            معلومات الترتيب
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <h6 class="text-primary mb-1">@Model.Members.Count</h6>
                                                    <small class="text-muted">إجمالي الأعضاء</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <h6 class="text-success mb-1">@Model.Members.Count(m => m.CanEvaluate)</h6>
                                                    <small class="text-muted">يمكنهم التقييم</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-list-ol me-2"></i>
                                            الترتيب الحالي (للتقارير)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="currentOrderDisplay" class="small">
                                            <!-- سيتم تحديث هذا القسم تلقائياً -->
                                        </div>
                                        <div class="mt-2">
                                            <button type="button" id="copyOrderBtn" class="btn btn-outline-primary btn-sm w-100">
                                                <i class="fas fa-copy me-1"></i>
                                                نسخ الترتيب
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/sortablejs/Sortable.min.js"></script>
    <script>
        $(document).ready(function () {
            // تهيئة Sortable
            var sortable = Sortable.create(document.getElementById('sortableMembers'), {
                handle: '.handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: function (evt) {
                    updateMemberNumbers();
                }
            });

            // تحديث أرقام الأعضاء والأسماء
            function updateMemberNumbers() {
                $('#sortableMembers .list-group-item').each(function (index) {
                    var memberId = $(this).data('member-id');
                    var newPosition = index + 1;
                    
                    // تحديث اسم العضو بناءً على موقعه الجديد
                    var memberNameElement = $(this).find('h6.mb-1');
                    var currentText = memberNameElement.text();
                    
                    // استخراج المعلومات الأساسية (الرتبة والاسم)
                    var match = currentText.match(/^(.+?)\s+(.+?)\s+\((.+?)\)$/);
                    if (match) {
                        var rankName = match[1];
                        var fullName = match[2];
                        var role = match[3];
                        
                        // تحديث النص بناءً على الموقع الجديد
                        var newRoleText;
                        if (newPosition === 1) {
                            newRoleText = "رئيس اللجنة (العضو الأول)";
                        } else {
                            newRoleText = `العضو ${getArabicNumber(newPosition)}`;
                        }
                        
                        var newDisplayName = `${rankName} ${fullName} (${newRoleText})`;
                        memberNameElement.text(newDisplayName);
                        
                        // إضافة تأثير بصري مؤقت
                        memberNameElement.addClass('text-primary');
                        setTimeout(function() {
                            memberNameElement.removeClass('text-primary');
                        }, 1000);
                    }
                    
                    console.log(`Member ${memberId} is now at position ${newPosition}`);
                });
                
                // تحديث عرض الترتيب الحالي
                updateCurrentOrderDisplay();
            }
            
            // تحديث عرض الترتيب الحالي
            function updateCurrentOrderDisplay() {
                var orderText = '';
                $('#sortableMembers .list-group-item').each(function (index) {
                    var position = index + 1;
                    var memberName = $(this).find('h6.mb-1').text();
                    
                    // استخراج الاسم فقط بدون الرتبة والدور
                    var match = memberName.match(/^(.+?)\s+(.+?)\s+\((.+?)\)$/);
                    if (match) {
                        var rankName = match[1];
                        var fullName = match[2];
                        var roleText = match[3];
                        
                        orderText += `<div class="mb-1">
                            <strong>${position}.</strong> ${rankName} ${fullName}
                            <br><small class="text-muted">${roleText}</small>
                        </div>`;
                    }
                });
                
                $('#currentOrderDisplay').html(orderText);
            }
            
            // دالة تحويل الأرقام إلى العربية
            function getArabicNumber(number) {
                var arabicNumbers = {
                    1: "الأول",
                    2: "الثاني", 
                    3: "الثالث",
                    4: "الرابع",
                    5: "الخامس",
                    6: "السادس",
                    7: "السابع",
                    8: "الثامن",
                    9: "التاسع",
                    10: "العاشر"
                };
                
                return arabicNumbers[number] || number.toString();
            }

            // حفظ الترتيب الجديد
            $('#saveOrderBtn').click(function () {
                var memberIds = [];
                $('#sortableMembers .list-group-item').each(function () {
                    memberIds.push($(this).data('member-id'));
                });

                var committeeId = $('#committeeId').val();
                
                // الحصول على token الحماية من CSRF
                var token = $('input[name="__RequestVerificationToken"]').val();

                $.ajax({
                    url: '@Url.Action("ReorderMembers", "CommitteeMember")',
                    type: 'POST',
                    data: {
                        committeeId: committeeId,
                        memberIds: memberIds,
                        __RequestVerificationToken: token
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم بنجاح!',
                                text: response.message,
                                confirmButtonText: 'حسناً'
                            }).then((result) => {
                                window.location.href = '@Url.Action("Index", "CommitteeMember")';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ!',
                                text: response.message,
                                confirmButtonText: 'حسناً'
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: 'حدث خطأ أثناء حفظ الترتيب',
                            confirmButtonText: 'حسناً'
                        });
                    }
                });
            });

            // إعادة تعيين الترتيب
            $('#resetOrderBtn').click(function () {
                Swal.fire({
                    title: 'تأكيد إعادة التعيين',
                    text: 'هل تريد إعادة ترتيب الأعضاء حسب الترتيب الأصلي؟',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، إعادة التعيين',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload();
                    }
                });
            });
            
            // نسخ الترتيب الحالي
            $('#copyOrderBtn').click(function () {
                var orderText = '';
                $('#sortableMembers .list-group-item').each(function (index) {
                    var position = index + 1;
                    var memberName = $(this).find('h6.mb-1').text();
                    
                    // استخراج الاسم فقط بدون الرتبة والدور
                    var match = memberName.match(/^(.+?)\s+(.+?)\s+\((.+?)\)$/);
                    if (match) {
                        var rankName = match[1];
                        var fullName = match[2];
                        var roleText = match[3];
                        
                        orderText += `${position}. ${rankName} ${fullName} (${roleText})\n`;
                    }
                });
                
                // نسخ إلى الحافظة
                navigator.clipboard.writeText(orderText).then(function() {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم النسخ!',
                        text: 'تم نسخ ترتيب الأعضاء إلى الحافظة',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }).catch(function() {
                    // إذا فشل النسخ، اعرض النص في نافذة منبثقة
                    Swal.fire({
                        title: 'ترتيب الأعضاء',
                        html: '<pre style="text-align: right; direction: rtl;">' + orderText + '</pre>',
                        confirmButtonText: 'حسناً'
                    });
                });
            });
            
            // تهيئة العرض الأولي
            updateCurrentOrderDisplay();
        });
    </script>
}

<style>
    .handle {
        cursor: grab;
    }
    .handle:active {
        cursor: grabbing;
    }
    .sortable-ghost {
        opacity: 0.5;
        background: #f8f9fa;
    }
    .sortable-chosen {
        background: #e3f2fd;
    }
    .sortable-drag {
        background: #fff;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
</style> 
﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Airbases",
                columns: table => new
                {
                    AirbaseId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AirbaseName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Airbases", x => x.AirbaseId);
                });

            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    CategoryId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CategoryName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CategoryCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.CategoryId);
                });

            migrationBuilder.CreateTable(
                name: "Committees",
                columns: table => new
                {
                    CommitteeId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CommitteeName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Committees", x => x.CommitteeId);
                });

            migrationBuilder.CreateTable(
                name: "EvaluationCriteria",
                columns: table => new
                {
                    CriteriaId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CriteriaName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    MaxScore = table.Column<int>(type: "int", nullable: false),
                    Weight = table.Column<decimal>(type: "decimal(5,2)", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationCriteria", x => x.CriteriaId);
                });

            migrationBuilder.CreateTable(
                name: "EvaluationForms",
                columns: table => new
                {
                    EvaluationFormId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationForms", x => x.EvaluationFormId);
                });

            migrationBuilder.CreateTable(
                name: "Ranks",
                columns: table => new
                {
                    RankId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RankName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ranks", x => x.RankId);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    RoleId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.RoleId);
                });

            migrationBuilder.CreateTable(
                name: "EvaluationFormItems",
                columns: table => new
                {
                    EvaluationFormItemId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EvaluationFormId = table.Column<int>(type: "int", nullable: false),
                    Criteria = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    MaxScore = table.Column<int>(type: "int", nullable: false, defaultValue: 10),
                    Weight = table.Column<decimal>(type: "decimal(3,1)", nullable: false, defaultValue: 1.0m),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationFormItems", x => x.EvaluationFormItemId);
                    table.ForeignKey(
                        name: "FK_EvaluationFormItems_EvaluationForms_EvaluationFormId",
                        column: x => x.EvaluationFormId,
                        principalTable: "EvaluationForms",
                        principalColumn: "EvaluationFormId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Candidates",
                columns: table => new
                {
                    CandidateId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FullName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CategoryId = table.Column<int>(type: "int", nullable: false),
                    ServiceNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    NationalIdNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    RankId = table.Column<int>(type: "int", nullable: false),
                    AirbaseId = table.Column<int>(type: "int", nullable: false),
                    Department = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Major = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    University = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    GraduationYear = table.Column<int>(type: "int", nullable: true),
                    MarksGrade = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    DateOfBirth = table.Column<DateTime>(type: "datetime2", nullable: false),
                    JobTitle = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Candidates", x => x.CandidateId);
                    table.ForeignKey(
                        name: "FK_Candidates_Airbases_AirbaseId",
                        column: x => x.AirbaseId,
                        principalTable: "Airbases",
                        principalColumn: "AirbaseId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Candidates_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "CategoryId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Candidates_Ranks_RankId",
                        column: x => x.RankId,
                        principalTable: "Ranks",
                        principalColumn: "RankId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    UserId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ServiceNumber = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false),
                    Password = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    RankId = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    LastLoginAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.UserId);
                    table.ForeignKey(
                        name: "FK_Users_Ranks_RankId",
                        column: x => x.RankId,
                        principalTable: "Ranks",
                        principalColumn: "RankId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CandidateEvaluations",
                columns: table => new
                {
                    CandidateEvaluationId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CandidateId = table.Column<int>(type: "int", nullable: false),
                    EvaluationFormId = table.Column<int>(type: "int", nullable: false),
                    CommitteeId = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    TotalScore = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    EvaluatorCount = table.Column<int>(type: "int", nullable: false),
                    StartedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CompletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    GeneralNotes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CandidateEvaluations", x => x.CandidateEvaluationId);
                    table.ForeignKey(
                        name: "FK_CandidateEvaluations_Candidates_CandidateId",
                        column: x => x.CandidateId,
                        principalTable: "Candidates",
                        principalColumn: "CandidateId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateEvaluations_Committees_CommitteeId",
                        column: x => x.CommitteeId,
                        principalTable: "Committees",
                        principalColumn: "CommitteeId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateEvaluations_EvaluationForms_EvaluationFormId",
                        column: x => x.EvaluationFormId,
                        principalTable: "EvaluationForms",
                        principalColumn: "EvaluationFormId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                columns: table => new
                {
                    UserRoleId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    RoleId = table.Column<int>(type: "int", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => x.UserRoleId);
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "RoleId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Airbases",
                columns: new[] { "AirbaseId", "AirbaseName" },
                values: new object[,]
                {
                    { 1, "قيادة سلاح الجو السلطاني العماني" },
                    { 2, "قاعدة غلا وأكاديمية السلطان قابوس الجوية" },
                    { 3, "قاعدة السيب الجوية" },
                    { 4, "قاعدة صلالة الجوية" },
                    { 5, "قاعدة المصنعة الجوية" },
                    { 6, "قاعدة مصيرة الجوية" },
                    { 7, "قاعدة أدم الجوية" },
                    { 8, "قاعدة ثمريت الجوية" },
                    { 9, "قاعدة خصب الجوية" }
                });

            migrationBuilder.InsertData(
                table: "Categories",
                columns: new[] { "CategoryId", "CategoryCode", "CategoryName", "CreatedAt", "Description", "IsActive", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, "CAT-PLT", "المرشحيين الطيارين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "فئة المرشحين للالتحاق بسلاح الجو السلطاني العماني كمرشحين طيارين.", true, null },
                    { 2, "CAT-MUG", "المرشحيين الجامعيين العسكريين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط عسكريين جامعيين", true, null },
                    { 3, "CAT-CUG", "المرشحيين الجامعيين المدنيين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط مدنيين جامعيين", true, null },
                    { 4, "CAT-LSO", "ضباط الخدمة المحدودة", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط الصف من ذوي الخدمة المحدودة وسسبق لهم العمل في السلاح وهم برتبة وكيل فأعلى", true, null },
                    { 5, "CAT-NCO", "ضباط الصف ( رقباء / عرفاء)", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف برتبة رقيب أو عريف سبق لهم العمل بالسلاح", true, null },
                    { 6, "CAT-TCN", "ضباط الصف الكلية التقنية العسكرية", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف خريجوا الكلية العسكرية التقنية", true, null },
                    { 7, "CAT-CNP", "ضباط الصف المدنيين للترفيع", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف مدنيين مرشحين للترقية بالصفة المدنية", true, null }
                });

            migrationBuilder.InsertData(
                table: "Committees",
                columns: new[] { "CommitteeId", "CommitteeName", "CreatedAt", "Description", "Status", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, "لجنة تقييم الطيارين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "تقييم المرشحين لمناصب الطيران", 1, null },
                    { 2, "لجنة المقابلات الشخصية", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "إجراء المقابلات للمرشحين", 1, null },
                    { 3, "لجنة الفحص الطبي", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "فحص اللياقة الطبية للمرشحين", 1, null },
                    { 4, "لجنة التقييم التقني", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "تقييم المهارات التقنية والهندسية", 1, null }
                });

            migrationBuilder.InsertData(
                table: "EvaluationCriteria",
                columns: new[] { "CriteriaId", "CreatedAt", "CriteriaName", "Description", "DisplayOrder", "IsActive", "MaxScore", "UpdatedAt", "Weight" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "المظهر العام واللياقة البدنية", "تقييم المظهر العام واللياقة البدنية للمرشح", 1, true, 10, null, 1.0m },
                    { 2, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الثقافة العامة", "تقييم مستوى الثقافة العامة والمعرفة", 2, true, 15, null, 1.5m },
                    { 3, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "المهارات اللغوية", "تقييم مهارات اللغة العربية والإنجليزية", 3, true, 15, null, 1.5m },
                    { 4, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "المهارات التقنية", "تقييم المهارات التقنية والمعلوماتية", 4, true, 20, null, 2.0m },
                    { 5, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الشخصية والقيادة", "تقييم الشخصية ومهارات القيادة", 5, true, 20, null, 2.0m },
                    { 6, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الدوافع والطموح", "تقييم دوافع المرشح وطموحاته المهنية", 6, true, 10, null, 1.0m },
                    { 7, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الخبرة العملية", "تقييم الخبرة العملية السابقة", 7, true, 10, null, 1.0m }
                });

            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "RankId", "CreatedAt", "Description", "DisplayOrder", "RankName" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8060), "Senior Air Officer", 1, "Air Marshal" },
                    { 2, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8741), "Senior Air Officer", 2, "Air Vice Marshal" },
                    { 3, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8743), "Senior Air Officer", 3, "Air Commodore" },
                    { 4, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8744), "Senior Officer", 4, "Group Captain" },
                    { 5, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8745), "Senior Officer", 5, "Wing Commander" },
                    { 6, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8746), "Officer", 6, "Squadron Leader" },
                    { 7, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8746), "Officer", 7, "Flight Lieutenant" },
                    { 8, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8747), "Junior Officer", 8, "Flying Officer" },
                    { 9, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8748), "Junior Officer", 9, "Pilot Officer" },
                    { 10, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8749), "Senior NCO", 10, "Warrant Officer" },
                    { 11, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8750), "NCO", 11, "Flight Sergeant" },
                    { 12, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8751), "NCO", 12, "Sergeant" },
                    { 13, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8751), "Junior NCO", 13, "Corporal" },
                    { 14, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8752), "Enlisted", 14, "Senior Aircraftman" },
                    { 15, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8753), "Enlisted", 15, "Leading Aircraftman" },
                    { 16, new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8764), "Enlisted", 16, "Aircraftman" }
                });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "RoleId", "CreatedAt", "Description", "RoleName" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(4502), "System Administrator with full access", "Admin" },
                    { 2, new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5069), "Evaluation Coordinator", "Coordinator" },
                    { 3, new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5071), "Personnel Evaluator", "Evaluator" },
                    { 4, new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5072), "Read-only access to evaluations", "Viewer" },
                    { 5, new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5072), "Human Resources Manager", "HR Manager" },
                    { 6, new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5073), "Training and Development Officer", "Training Officer" }
                });

            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "UserId", "CreatedAt", "IsActive", "LastLoginAt", "Password", "RankId", "ServiceNumber" },
                values: new object[] { 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), true, null, "$2a$11$N9qo8uLOickgx2ZMRZoMye.IjdQXvbVxVv0v6XVHbOxLuvh.LbHSi", 3, "RAFO001" });

            migrationBuilder.InsertData(
                table: "UserRoles",
                columns: new[] { "UserRoleId", "AssignedAt", "RoleId", "UserId" },
                values: new object[] { 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), 1, 1 });

            migrationBuilder.CreateIndex(
                name: "IX_CandidateEvaluations_CandidateId_FormId_CommitteeId",
                table: "CandidateEvaluations",
                columns: new[] { "CandidateId", "EvaluationFormId", "CommitteeId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CandidateEvaluations_CommitteeId",
                table: "CandidateEvaluations",
                column: "CommitteeId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateEvaluations_EvaluationFormId",
                table: "CandidateEvaluations",
                column: "EvaluationFormId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateEvaluations_Status",
                table: "CandidateEvaluations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_AirbaseId",
                table: "Candidates",
                column: "AirbaseId");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_CategoryId",
                table: "Candidates",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_FullName",
                table: "Candidates",
                column: "FullName");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_NationalIdNumber",
                table: "Candidates",
                column: "NationalIdNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_RankId",
                table: "Candidates",
                column: "RankId");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_ServiceNumber",
                table: "Candidates",
                column: "ServiceNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Committees_CommitteeName",
                table: "Committees",
                column: "CommitteeName");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationCriteria_DisplayOrder",
                table: "EvaluationCriteria",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationFormItems_FormId",
                table: "EvaluationFormItems",
                column: "EvaluationFormId");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_Title",
                table: "EvaluationForms",
                column: "Title");

            migrationBuilder.CreateIndex(
                name: "IX_Ranks_RankName",
                table: "Ranks",
                column: "RankName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Roles_RoleName",
                table: "Roles",
                column: "RoleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                table: "UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId_RoleId",
                table: "UserRoles",
                columns: new[] { "UserId", "RoleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_RankId",
                table: "Users",
                column: "RankId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_ServiceNumber",
                table: "Users",
                column: "ServiceNumber",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CandidateEvaluations");

            migrationBuilder.DropTable(
                name: "EvaluationCriteria");

            migrationBuilder.DropTable(
                name: "EvaluationFormItems");

            migrationBuilder.DropTable(
                name: "UserRoles");

            migrationBuilder.DropTable(
                name: "Candidates");

            migrationBuilder.DropTable(
                name: "Committees");

            migrationBuilder.DropTable(
                name: "EvaluationForms");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "Airbases");

            migrationBuilder.DropTable(
                name: "Categories");

            migrationBuilder.DropTable(
                name: "Ranks");
        }
    }
}

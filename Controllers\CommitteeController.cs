using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.ViewModels;
using Microsoft.AspNetCore.Authorization;

namespace RafoEvaluation.Controllers
{
    [Authorize(Roles = "Admin")]
    public class CommitteeController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CommitteeController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Committee
        public async Task<IActionResult> Index(string searchTerm = "", CommitteeStatus? statusFilter = null, int page = 1)
        {
            const int pageSize = 10;
            var query = _context.Committees.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(c => c.CommitteeName.Contains(searchTerm) || 
                                        (c.Description != null && c.Description.Contains(searchTerm)));
            }

            if (statusFilter.HasValue)
            {
                query = query.Where(c => c.Status == statusFilter.Value);
            }

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

            var committees = await query
                .OrderBy(c => c.CommitteeName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(c => new CommitteeListItemViewModel
                {
                    CommitteeId = c.CommitteeId,
                    CommitteeName = c.CommitteeName,
                    Description = c.Description,
                    Status = c.Status,
                    CreatedAt = c.CreatedAt
                })
                .ToListAsync();

            // Calculate statistics
            var totalCommittees = await _context.Committees.CountAsync();
            var activeCommittees = await _context.Committees.CountAsync(c => c.Status == CommitteeStatus.Active);
            var inactiveCommittees = await _context.Committees.CountAsync(c => c.Status == CommitteeStatus.Inactive);

            var viewModel = new CommitteeListViewModel
            {
                Committees = committees,
                SearchTerm = searchTerm,
                StatusFilter = statusFilter,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCommittees = totalCommittees,
                ActiveCommittees = activeCommittees,
                InactiveCommittees = inactiveCommittees
            };

            return View(viewModel);
        }

        // GET: Committee/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var committee = await _context.Committees
                .FirstOrDefaultAsync(c => c.CommitteeId == id);

            if (committee == null)
            {
                return NotFound();
            }

            // Get committee members with user information
            var members = await _context.CommitteeMembers
                .Where(cm => cm.CommitteeId == id)
                .Select(cm => new CommitteeMemberDetailsViewModel
                {
                    CommitteeMemberId = cm.CommitteeMemberId,
                    CommitteeId = cm.CommitteeId,
                    CommitteeName = committee.CommitteeName,
                    ServiceNumber = cm.ServiceNumber,
                    UserName = cm.User != null ? cm.User.FullName : null,
                    RankName = cm.User != null && cm.User.Rank != null ? cm.User.Rank.RankName : null,
                    Role = cm.Role,
                    Notes = cm.Notes,
                    IsActive = cm.IsActive,
                    CreatedAt = cm.CreatedAt,
                    UpdatedAt = cm.UpdatedAt
                })
                .ToListAsync();

            var viewModel = new CommitteeDetailsViewModel
            {
                CommitteeId = committee.CommitteeId,
                CommitteeName = committee.CommitteeName,
                Description = committee.Description,
                Status = committee.Status,
                CreatedAt = committee.CreatedAt,
                UpdatedAt = committee.UpdatedAt,
                Members = members
            };

            return View(viewModel);
        }

        // GET: Committee/Create
        public IActionResult Create()
        {
            return View(new CommitteeCreateViewModel());
        }

        // POST: Committee/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CommitteeCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                var committee = new Committee
                {
                    CommitteeName = model.CommitteeName,
                    Description = model.Description,
                    Status = model.Status,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Add(committee);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم إنشاء اللجنة بنجاح.";
                return RedirectToAction(nameof(Details), new { id = committee.CommitteeId });
            }

            return View(model);
        }

        // GET: Committee/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var committee = await _context.Committees.FindAsync(id);
            if (committee == null)
            {
                return NotFound();
            }

            var viewModel = new CommitteeEditViewModel
            {
                CommitteeId = committee.CommitteeId,
                CommitteeName = committee.CommitteeName,
                Description = committee.Description,
                Status = committee.Status
            };

            return View(viewModel);
        }

        // POST: Committee/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CommitteeEditViewModel model)
        {
            if (id != model.CommitteeId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var committee = await _context.Committees.FindAsync(id);
                    if (committee == null)
                    {
                        return NotFound();
                    }

                    committee.CommitteeName = model.CommitteeName;
                    committee.Description = model.Description;
                    committee.Status = model.Status;
                    committee.UpdatedAt = DateTime.UtcNow;

                    _context.Update(committee);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "تم تحديث اللجنة بنجاح.";
                    return RedirectToAction(nameof(Details), new { id = committee.CommitteeId });
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CommitteeExists(id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            return View(model);
        }

        // GET: Committee/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var committee = await _context.Committees
                .FirstOrDefaultAsync(c => c.CommitteeId == id);

            if (committee == null)
            {
                return NotFound();
            }

            return View(committee);
        }

        // POST: Committee/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var committee = await _context.Committees.FindAsync(id);
            if (committee != null)
            {
                _context.Committees.Remove(committee);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف اللجنة بنجاح.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool CommitteeExists(int id)
        {
            return _context.Committees.Any(e => e.CommitteeId == id);
        }
    }
} 
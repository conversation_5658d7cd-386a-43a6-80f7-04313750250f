@model List<RafoEvaluation.ViewModels.CandidateEvaluationResultViewModel>

@{
    ViewData["Title"] = "طباعة نتائج التقييم";
    var evaluationForm = ViewBag.EvaluationForm as RafoEvaluation.Models.EvaluationForm;
    Layout = null; // No layout for printing
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نتائج التقييم - @(evaluationForm?.Title ?? "غير محدد")</title>
    <style>
        @* Print-friendly styles *@
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: black;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        
        .header h2 {
            margin: 10px 0 0 0;
            color: #666;
            font-size: 18px;
        }
        
        .info-section {
            margin-bottom: 30px;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .info-table td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        
        .info-table td:first-child {
            font-weight: bold;
            background-color: #f8f9fa;
            width: 30%;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .results-table th,
        .results-table td {
            padding: 10px;
            border: 1px solid #333;
            text-align: center;
        }
        
        .results-table th {
            background-color: #333;
            color: white;
            font-weight: bold;
        }
        
        .results-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .rank-1 { background-color: #fff3cd !important; }
        .rank-2 { background-color: #e2e3e5 !important; }
        .rank-3 { background-color: #f8d7da !important; }
        
        .rank-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .rank-1 .rank-badge { background-color: #ffc107; color: #000; }
        .rank-2 .rank-badge { background-color: #6c757d; color: #fff; }
        .rank-3 .rank-badge { background-color: #dc3545; color: #fff; }
        
        .details-section {
            margin-top: 40px;
            page-break-before: always;
        }
        
        .candidate-details {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .candidate-details h3 {
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .details-table th,
        .details-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .details-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        
        @@media print {
            body { margin: 0; }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>نتائج التقييم</h1>
        <h2>@(evaluationForm?.Title ?? "غير محدد")</h2>
        <p>تاريخ الطباعة: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</p>
    </div>

    <div class="info-section">
        <table class="info-table">
            <tr>
                <td>اسم النموذج</td>
                <td>@(evaluationForm?.Title ?? "غير محدد")</td>
            </tr>
            <tr>
                <td>اللجنة</td>
                <td>-</td>
            </tr>
            <tr>
                <td>عدد المرشحين</td>
                <td>@Model.Count</td>
            </tr>
            <tr>
                <td>أعلى درجة</td>
                <td>@(Model.Any() ? Model.Max(r => r.TotalScore ?? 0).ToString("F2") : "0.00")</td>
            </tr>
            <tr>
                <td>المعدل العام</td>
                <td>@(Model.Any() ? Model.Where(r => r.TotalScore.HasValue).Average(r => r.TotalScore!.Value).ToString("F2") : "0.00")</td>
            </tr>
            <tr>
                <td>أقل درجة</td>
                <td>@(Model.Any() ? Model.Where(r => r.TotalScore.HasValue).Min(r => r.TotalScore!.Value).ToString("F2") : "0.00")</td>
            </tr>
        </table>
    </div>

    @if (Model.Any())
    {
        <div class="results-section">
            <h3>ترتيب النتائج</h3>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>الترتيب</th>
                        <th>الاسم</th>
                        <th>الرتبة</th>
                        <th>القاعدة الجوية</th>
                        <th>الدرجة النهائية</th>
                        <th>المجموع الموزون</th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.Count; i++)
                    {
                        var result = Model[i];
                        var rank = i + 1;
                        var rankClass = rank <= 3 ? $"rank-{rank}" : "";
                        
                        <tr class="@rankClass">
                            <td>
                                <span class="rank-badge">@rank</span>
                            </td>
                            <td>@result.CandidateName</td>
                            <td>@result.RankName</td>
                            <td>@result.AirbaseName</td>
                            <td><strong>@(result.TotalScore?.ToString("F2") ?? "-")</strong></td>
                            <td>@(result.TotalScore?.ToString("F2") ?? "-")</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        <div class="details-section">
            <h3>تفاصيل تقييم كل مرشح</h3>
            
            @for (int i = 0; i < Model.Count; i++)
            {
                var result = Model[i];
                <div class="candidate-details">
                    <h3>@(i + 1). @result.CandidateName</h3>
                    
                    <table class="info-table">
                        <tr>
                            <td>الاسم</td>
                            <td>@result.CandidateName</td>
                        </tr>
                        <tr>
                            <td>الرتبة</td>
                            <td>@result.RankName</td>
                        </tr>
                        <tr>
                            <td>القاعدة الجوية</td>
                            <td>@result.AirbaseName</td>
                        </tr>
                        <tr>
                            <td>الدرجة النهائية</td>
                            <td><strong>@(result.TotalScore?.ToString("F2") ?? "-")</strong></td>
                        </tr>
                        <tr>
                            <td>المجموع</td>
                            <td>@(result.TotalScore?.ToString("F2") ?? "-")</td>
                        </tr>

                    </table>
                    
                    <h4>تفاصيل المعايير</h4>
                    <table class="details-table">
                        <thead>
                            <tr>
                                <th>المعيار</th>
                                <th>الدرجة</th>
                                <th>الدرجة القصوى</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @* Removed EvaluationFormItems as it's not available in the new model *@
                            <tr>
                                <td colspan="4" class="text-center">تفاصيل المعايير غير متوفرة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            }
        </div>
    }
    else
    {
        <div class="no-results">
            <p>لا توجد نتائج تقييم مكتملة لعرضها.</p>
        </div>
    }

    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام تقييم المرشحين - @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</p>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html> 
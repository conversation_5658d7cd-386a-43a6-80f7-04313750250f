@model RafoEvaluation.ViewModels.CandidateGradesListViewModel
@{
    ViewData["Title"] = "درجات المرشحين";
    ViewData["ActivePage"] = "CandidateGrades";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        درجات المرشحين
                    </h2>
                    <p class="text-muted mb-0">عرض وتتبع درجات المرشحين في التقييمات</p>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info me-2">
                        <i class="fas fa-user-tie me-1"></i>
                        @Model.CurrentUserRole
                    </span>
                    <span class="badge bg-success">
                        <i class="fas fa-users me-1"></i>
                        @Model.TotalCount مرشح
                    </span>
                </div>
            </div>
        </div>
    </div>

    @if (!Model.CanViewGrades)
    {
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> ليس لديك صلاحية لعرض درجات المرشحين. يرجى التواصل مع المدير.
        </div>
    }

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="searchTerm" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="searchTerm" name="searchTerm" 
                                   value="@Model.SearchTerm" placeholder="اسم المرشح أو رقم الخدمة أو اللجنة">
                        </div>
                        <div class="col-md-3">
                            <label for="committeeId" class="form-label">اللجنة</label>
                            <select class="form-select" id="committeeId" name="committeeId">
                                <option value="">جميع اللجان</option>
                                @foreach (var committee in Model.AvailableCommittees)
                                {
                                    <option value="@committee.CommitteeId" selected="@(Model.CommitteeId == committee.CommitteeId)">
                                        @committee.CommitteeName
                                    </option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">حالة التقييم</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="1" selected="@(Model.Status == EvaluationStatus.Pending)">في الانتظار</option>
                                <option value="2" selected="@(Model.Status == EvaluationStatus.InProgress)">قيد التقييم</option>
                                <option value="3" selected="@(Model.Status == EvaluationStatus.Completed)">مكتمل</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <div class="d-flex gap-2 w-100">
                                <button type="submit" class="btn btn-primary flex-fill">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    <div class="row">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        نتائج البحث (@Model.TotalCount مرشح)
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if (!Model.Candidates.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد نتائج</h5>
                            <p class="text-muted">جرب تغيير معايير البحث</p>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table id="candidateGradesTable" class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>المرشح</th>
                                        <th>اللجنة</th>
                                        <th>حالة التقييم</th>
                                        <th>الدرجة الإجمالية</th>
                                        <th>عدد المقيمين</th>
                                        <th>تاريخ التقييم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var candidate in Model.Candidates)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">@candidate.CandidateName</h6>
                                                        <small class="text-muted">
                                                            @candidate.ServiceNumber - @candidate.RankName
                                                        </small>
                                                        <br>
                                                        <span class="badge bg-info">@candidate.CategoryName</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>@candidate.CommitteeName</strong>
                                                    <br>
                                                    <small class="text-muted">@candidate.EvaluationFormTitle</small>
                                                </div>
                                            </td>
                                            <td>
                                                @{
                                                    var statusClass = candidate.Status switch
                                                    {
                                                        EvaluationStatus.Pending => "bg-warning",
                                                        EvaluationStatus.InProgress => "bg-info",
                                                        EvaluationStatus.Completed => "bg-success",
                                                        _ => "bg-secondary"
                                                    };
                                                    var statusText = candidate.Status switch
                                                    {
                                                        EvaluationStatus.Pending => "في الانتظار",
                                                        EvaluationStatus.InProgress => "قيد التقييم",
                                                        EvaluationStatus.Completed => "مكتمل",
                                                        _ => "غير محدد"
                                                    };
                                                }
                                                <span class="badge @statusClass">@statusText</span>
                                            </td>
                                            <td>
                                                @if (candidate.TotalScore.HasValue)
                                                {
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress flex-fill me-2" style="height: 8px;">
                                                            <div class="progress-bar bg-success" style="width: @(candidate.TotalScore.Value)%"></div>
                                                        </div>
                                                        <span class="fw-bold">@candidate.TotalScore.Value.ToString("F1")%</span>
                                                    </div>
                                                    @if (candidate.AverageScore.HasValue)
                                                    {
                                                        <small class="text-muted">متوسط: @candidate.AverageScore.Value.ToString("F1")%</small>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <div class="fw-bold">@candidate.EvaluatorCount</div>
                                                    <small class="text-muted">
                                                        حاضر: @candidate.PresentEvaluatorCount | غائب: @candidate.AbsentEvaluatorCount
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                @if (candidate.CompletedAt.HasValue)
                                                {
                                                    <div>
                                                        <strong>@candidate.CompletedAt.Value.ToString("dd/MM/yyyy")</strong>
                                                        <br>
                                                        <small class="text-muted">@candidate.CompletedAt.Value.ToString("HH:mm")</small>
                                                    </div>
                                                }
                                                else if (candidate.StartedAt.HasValue)
                                                {
                                                    <div>
                                                        <strong>@candidate.StartedAt.Value.ToString("dd/MM/yyyy")</strong>
                                                        <br>
                                                        <small class="text-muted">بدأ في @candidate.StartedAt.Value.ToString("HH:mm")</small>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">لم يبدأ بعد</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = candidate.CandidateEvaluationId })" 
                                                       class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="@Url.Action("Print", new { id = candidate.CandidateEvaluationId })" 
                                                       class="btn btn-sm btn-outline-secondary" title="طباعة" target="_blank">
                                                        <i class="fas fa-print"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Evaluators Table -->
    @if (Model.Candidates.Any(c => c.EvaluatorScores.Any()))
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i>
                            جدول المقيمين والدرجات التفصيلية
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table id="evaluatorsTable" class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>المرشح</th>
                                        <th>اللجنة</th>
                                        <th>المقيم</th>
                                        <th>الدور في اللجنة</th>
                                        <th>الدرجة الإجمالية</th>
                                        <th>الحضور</th>
                                        <th>تاريخ التقييم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var candidate in Model.Candidates.Where(c => c.EvaluatorScores.Any()))
                                    {
                                        @foreach (var evaluator in candidate.EvaluatorScores)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0">@candidate.CandidateName</h6>
                                                            <small class="text-muted">
                                                                @candidate.ServiceNumber - @candidate.RankName
                                                            </small>
                                                            <br>
                                                            <span class="badge bg-info">@candidate.CategoryName</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>@candidate.CommitteeName</strong>
                                                        <br>
                                                        <small class="text-muted">@candidate.EvaluationFormTitle</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                            <i class="fas fa-user-tie"></i>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0">@evaluator.EvaluatorName</h6>
                                                            <small class="text-muted">
                                                                @evaluator.EvaluatorServiceNumber - @evaluator.EvaluatorRank
                                                            </small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">@evaluator.CommitteeRole.GetDisplayName()</span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress flex-fill me-2" style="height: 8px;">
                                                            <div class="progress-bar bg-primary" style="width: @(evaluator.TotalScore)%"></div>
                                                        </div>
                                                        <span class="fw-bold">@evaluator.TotalScore.ToString("F1")%</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-@(evaluator.IsPresent ? "success" : "warning")">
                                                        <i class="fas fa-@(evaluator.IsPresent ? "check" : "times") me-1"></i>
                                                        @(evaluator.IsPresent ? "حاضر" : "غائب")
                                                    </span>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>@evaluator.EvaluatedAt.ToString("dd/MM/yyyy")</strong>
                                                        <br>
                                                        <small class="text-muted">@evaluator.EvaluatedAt.ToString("HH:mm")</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="@Url.Action("Details", new { id = candidate.CandidateEvaluationId })" 
                                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        @if (!string.IsNullOrEmpty(evaluator.EvaluatorNotes))
                                                        {
                                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                                    title="ملاحظات المقيم" 
                                                                    data-bs-toggle="tooltip" 
                                                                    data-bs-placement="top" 
                                                                    data-bs-content="@evaluator.EvaluatorNotes">
                                                                <i class="fas fa-comment"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Pagination -->
    @if (Model.TotalPages > 1)
    {
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="صفحات النتائج">
                    <ul class="pagination justify-content-center">
                        @if (Model.PageNumber > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" href="@Url.Action("Index", new { page = Model.PageNumber - 1, searchTerm = Model.SearchTerm, committeeId = Model.CommitteeId, status = Model.Status })">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        }

                        @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                        {
                            <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                                <a class="page-link" href="@Url.Action("Index", new { page = i, searchTerm = Model.SearchTerm, committeeId = Model.CommitteeId, status = Model.Status })">
                                    @i
                                </a>
                            </li>
                        }

                        @if (Model.PageNumber < Model.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" href="@Url.Action("Index", new { page = Model.PageNumber + 1, searchTerm = Model.SearchTerm, committeeId = Model.CommitteeId, status = Model.Status })">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        }
                    </ul>
                </nav>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTable for candidate grades
            $('#candidateGradesTable').DataTable({
                language: {
                    url: '/lib/datatables/js/ar.json'
                },
                responsive: true,
                pageLength: 10,
                order: [[5, 'desc']], // Sort by evaluation date
                columnDefs: [
                    { orderable: false, targets: [6] } // Actions column
                ]
            });

            // Initialize DataTable for evaluators
            $('#evaluatorsTable').DataTable({
                language: {
                    url: '/lib/datatables/js/ar.json'
                },
                responsive: true,
                pageLength: 15,
                order: [[6, 'desc']], // Sort by evaluation date
                columnDefs: [
                    { orderable: false, targets: [7] } // Actions column
                ]
            });

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
}

<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem;
    }
    
    .btn-group .btn:not(:last-child) {
        margin-left: 0.25rem;
    }

    /* DataTables customization */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_info {
        margin-top: 1rem;
    }

    .dataTables_wrapper .dataTables_paginate {
        margin-top: 1rem;
    }

    /* Responsive table improvements */
    .table-responsive {
        border: none;
    }

    /* Card improvements */
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .card-header {
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }
</style> 
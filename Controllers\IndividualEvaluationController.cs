using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.ViewModels;
using Microsoft.AspNetCore.Authorization;
using RafoEvaluation.Services;
using Microsoft.Extensions.Logging;

namespace RafoEvaluation.Controllers
{
    [Authorize]
    public class IndividualEvaluationController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthenticationService _authService;
        private readonly ILogger<IndividualEvaluationController> _logger;

        public IndividualEvaluationController(ApplicationDbContext context, IAuthenticationService authService, ILogger<IndividualEvaluationController> logger)
        {
            _context = context;
            _authService = authService;
            _logger = logger;
        }

        // GET: IndividualEvaluation/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var individualEvaluation = await _context.IndividualEvaluations
                .Include(ie => ie.CandidateEvaluation)
                    .ThenInclude(ce => ce.Candidate)
                        .ThenInclude(c => c.Rank)
                .Include(ie => ie.CandidateEvaluation)
                    .ThenInclude(ce => ce.Candidate)
                        .ThenInclude(c => c.Category)
                .Include(ie => ie.CandidateEvaluation)
                    .ThenInclude(ce => ce.Committee)
                .Include(ie => ie.CandidateEvaluation)
                    .ThenInclude(ce => ce.EvaluationForm)
                .Include(ie => ie.Evaluator)
                    .ThenInclude(e => e.Rank)
                .Include(ie => ie.CriteriaScores)
                    .ThenInclude(cs => cs.EvaluationFormItem)
                .FirstOrDefaultAsync(ie => ie.IndividualEvaluationId == id);

            if (individualEvaluation == null)
            {
                return NotFound();
            }

            var viewModel = new IndividualEvaluationDetailsViewModel
            {
                IndividualEvaluationId = individualEvaluation.IndividualEvaluationId,
                CandidateEvaluationId = individualEvaluation.CandidateEvaluationId,
                EvaluatorId = individualEvaluation.EvaluatorId,
                EvaluatorName = individualEvaluation.Evaluator.FullName ?? individualEvaluation.Evaluator.ServiceNumber,
                EvaluatorRank = individualEvaluation.Evaluator.Rank?.RankName ?? "غير محدد",
                EvaluatorServiceNumber = individualEvaluation.Evaluator.ServiceNumber,
                TotalScore = individualEvaluation.TotalScore,
                EvaluatorNotes = individualEvaluation.EvaluatorNotes,
                IsPresent = individualEvaluation.IsPresent,
                EvaluatedAt = individualEvaluation.EvaluatedAt,
                UpdatedAt = individualEvaluation.UpdatedAt,
                
                // Candidate Information
                CandidateName = individualEvaluation.CandidateEvaluation.Candidate.FullName,
                CandidateServiceNumber = individualEvaluation.CandidateEvaluation.Candidate.ServiceNumber,
                CandidateRank = individualEvaluation.CandidateEvaluation.Candidate.Rank?.RankName ?? "غير محدد",
                CandidateCategory = individualEvaluation.CandidateEvaluation.Candidate.Category?.CategoryName ?? "غير محدد",
                
                // Committee Information
                CommitteeName = individualEvaluation.CandidateEvaluation.Committee.CommitteeName,
                EvaluationFormTitle = individualEvaluation.CandidateEvaluation.EvaluationForm.Title,
                
                // Criteria Scores
                CriteriaScores = individualEvaluation.CriteriaScores.Select(cs => new CriteriaScoreViewModel
                {
                    CriteriaId = cs.EvaluationFormItem.EvaluationFormItemId,
                    CriteriaName = cs.EvaluationFormItem.Criteria,
                    CriteriaDescription = cs.EvaluationFormItem.Description,
                    MaxScore = cs.EvaluationFormItem.MaxScore,
                    Score = cs.Score,
                    Notes = null // IndividualEvaluationCriteria doesn't have Notes property
                }).OrderBy(cs => cs.CriteriaName).ToList()
            };

            return View(viewModel);
        }

        // GET: IndividualEvaluation/Index
        public async Task<IActionResult> Index(int candidateEvaluationId)
        {
            var serviceNumber = User.FindFirst("ServiceNumber")?.Value;
            var isAdmin = User.IsInRole("Admin");
            
            var candidateEvaluation = await _context.CandidateEvaluations
                .Include(ce => ce.Candidate)
                    .ThenInclude(c => c.Rank)
                .Include(ce => ce.Candidate)
                    .ThenInclude(c => c.Category)
                .Include(ce => ce.Candidate)
                    .ThenInclude(c => c.Airbase)
                .Include(ce => ce.EvaluationForm)
                .Include(ce => ce.Committee)
                .Include(ce => ce.IndividualEvaluations)
                    .ThenInclude(ie => ie.Evaluator)
                        .ThenInclude(e => e.Rank)
                .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == candidateEvaluationId);

            if (candidateEvaluation == null)
            {
                return NotFound();
            }

            // Check if user has access to this evaluation's committee
            if (!isAdmin && !string.IsNullOrEmpty(serviceNumber))
            {
                var isMember = await _authService.IsUserInCommitteeAsync(serviceNumber, candidateEvaluation.CommitteeId);
                if (!isMember)
                {
                    TempData["ErrorMessage"] = "ليس لديك صلاحية للوصول إلى هذا التقييم.";
                    return RedirectToAction("Index", "CommitteeEvaluation");
                }
            }

            // Get committee members for this evaluation
            var committeeMembers = await _context.CommitteeMembers
                .Include(cm => cm.User)
                    .ThenInclude(u => u.Rank)
                .Where(cm => cm.CommitteeId == candidateEvaluation.CommitteeId && cm.IsActive)
                .OrderBy(cm => cm.Role)
                .ThenBy(cm => cm.ServiceNumber)
                .ToListAsync();

            // Calculate evaluation statistics - from ACTIVE committee members only
            var individualEvaluations = candidateEvaluation.IndividualEvaluations.ToList();
            
            // Filter evaluations from ACTIVE committee members
            var activeCommitteeMembers = committeeMembers
                .Where(cm => cm.IsActive)
                .Select(cm => cm.User?.UserId)
                .Where(userId => userId.HasValue)
                .Select(userId => userId!.Value)
                .ToHashSet();

            var activeEvaluations = individualEvaluations
                .Where(ie => ie.IsPresent && activeCommitteeMembers.Contains(ie.EvaluatorId))
                .ToList();
            
            var averageScore = activeEvaluations.Any() ? activeEvaluations.Average(ie => ie.TotalScore) : 0;
            var maxScore = activeEvaluations.Any() ? activeEvaluations.Max(ie => ie.TotalScore) : 0;
            var minScore = activeEvaluations.Any() ? activeEvaluations.Min(ie => ie.TotalScore) : 0;
            
            // Calculate standard deviation
            decimal? standardDeviation = null;
            if (activeEvaluations.Count > 1)
            {
                var mean = activeEvaluations.Average(ie => ie.TotalScore);
                var variance = activeEvaluations.Average(ie => (ie.TotalScore - mean) * (ie.TotalScore - mean));
                standardDeviation = (decimal)Math.Sqrt((double)variance);
            }

            var viewModel = new MultiEvaluatorEvaluationViewModel
            {
                CandidateEvaluationId = candidateEvaluation.CandidateEvaluationId,
                CandidateId = candidateEvaluation.CandidateId,
                CommitteeId = candidateEvaluation.CommitteeId,
                CandidateName = candidateEvaluation.Candidate.FullName,
                ServiceNumber = candidateEvaluation.Candidate.ServiceNumber,
                RankName = candidateEvaluation.Candidate.Rank?.RankName ?? "غير محدد",
                CategoryName = candidateEvaluation.Candidate.Category?.CategoryName ?? "غير محدد",
                AirbaseName = candidateEvaluation.Candidate.Airbase?.AirbaseName ?? "غير محدد",
                EvaluationFormId = candidateEvaluation.EvaluationFormId,
                EvaluationFormTitle = candidateEvaluation.EvaluationForm.Title,
                CommitteeName = candidateEvaluation.Committee.CommitteeName,
                Status = candidateEvaluation.Status,
                AverageScore = averageScore,
                TotalScore = candidateEvaluation.TotalScore,
                PresentEvaluatorCount = activeEvaluations.Count,
                AbsentEvaluatorCount = individualEvaluations.Count - activeEvaluations.Count,
                TotalEvaluatorCount = individualEvaluations.Count,
                StartedAt = candidateEvaluation.StartedAt,
                CompletedAt = candidateEvaluation.CompletedAt,
                CreatedAt = candidateEvaluation.CreatedAt,
                GeneralNotes = candidateEvaluation.GeneralNotes,
                MaxIndividualScore = maxScore,
                MinIndividualScore = minScore,
                IndividualEvaluations = individualEvaluations.Select(ie => new IndividualEvaluationViewModel
                {
                    IndividualEvaluationId = ie.IndividualEvaluationId,
                    CandidateEvaluationId = ie.CandidateEvaluationId,
                    EvaluatorId = ie.EvaluatorId,
                    EvaluatorName = ie.Evaluator.FullName ?? ie.Evaluator.ServiceNumber,
                    EvaluatorRank = ie.Evaluator.Rank?.RankName ?? "غير محدد",
                    TotalScore = ie.TotalScore,
                    EvaluatorNotes = ie.EvaluatorNotes,
                    IsPresent = ie.IsPresent,
                    EvaluatedAt = ie.EvaluatedAt,
                    UpdatedAt = ie.UpdatedAt
                }).OrderByDescending(ie => ie.EvaluatedAt).ToList()
            };

            ViewBag.CommitteeMembers = committeeMembers.Select(cm => new MultiEvaluatorCommitteeMemberViewModel
            {
                UserId = cm.User?.UserId ?? 0,
                ServiceNumber = cm.ServiceNumber,
                FullName = cm.User?.FullName ?? cm.ServiceNumber,
                RankName = cm.User?.Rank?.RankName ?? "غير محدد",
                Role = cm.Role,
                IsActive = cm.IsActive,
                HasEvaluated = cm.User != null && individualEvaluations.Any(ie => ie.EvaluatorId == cm.User.UserId),
                LastEvaluationDate = cm.User != null ? individualEvaluations.Where(ie => ie.EvaluatorId == cm.User.UserId).Max(ie => ie.EvaluatedAt) : null,
                EvaluationsCount = cm.User != null ? individualEvaluations.Count(ie => ie.EvaluatorId == cm.User.UserId) : 0
            }).ToList();
            ViewBag.StandardDeviation = standardDeviation;
            ViewBag.TotalMaxScore = candidateEvaluation.EvaluationForm.Items.Sum(i => i.MaxScore);

            return View(viewModel);
        }

        // GET: IndividualEvaluation/Create
        public async Task<IActionResult> Create(int candidateEvaluationId)
        {
            var serviceNumber = User.FindFirst("ServiceNumber")?.Value;
            var isAdmin = User.IsInRole("Admin");
            
            var candidateEvaluation = await _context.CandidateEvaluations
                .Include(ce => ce.Candidate)
                .Include(ce => ce.Committee)
                .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == candidateEvaluationId);

            if (candidateEvaluation == null)
            {
                return NotFound();
            }

            // Check if user has access to this evaluation's committee
            if (!isAdmin && !string.IsNullOrEmpty(serviceNumber))
            {
                var canEvaluate = await _authService.CanUserEvaluateInCommitteeAsync(serviceNumber, candidateEvaluation.CommitteeId);
                if (!canEvaluate)
                {
                    TempData["ErrorMessage"] = "ليس لديك صلاحية لإضافة تقييم لهذا المرشح.";
                    return RedirectToAction("Index", "CommitteeEvaluation");
                }
            }

            // Check if user already evaluated this candidate
            var currentUserId = await _context.Users
                .Where(u => u.ServiceNumber == serviceNumber)
                .Select(u => u.UserId)
                .FirstOrDefaultAsync();

            if (currentUserId > 0)
            {
                var existingEvaluation = await _context.IndividualEvaluations
                    .FirstOrDefaultAsync(ie => ie.CandidateEvaluationId == candidateEvaluationId && 
                                              ie.EvaluatorId == currentUserId);

                if (existingEvaluation != null)
                {
                    TempData["WarningMessage"] = "لقد قمت بتقييم هذا المرشح مسبقاً.";
                    return RedirectToAction(nameof(Index), new { candidateEvaluationId });
                }
            }

            var viewModel = new IndividualEvaluationCreateViewModel
            {
                CandidateEvaluationId = candidateEvaluationId,
                EvaluatorId = currentUserId
            };

            ViewBag.CandidateName = candidateEvaluation.Candidate.FullName;
            ViewBag.CommitteeName = candidateEvaluation.Committee.CommitteeName;

            return View(viewModel);
        }

        // POST: IndividualEvaluation/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(IndividualEvaluationCreateViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var serviceNumber = User.FindFirst("ServiceNumber")?.Value;
                var isAdmin = User.IsInRole("Admin");
                
                var candidateEvaluation = await _context.CandidateEvaluations
                    .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == viewModel.CandidateEvaluationId);

                if (candidateEvaluation == null)
                {
                    return NotFound();
                }

                // Check if user has access to this evaluation's committee
                if (!isAdmin && !string.IsNullOrEmpty(serviceNumber))
                {
                    var canEvaluate = await _authService.CanUserEvaluateInCommitteeAsync(serviceNumber, candidateEvaluation.CommitteeId);
                    if (!canEvaluate)
                    {
                        TempData["ErrorMessage"] = "ليس لديك صلاحية لإضافة تقييم لهذا المرشح.";
                        return RedirectToAction("Index", "CommitteeEvaluation");
                    }
                }

                // Check if user already evaluated this candidate
                var currentUserId = await _context.Users
                    .Where(u => u.ServiceNumber == serviceNumber)
                    .Select(u => u.UserId)
                    .FirstOrDefaultAsync();

                if (currentUserId > 0)
                {
                    var existingEvaluation = await _context.IndividualEvaluations
                        .FirstOrDefaultAsync(ie => ie.CandidateEvaluationId == viewModel.CandidateEvaluationId && 
                                                  ie.EvaluatorId == currentUserId);

                    if (existingEvaluation != null)
                    {
                        TempData["WarningMessage"] = "لقد قمت بتقييم هذا المرشح مسبقاً.";
                        return RedirectToAction(nameof(Index), new { candidateEvaluationId = viewModel.CandidateEvaluationId });
                    }
                }

                var individualEvaluation = new IndividualEvaluation
                {
                    CandidateEvaluationId = viewModel.CandidateEvaluationId,
                    EvaluatorId = currentUserId,
                    TotalScore = viewModel.TotalScore,
                    EvaluatorNotes = viewModel.EvaluatorNotes,
                    IsPresent = viewModel.IsPresent,
                    EvaluatedAt = DateTime.UtcNow
                };

                _context.IndividualEvaluations.Add(individualEvaluation);

                // Update candidate evaluation statistics
                await UpdateCandidateEvaluationStatistics(viewModel.CandidateEvaluationId);

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم إضافة تقييمك بنجاح.";
                return RedirectToAction(nameof(Index), new { candidateEvaluationId = viewModel.CandidateEvaluationId });
            }

            return View(viewModel);
        }

        // GET: IndividualEvaluation/Edit
        public async Task<IActionResult> Edit(int id)
        {
            var serviceNumber = User.FindFirst("ServiceNumber")?.Value;
            var isAdmin = User.IsInRole("Admin");
            
            var individualEvaluation = await _context.IndividualEvaluations
                .Include(ie => ie.CandidateEvaluation)
                    .ThenInclude(ce => ce.Candidate)
                .Include(ie => ie.CandidateEvaluation)
                    .ThenInclude(ce => ce.Committee)
                .Include(ie => ie.Evaluator)
                .FirstOrDefaultAsync(ie => ie.IndividualEvaluationId == id);

            if (individualEvaluation == null)
            {
                return NotFound();
            }

            // Check if user has access to this evaluation's committee
            if (!isAdmin && !string.IsNullOrEmpty(serviceNumber))
            {
                var isMember = await _authService.IsUserInCommitteeAsync(serviceNumber, individualEvaluation.CandidateEvaluation.CommitteeId);
                if (!isMember)
                {
                    TempData["ErrorMessage"] = "ليس لديك صلاحية لتعديل هذا التقييم.";
                    return RedirectToAction("Index", "CommitteeEvaluation");
                }
            }

            // Check if user is the evaluator or admin
            var currentUserId = await _context.Users
                .Where(u => u.ServiceNumber == serviceNumber)
                .Select(u => u.UserId)
                .FirstOrDefaultAsync();

            if (!isAdmin && individualEvaluation.EvaluatorId != currentUserId)
            {
                TempData["ErrorMessage"] = "يمكنك فقط تعديل تقييماتك الخاصة.";
                return RedirectToAction(nameof(Index), new { candidateEvaluationId = individualEvaluation.CandidateEvaluationId });
            }

            var viewModel = new IndividualEvaluationEditViewModel
            {
                IndividualEvaluationId = individualEvaluation.IndividualEvaluationId,
                TotalScore = individualEvaluation.TotalScore,
                EvaluatorNotes = individualEvaluation.EvaluatorNotes,
                IsPresent = individualEvaluation.IsPresent
            };

            ViewBag.CandidateName = individualEvaluation.CandidateEvaluation.Candidate.FullName;
            ViewBag.CommitteeName = individualEvaluation.CandidateEvaluation.Committee.CommitteeName;
            ViewBag.EvaluatorName = individualEvaluation.Evaluator.FullName ?? individualEvaluation.Evaluator.ServiceNumber;

            return View(viewModel);
        }

        // POST: IndividualEvaluation/Edit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, IndividualEvaluationEditViewModel viewModel)
        {
            if (id != viewModel.IndividualEvaluationId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var serviceNumber = User.FindFirst("ServiceNumber")?.Value;
                var isAdmin = User.IsInRole("Admin");
                
                var individualEvaluation = await _context.IndividualEvaluations
                    .Include(ie => ie.CandidateEvaluation)
                    .FirstOrDefaultAsync(ie => ie.IndividualEvaluationId == id);

                if (individualEvaluation == null)
                {
                    return NotFound();
                }

                // Check if user has access to this evaluation's committee
                if (!isAdmin && !string.IsNullOrEmpty(serviceNumber))
                {
                    var isMember = await _authService.IsUserInCommitteeAsync(serviceNumber, individualEvaluation.CandidateEvaluation.CommitteeId);
                    if (!isMember)
                    {
                        TempData["ErrorMessage"] = "ليس لديك صلاحية لتعديل هذا التقييم.";
                        return RedirectToAction("Index", "CommitteeEvaluation");
                    }
                }

                // Check if user is the evaluator or admin
                var currentUserId = await _context.Users
                    .Where(u => u.ServiceNumber == serviceNumber)
                    .Select(u => u.UserId)
                    .FirstOrDefaultAsync();

                if (!isAdmin && individualEvaluation.EvaluatorId != currentUserId)
                {
                    TempData["ErrorMessage"] = "يمكنك فقط تعديل تقييماتك الخاصة.";
                    return RedirectToAction(nameof(Index), new { candidateEvaluationId = individualEvaluation.CandidateEvaluationId });
                }

                individualEvaluation.TotalScore = viewModel.TotalScore;
                individualEvaluation.EvaluatorNotes = viewModel.EvaluatorNotes;
                individualEvaluation.IsPresent = viewModel.IsPresent;
                individualEvaluation.UpdatedAt = DateTime.UtcNow;

                // Update candidate evaluation statistics
                await UpdateCandidateEvaluationStatistics(individualEvaluation.CandidateEvaluationId);

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم تحديث التقييم بنجاح.";
                return RedirectToAction(nameof(Index), new { candidateEvaluationId = individualEvaluation.CandidateEvaluationId });
            }

            return View(viewModel);
        }

        // POST: IndividualEvaluation/Delete
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var serviceNumber = User.FindFirst("ServiceNumber")?.Value;
            var isAdmin = User.IsInRole("Admin");
            
            var individualEvaluation = await _context.IndividualEvaluations
                .Include(ie => ie.CandidateEvaluation)
                .FirstOrDefaultAsync(ie => ie.IndividualEvaluationId == id);

            if (individualEvaluation == null)
            {
                return NotFound();
            }

            // Check if user has access to this evaluation's committee
            if (!isAdmin && !string.IsNullOrEmpty(serviceNumber))
            {
                var isMember = await _authService.IsUserInCommitteeAsync(serviceNumber, individualEvaluation.CandidateEvaluation.CommitteeId);
                if (!isMember)
                {
                    TempData["ErrorMessage"] = "ليس لديك صلاحية لحذف هذا التقييم.";
                    return RedirectToAction("Index", "CommitteeEvaluation");
                }
            }

            // Check if user is the evaluator or admin
            var currentUserId = await _context.Users
                .Where(u => u.ServiceNumber == serviceNumber)
                .Select(u => u.UserId)
                .FirstOrDefaultAsync();

            if (!isAdmin && individualEvaluation.EvaluatorId != currentUserId)
            {
                TempData["ErrorMessage"] = "يمكنك فقط حذف تقييماتك الخاصة.";
                return RedirectToAction(nameof(Index), new { candidateEvaluationId = individualEvaluation.CandidateEvaluationId });
            }

            var candidateEvaluationId = individualEvaluation.CandidateEvaluationId;
            
            _context.IndividualEvaluations.Remove(individualEvaluation);

            // Update candidate evaluation statistics
            await UpdateCandidateEvaluationStatistics(candidateEvaluationId);

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "تم حذف التقييم بنجاح.";
            return RedirectToAction(nameof(Index), new { candidateEvaluationId });
        }

        // Helper method to update candidate evaluation statistics
        private async Task UpdateCandidateEvaluationStatistics(int candidateEvaluationId)
        {
            var candidateEvaluation = await _context.CandidateEvaluations
                .Include(ce => ce.IndividualEvaluations)
                    .ThenInclude(ie => ie.Evaluator)
                .Include(ce => ce.Committee)
                    .ThenInclude(c => c.CommitteeMembers.Where(cm => cm.IsActive))
                .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == candidateEvaluationId);

            if (candidateEvaluation != null)
            {
                var individualEvaluations = candidateEvaluation.IndividualEvaluations.ToList();
                
                // Filter evaluations from ACTIVE committee members only
                var activeCommitteeMembers = candidateEvaluation.Committee?.CommitteeMembers
                    .Where(cm => cm.IsActive)
                    .Select(cm => cm.User?.UserId)
                    .Where(userId => userId.HasValue)
                    .Select(userId => userId!.Value)
                    .ToHashSet() ?? new HashSet<int>();

                var activeEvaluations = individualEvaluations
                    .Where(ie => ie.IsPresent && activeCommitteeMembers.Contains(ie.EvaluatorId))
                    .ToList();

                candidateEvaluation.EvaluatorCount = individualEvaluations.Count;
                candidateEvaluation.PresentEvaluatorCount = activeEvaluations.Count; // Only count active members
                candidateEvaluation.AbsentEvaluatorCount = individualEvaluations.Count - activeEvaluations.Count;

                if (activeEvaluations.Any())
                {
                    candidateEvaluation.AverageScore = activeEvaluations.Average(ie => ie.TotalScore);
                    candidateEvaluation.TotalScore = activeEvaluations.Sum(ie => ie.TotalScore);
                    
                    _logger.LogInformation($"Calculated average score: {candidateEvaluation.AverageScore} from {activeEvaluations.Count} active committee members");
                }
                else
                {
                    candidateEvaluation.AverageScore = null;
                    candidateEvaluation.TotalScore = null;
                    
                    _logger.LogInformation("No evaluations from active committee members found");
                }

                // Update status based on active evaluations
                if (activeEvaluations.Count > 0)
                {
                    candidateEvaluation.Status = EvaluationStatus.Completed;
                    candidateEvaluation.CompletedAt = DateTime.UtcNow;
                }
                else if (individualEvaluations.Count > 0)
                {
                    candidateEvaluation.Status = EvaluationStatus.InProgress;
                }
                else
                {
                    candidateEvaluation.Status = EvaluationStatus.Pending;
                }

                candidateEvaluation.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
        }
    }
} 
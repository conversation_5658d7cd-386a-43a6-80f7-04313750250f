# نظام الإشعارات التلقائي - Notifications Auto-Hide System

## نظرة عامة
تم تحديث نظام الإشعارات في التطبيق ليعمل تلقائياً مع جميع الإشعارات الموجودة. الآن جميع الإشعارات (نجاح، خطأ، تحذير، معلومات) ستختفي تلقائياً بعد 5 ثوانٍ.

## الميزات الجديدة

### 1. إخفاء تلقائي
- **مدة العرض**: 5 ثوانٍ
- **انتقال سلس**: fade-out effect لمدة 0.5 ثانية
- **إزالة تلقائية**: من DOM بعد انتهاء الانتقال

### 2. مؤشر بصري
- **شريط العد التنازلي**: شريط أبيض شفاف في أسفل الإشعار
- **حركة متدرجة**: من اليسار إلى اليمين (RTL)
- **مدة 5 ثوانٍ**: يتناقص العرض تدريجياً

### 3. تحسينات المظهر
- **ظلال محسنة**: box-shadow أفضل للإشعارات
- **حواف دائرية**: border-radius محسن
- **أزرار الإغلاق**: تأثيرات hover محسنة

## كيفية العمل

### JavaScript (site.js)
```javascript
function autoHideNotifications() {
    const notifications = document.querySelectorAll('.alert');
    
    notifications.forEach(notification => {
        if (!notification.classList.contains('position-fixed')) {
            notification.classList.add('auto-hide');
            
            setTimeout(() => {
                notification.style.transition = 'opacity 0.5s ease-out';
                notification.style.opacity = '0';
                
                setTimeout(() => {
                    notification.remove();
                }, 500);
            }, 5000);
        }
    });
}
```

### CSS (site.css)
```css
.alert.auto-hide {
    transition: opacity 0.5s ease-out;
    position: relative;
}

.alert.auto-hide::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    animation: countdown 5s linear;
    border-radius: 0 0 0.375rem 0.375rem;
}

@keyframes countdown {
    from { width: 100%; }
    to { width: 0%; }
}
```

## أنواع الإشعارات المدعومة

### 1. إشعارات TempData (تختفي تلقائياً)
- `TempData["SuccessMessage"]` - رسائل النجاح
- `TempData["ErrorMessage"]` - رسائل الخطأ
- `TempData["WarningMessage"]` - رسائل التحذير
- `TempData["InfoMessage"]` - رسائل المعلومات

### 2. إشعارات JavaScript (تختفي تلقائياً بالفعل)
- `showNotification()` - دالة JavaScript
- `Utils.showNotification()` - دالة Utility

### 3. إشعارات ثابتة (لا تختفي تلقائياً)
- الإشعارات التي تحتوي على `position-fixed` class

## الصفحات المدعومة

جميع الصفحات التي تحتوي على إشعارات تدعم النظام الجديد:

- **Candidate/Index.cshtml** - إدارة المرشحين
- **User/Index.cshtml** - إدارة المستخدمين
- **CommitteeEvaluation/SelectCandidates.cshtml** - اختيار المرشحين
- **CandidateCommitteeAssignment/Create.cshtml** - إنشاء تعيينات
- **Reports/DetailedReport.cshtml** - التقارير المفصلة
- **Category/ManageWeights.cshtml** - إدارة الأوزان
- **وغيرها...**

## التخصيص

### تغيير مدة العرض
```javascript
// في site.js، تغيير القيمة من 5000 إلى القيمة المطلوبة (بالميلي ثانية)
setTimeout(() => {
    // إخفاء الإشعار
}, 5000); // 5 ثوانٍ
```

### تغيير مدة الانتقال
```css
/* في site.css، تغيير القيمة من 0.5s إلى القيمة المطلوبة */
.alert.auto-hide {
    transition: opacity 0.5s ease-out; /* 0.5 ثانية */
}
```

## استثناءات

### الإشعارات التي لا تختفي تلقائياً
1. **إشعارات JavaScript الثابتة**: تحتوي على `position-fixed` class
2. **إشعارات التحقق من صحة النماذج**: `asp-validation-summary`
3. **إشعارات المعلومات الثابتة**: مثل الإرشادات والتعليمات

### كيفية إضافة استثناء
```html
<!-- إضافة class "no-auto-hide" لمنع الإخفاء التلقائي -->
<div class="alert alert-info no-auto-hide">
    هذه الرسالة لن تختفي تلقائياً
</div>
```

## استكشاف الأخطاء

### Console Logs
النظام يطبع رسائل في console للمساعدة في استكشاف الأخطاء:

```javascript
console.log(`تم العثور على ${notifications.length} إشعار في الصفحة`);
console.log(`إشعار رقم ${index + 1} سيختفي تلقائياً بعد 5 ثوانٍ`);
console.log(`إخفاء إشعار رقم ${index + 1}`);
console.log(`تم إزالة إشعار رقم ${index + 1} من الصفحة`);
```

### مشاكل شائعة
1. **الإشعارات لا تختفي**: تأكد من عدم وجود أخطاء JavaScript
2. **توقيت غير صحيح**: تحقق من أن site.js محمل بشكل صحيح
3. **مشاكل CSS**: تأكد من أن site.css محمل

## التوافق

- **Bootstrap 5**: متوافق بالكامل
- **RTL Support**: يدعم اللغة العربية والاتجاه من اليمين لليسار
- **Mobile Responsive**: يعمل على جميع أحجام الشاشات
- **Modern Browsers**: يدعم جميع المتصفحات الحديثة

## تاريخ التحديث

- **التاريخ**: يناير 2025
- **الإصدار**: 1.0
- **المطور**: AI Assistant
- **الوصف**: إضافة نظام إخفاء تلقائي للإشعارات

## ملاحظات

- النظام يعمل تلقائياً مع جميع الإشعارات الموجودة
- لا حاجة لتعديل ملفات Razor الموجودة
- يحافظ على جميع الوظائف الحالية
- يحسن تجربة المستخدم بشكل كبير

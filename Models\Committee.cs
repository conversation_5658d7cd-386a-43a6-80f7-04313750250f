using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RafoEvaluation.Models
{
    public class Committee
    {
        [Key]
        public int CommitteeId { get; set; }

        [Required(ErrorMessage = "اسم اللجنة مطلوب")]
        [Display(Name = "اسم اللجنة")]
        [StringLength(100, ErrorMessage = "اسم اللجنة لا يمكن أن يتجاوز 100 حرف")]
        public string CommitteeName { get; set; } = string.Empty;

        [Display(Name = "وصف اللجنة")]
        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        public string? Description { get; set; }

        [Display(Name = "الحالة")]
        public CommitteeStatus Status { get; set; } = CommitteeStatus.Active;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<CommitteeMember> CommitteeMembers { get; set; } = new List<CommitteeMember>();
        public virtual ICollection<CandidateCommitteeAssignment> CandidateCommitteeAssignments { get; set; } = new List<CandidateCommitteeAssignment>();
        public virtual ICollection<CandidateEvaluation> CandidateEvaluations { get; set; } = new List<CandidateEvaluation>();

        // Calculated Properties
        [NotMapped]
        public int ActiveMemberCount => CommitteeMembers?.Count(m => m.IsActive) ?? 0;

        [NotMapped]
        public bool HasChairman => CommitteeMembers?.Any(m => m.IsActive && m.Role == CommitteeMemberRole.رئيس_اللجنة) ?? false;
    }

    public enum CommitteeStatus
    {
        [Display(Name = "مفعلة")]
        Active = 1,
        
        [Display(Name = "غير مفعلة")]
        Inactive = 2
    }
} 
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models.Auth;
using RafoEvaluation.ViewModels;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Authorization;

namespace RafoEvaluation.Controllers
{
    /// <summary>
    /// Controller لإدارة الرتب - Rank Management Controller
    /// يتبع أفضل الممارسات في ASP.NET Core MVC
    /// </summary>
    [Authorize(Roles = "Admin")]
    public class RankController : Controller
    {
        #region Fields & Constructor

        private readonly ApplicationDbContext _context;
        private readonly ILogger<RankController> _logger;

        public RankController(ApplicationDbContext context, ILogger<RankController> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region Index - عرض قائمة الرتب

        /// <summary>
        /// عرض قائمة الرتب مع إحصائيات
        /// GET: /Rank
        /// </summary>
        /// <returns>صفحة قائمة الرتب</returns>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                _logger.LogInformation("بدء جلب قائمة الرتب");

                // جلب جميع الرتب مع الترتيب
                var ranks = await GetRanksAsync();

                _logger.LogInformation("تم جلب {Count} رتبة بنجاح", ranks.Count);
                return View(ranks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب قائمة الرتب");
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب الرتب. يرجى المحاولة مرة أخرى.";
                return View(new List<RankViewModel>());
            }
        }

        #endregion

        #region Create - إنشاء رتبة جديدة

        /// <summary>
        /// عرض نموذج إنشاء رتبة جديدة
        /// GET: /Rank/Create
        /// </summary>
        [HttpGet]
        public IActionResult Create()
        {
            _logger.LogInformation("عرض نموذج إنشاء رتبة جديدة");
            return View(new CreateRankViewModel());
        }

        /// <summary>
        /// معالجة إنشاء رتبة جديدة
        /// POST: /Rank/Create
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateRankViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("نموذج إنشاء الرتبة غير صالح");
                    return View(model);
                }

                // التحقق من عدم تكرار اسم الرتبة
                if (await IsRankNameExistsAsync(model.RankName))
                {
                    ModelState.AddModelError(nameof(model.RankName), "اسم الرتبة موجود مسبقاً");
                    return View(model);
                }

                // إنشاء الرتبة الجديدة
                var rank = CreateRankFromViewModel(model);
                
                _context.Ranks.Add(rank);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء رتبة جديدة: {RankName}", rank.RankName);

                TempData["SuccessMessage"] = $"تم إنشاء الرتبة '{rank.RankName}' بنجاح";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء رتبة جديدة");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء الرتبة. يرجى المحاولة مرة أخرى.";
                return View(model);
            }
        }

        #endregion

        #region Edit - تعديل رتبة

        /// <summary>
        /// عرض نموذج تعديل رتبة
        /// GET: /Rank/Edit/5
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Edit(int? id)
        {
            if (!id.HasValue)
            {
                _logger.LogWarning("محاولة تعديل رتبة بدون معرف");
                return NotFound();
            }

            try
            {
                var rank = await _context.Ranks.FindAsync(id.Value);
                if (rank == null)
                {
                    _logger.LogWarning("لم يتم العثور على الرتبة بالمعرف: {Id}", id.Value);
                    return NotFound();
                }

                var viewModel = CreateEditViewModelFromRank(rank);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب بيانات الرتبة للتعديل: {Id}", id.Value);
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب بيانات الرتبة.";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// معالجة تعديل رتبة
        /// POST: /Rank/Edit/5
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditRankViewModel model)
        {
            if (id != model.RankId)
            {
                _logger.LogWarning("عدم تطابق معرف الرتبة في التعديل");
                return NotFound();
            }

            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                // التحقق من عدم تكرار اسم الرتبة (باستثناء الرتبة الحالية)
                if (await IsRankNameExistsAsync(model.RankName, id))
                {
                    ModelState.AddModelError(nameof(model.RankName), "اسم الرتبة موجود مسبقاً");
                    return View(model);
                }

                var rank = await _context.Ranks.FindAsync(id);
                if (rank == null)
                {
                    return NotFound();
                }

                // تحديث بيانات الرتبة
                UpdateRankFromViewModel(rank, model);
                
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الرتبة: {RankName}", rank.RankName);
                TempData["SuccessMessage"] = $"تم تحديث الرتبة '{rank.RankName}' بنجاح";
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الرتبة: {Id}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث الرتبة. يرجى المحاولة مرة أخرى.";
                return View(model);
            }
        }

        #endregion

        #region Details - عرض تفاصيل الرتبة

        /// <summary>
        /// عرض تفاصيل رتبة
        /// GET: /Rank/Details/5
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Details(int? id)
        {
            if (!id.HasValue)
            {
                return NotFound();
            }

            try
            {
                var rank = await _context.Ranks.FindAsync(id.Value);
                if (rank == null)
                {
                    return NotFound();
                }

                var viewModel = CreateViewModelFromRank(rank);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تفاصيل الرتبة: {Id}", id.Value);
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب تفاصيل الرتبة.";
                return RedirectToAction(nameof(Index));
            }
        }

        #endregion

        #region Delete - حذف رتبة

        /// <summary>
        /// عرض تأكيد حذف رتبة
        /// GET: /Rank/Delete/5
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Delete(int? id)
        {
            if (!id.HasValue)
            {
                return NotFound();
            }

            try
            {
                var rank = await _context.Ranks.FindAsync(id.Value);
                if (rank == null)
                {
                    return NotFound();
                }

                var viewModel = CreateViewModelFromRank(rank);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب بيانات الرتبة للحذف: {Id}", id.Value);
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب بيانات الرتبة.";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// تأكيد حذف رتبة
        /// POST: /Rank/Delete/5
        /// </summary>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var rank = await _context.Ranks.FindAsync(id);
                if (rank == null)
                {
                    return NotFound();
                }

                // التحقق من عدم وجود مستخدمين مرتبطين بالرتبة
                var hasUsers = await _context.Users.AnyAsync(u => u.RankId == id);
                if (hasUsers)
                {
                    TempData["ErrorMessage"] = "لا يمكن حذف الرتبة لوجود مستخدمين مرتبطين بها.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Ranks.Remove(rank);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف الرتبة: {RankName}", rank.RankName);
                TempData["SuccessMessage"] = $"تم حذف الرتبة '{rank.RankName}' بنجاح";
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الرتبة: {Id}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف الرتبة. يرجى المحاولة مرة أخرى.";
                return RedirectToAction(nameof(Index));
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task<List<RankViewModel>> GetRanksAsync()
        {
            return await _context.Ranks
                .OrderBy(r => r.DisplayOrder)
                .ThenBy(r => r.RankName)
                .Select(r => CreateViewModelFromRank(r))
                .ToListAsync();
        }

        private async Task<bool> IsRankNameExistsAsync(string rankName, int? excludeId = null)
        {
            return await _context.Ranks
                .Where(r => excludeId == null || r.RankId != excludeId.Value)
                .AnyAsync(r => r.RankName == rankName);
        }

        private static Rank CreateRankFromViewModel(CreateRankViewModel model)
        {
            return new Rank
            {
                RankName = model.RankName,
                DisplayOrder = model.DisplayOrder
            };
        }

        private static EditRankViewModel CreateEditViewModelFromRank(Rank rank)
        {
            return new EditRankViewModel
            {
                RankId = rank.RankId,
                RankName = rank.RankName,
                DisplayOrder = rank.DisplayOrder,
                IsActive = true // Default to active
            };
        }

        private static void UpdateRankFromViewModel(Rank rank, EditRankViewModel model)
        {
            rank.RankName = model.RankName;
            rank.DisplayOrder = model.DisplayOrder;
        }

        private static RankViewModel CreateViewModelFromRank(Rank rank)
        {
            return new RankViewModel
            {
                RankId = rank.RankId,
                RankName = rank.RankName,
                DisplayOrder = rank.DisplayOrder,
                IsActive = true // Default to active
            };
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
} 
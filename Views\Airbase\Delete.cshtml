@model RafoEvaluation.ViewModels.AirbaseViewModel
@{
    ViewData["Title"] = "Delete Airbase";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-trash me-2"></i>Delete Airbase
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning!</strong> You are about to permanently delete this airbase. This action cannot be undone.
                    </div>

                    <h5 class="mb-3">Are you sure you want to delete this airbase?</h5>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">Airbase ID:</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-primary">@Model.AirbaseId</span>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Airbase Name:</label>
                        <p class="form-control-plaintext">@Model.AirbaseName</p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Total Candidates:</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-info">@Model.TotalCandidates</span>
                        </p>
                    </div>

                    @if (Model.TotalCandidates > 0)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> This airbase has @Model.TotalCandidates associated candidates. You cannot delete it until all candidates are removed or reassigned.
                        </div>
                    }

                    <div class="d-flex justify-content-end">
                        <a asp-action="Index" class="btn btn-secondary me-2">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        @if (Model.TotalCandidates == 0)
                        {
                            <form asp-action="Delete" method="post" class="d-inline">
                                <input type="hidden" asp-for="AirbaseId" />
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('Are you absolutely sure you want to delete this airbase? This action cannot be undone.')">
                                    <i class="fas fa-trash me-1"></i>Delete Permanently
                                </button>
                            </form>
                        }
                        else
                        {
                            <button type="button" class="btn btn-danger" disabled>
                                <i class="fas fa-trash me-1"></i>Cannot Delete
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

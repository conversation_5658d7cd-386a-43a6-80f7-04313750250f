using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Services
{
    public interface IAuthenticationService
    {
        Task<bool> ValidateUserAsync(string serviceNumber, string password);
        Task<User?> GetUserByServiceNumberAsync(string serviceNumber);
        Task<List<string>> GetUserRolesAsync(int userId);
        Task<bool> IsUserInCommitteeAsync(string serviceNumber, int committeeId);
        Task<bool> IsUserAdminAsync(int userId);
        Task UpdateLastLoginAsync(int userId);
        Task<bool> CanUserEvaluateInCommitteeAsync(string serviceNumber, int committeeId);
    }
}

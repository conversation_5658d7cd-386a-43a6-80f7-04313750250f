using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    public class CategoryViewModel
    {
        public int CategoryId { get; set; }

        [Required(ErrorMessage = "اسم الفئة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الفئة لا يمكن أن يتجاوز 100 حرف")]
        [Display(Name = "اسم الفئة")]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "رمز الفئة مطلوب")]
        [StringLength(10, ErrorMessage = "رمز الفئة لا يمكن أن يتجاوز 10 أحرف")]
        [Display(Name = "رمز الفئة")]
        public string CategoryCode { get; set; } = string.Empty;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "وزن التقييم")]
        [Range(0, 100, ErrorMessage = "وزن التقييم يجب أن يكون بين 0% و 100%")]
        public decimal EvaluationWeight { get; set; } = 30.00m;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }
    }

    public class CategoryCreateViewModel
    {
        [Required(ErrorMessage = "اسم الفئة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الفئة لا يمكن أن يتجاوز 100 حرف")]
        [Display(Name = "اسم الفئة")]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "رمز الفئة مطلوب")]
        [StringLength(10, ErrorMessage = "رمز الفئة لا يمكن أن يتجاوز 10 أحرف")]
        [Display(Name = "رمز الفئة")]
        public string CategoryCode { get; set; } = string.Empty;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "وزن التقييم")]
        [Range(0, 100, ErrorMessage = "وزن التقييم يجب أن يكون بين 0% و 100%")]
        public decimal EvaluationWeight { get; set; } = 30.00m;
    }

    public class CategoryEditViewModel
    {
        public int CategoryId { get; set; }

        [Required(ErrorMessage = "اسم الفئة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الفئة لا يمكن أن يتجاوز 100 حرف")]
        [Display(Name = "اسم الفئة")]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "رمز الفئة مطلوب")]
        [StringLength(10, ErrorMessage = "رمز الفئة لا يمكن أن يتجاوز 10 أحرف")]
        [Display(Name = "رمز الفئة")]
        public string CategoryCode { get; set; } = string.Empty;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "وزن التقييم")]
        [Range(0, 100, ErrorMessage = "وزن التقييم يجب أن يكون بين 0% و 100%")]
        public decimal EvaluationWeight { get; set; } = 30.00m;
    }

    public class CategoryWeightViewModel
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public string CategoryCode { get; set; } = string.Empty;
        public decimal CurrentWeight { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class CategoryWeightUpdateModel
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        
        [Display(Name = "وزن التقييم")]
        [Range(0, 100, ErrorMessage = "وزن التقييم يجب أن يكون بين 0% و 100%")]
        public decimal Weight { get; set; }
    }

    public class CategoryWeightsManagementViewModel
    {
        public List<CategoryWeightViewModel> Categories { get; set; } = new();
        public int TotalCategories { get; set; }
        public decimal AverageWeight { get; set; }
    }

    // CategoryListViewModel تم حذفها - نستخدم IEnumerable<CategoryViewModel> مباشرة مع ViewBag
}

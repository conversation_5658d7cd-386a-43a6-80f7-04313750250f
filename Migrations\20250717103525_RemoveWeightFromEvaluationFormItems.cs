﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class RemoveWeightFromEvaluationFormItems : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Weight",
                table: "EvaluationFormItems");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 10, 35, 23, 889, DateTimeKind.Utc).AddTicks(2307));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 10, 35, 23, 889, DateTimeKind.Utc).AddTicks(3169));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 10, 35, 23, 889, DateTimeKind.Utc).AddTicks(3172));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 10, 35, 23, 889, DateTimeKind.Utc).AddTicks(3173));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 10, 35, 23, 889, DateTimeKind.Utc).AddTicks(3174));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 10, 35, 23, 889, DateTimeKind.Utc).AddTicks(3176));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Weight",
                table: "EvaluationFormItems",
                type: "decimal(3,1)",
                nullable: false,
                defaultValue: 1.0m);

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 8, 19, 24, 806, DateTimeKind.Utc).AddTicks(8136));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 8, 19, 24, 806, DateTimeKind.Utc).AddTicks(8593));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 8, 19, 24, 806, DateTimeKind.Utc).AddTicks(8595));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 8, 19, 24, 806, DateTimeKind.Utc).AddTicks(8596));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 8, 19, 24, 806, DateTimeKind.Utc).AddTicks(8597));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 8, 19, 24, 806, DateTimeKind.Utc).AddTicks(8598));
        }
    }
}

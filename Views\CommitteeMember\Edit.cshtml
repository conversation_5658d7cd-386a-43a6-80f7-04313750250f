@model RafoEvaluation.ViewModels.CommitteeMemberEditViewModel
@{
    ViewData["Title"] = "تعديل عضو اللجنة";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-edit breadcrumb-icon"></i>
                    تعديل عضو اللجنة
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="CommitteeMember">
                                <i class="fas fa-users-cog"></i> أعضاء اللجان
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-edit"></i> تعديل العضو
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit ms-2"></i>تعديل بيانات العضو
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input type="hidden" asp-for="CommitteeMemberId" />
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="CommitteeId" class="form-label"></label>
                                    <select asp-for="CommitteeId" asp-items="@(new SelectList(Model.AvailableCommittees, "CommitteeId", "CommitteeName"))" class="form-select">
                                        <option value="">-- اختر اللجنة --</option>
                                    </select>
                                    <span asp-validation-for="CommitteeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="ServiceNumber" class="form-label"></label>
                                    <select asp-for="ServiceNumber" class="form-select" id="userSelect">
                                        <option value="">-- اختر العضو --</option>
                                        @foreach (var user in Model.AvailableUsers)
                                        {
                                            if (user.ServiceNumber == Model.ServiceNumber)
                                            {
                                                <option value="@user.ServiceNumber" data-rank="@user.Rank?.RankName" selected>
                                                    @user.ServiceNumber @user.Rank?.RankName @user.FullName
                                                </option>
                                            }
                                            else
                                            {
                                                <option value="@user.ServiceNumber" data-rank="@user.Rank?.RankName">
                                                    @user.ServiceNumber @user.Rank?.RankName @user.FullName
                                                </option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="ServiceNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label asp-for="Role" class="form-label"></label>
                                    <select asp-for="Role" asp-items="Html.GetEnumSelectList<CommitteeMemberRole>()" class="form-select" id="roleSelect">
                                    </select>
                                    <span asp-validation-for="Role" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" />
                                        <label asp-for="IsActive" class="form-check-label"></label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input asp-for="CanEvaluate" class="form-check-input" id="canEvaluateCheck" />
                                        <label asp-for="CanEvaluate" class="form-check-label"></label>
                                        <small class="form-text text-muted">تحديد ما إذا كان العضو يمكنه المشاركة في التقييمات</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Notes" class="form-label"></label>
                            <textarea asp-for="Notes" class="form-control" rows="3" placeholder="أدخل ملاحظات إضافية (اختياري)"></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save ms-1"></i>حفظ التغييرات
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.CommitteeMemberId" class="btn btn-info">
                                <i class="fas fa-eye ms-1"></i>عرض التفاصيل
                            </a>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-right ms-1"></i>رجوع
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // تحسين البحث في الدروبداون
            $('#userSelect').on('keyup', function() {
                var searchTerm = $(this).val().toLowerCase();
                var $options = $(this).find('option');
                
                $options.each(function() {
                    var optionText = $(this).text().toLowerCase();
                    if (optionText.includes(searchTerm) || searchTerm === '') {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });

            // عرض معلومات العضو المختار
            $('#userSelect').on('change', function() {
                var selectedOption = $(this).find('option:selected');
                var rank = selectedOption.data('rank');
                var serviceNumber = $(this).val();
                
                if (serviceNumber) {
                    // يمكن إضافة عرض معلومات إضافية هنا
                    console.log('تم اختيار:', serviceNumber, 'الرتبة:', rank);
                }
            });
            
            // تحديث قابلية التقييم بناءً على الدور المختار
            $('#roleSelect').on('change', function() {
                var selectedRole = $(this).val();
                var $canEvaluateCheck = $('#canEvaluateCheck');
                
                if (selectedRole === '@((int)CommitteeMemberRole.منسق)') {
                    $canEvaluateCheck.prop('checked', false);
                    $canEvaluateCheck.prop('disabled', true);
                    $canEvaluateCheck.closest('.form-check').addClass('text-muted');
                } else {
                    $canEvaluateCheck.prop('disabled', false);
                    $canEvaluateCheck.closest('.form-check').removeClass('text-muted');
                }
            });
            
            // تشغيل الحدث عند تحميل الصفحة
            $('#roleSelect').trigger('change');
        });
    </script>
} 
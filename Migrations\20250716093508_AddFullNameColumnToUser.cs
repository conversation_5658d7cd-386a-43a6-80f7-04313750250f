﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class AddFullNameColumnToUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "Users",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9213));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9963));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9965));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9967));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9967));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9969));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "UserId",
                keyValue: 1,
                column: "FullName",
                value: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FullName",
                table: "Users");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 13, 13, 573, DateTimeKind.Utc).AddTicks(7438));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 13, 13, 573, DateTimeKind.Utc).AddTicks(8687));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 13, 13, 573, DateTimeKind.Utc).AddTicks(8692));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 13, 13, 573, DateTimeKind.Utc).AddTicks(8695));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 13, 13, 573, DateTimeKind.Utc).AddTicks(8697));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 9, 13, 13, 573, DateTimeKind.Utc).AddTicks(8699));
        }
    }
}

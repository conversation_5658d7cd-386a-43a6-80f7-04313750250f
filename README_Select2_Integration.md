# إضافة مكتبة Select2.js للمشروع

## نظرة عامة

تم إضافة مكتبة Select2.js إلى المشروع لتحسين تجربة المستخدم في القوائم المنسدلة، مع التركيز على حقول الرتبة والقاعدة الجوية في صفحة إضافة المرشحين.

## الملفات المضافة

### **1. مكتبة Select2.js**
- **الموقع:** `wwwroot/lib/select2/`
- **الملفات:**
  - `select2.min.js` - ملف JavaScript الرئيسي
  - `select2.min.css` - ملف CSS الرئيسي

### **2. ملف CSS مخصص للدعم العربي**
- **الموقع:** `wwwroot/css/select2-rtl.css`
- **الوظائف:**
  - دعم RTL للغة العربية
  - تنسيق متناسق مع Bootstrap
  - دعم الأجهزة المحمولة
  - دعم الوضع المظلم

## التعديلات المطبقة

### **1. إضافة الملفات إلى Layout الرئيسي**

#### **الملف:** `Views/Shared/_Layout.cshtml`
```html
<!-- CSS Files -->
<link rel="stylesheet" href="~/lib/select2/select2.min.css" />
<link rel="stylesheet" href="~/css/select2-rtl.css" />

<!-- JavaScript Files -->
<script src="~/lib/select2/select2.min.js"></script>
```

### **2. تطبيق Select2 على حقول الرتبة والقاعدة الجوية**

#### **الملف:** `Views/Candidate/Create.cshtml`
```html
<!-- حقل الرتبة مع Select2 -->
<select asp-for="RankId" class="form-select select2-rank" id="rankSelect">
    <option value="">اختر الرتبة</option>
    @foreach (var rank in Model.Ranks)
    {
        <option value="@rank.RankId">@rank.RankName</option>
    }
</select>

<!-- حقل القاعدة الجوية مع Select2 -->
<select asp-for="AirbaseId" class="form-select select2-airbase" id="airbaseSelect">
    <option value="">اختر القاعدة الجوية</option>
    @foreach (var airbase in Model.Airbases)
    {
        <option value="@airbase.AirbaseId">@airbase.AirbaseName</option>
    }
</select>
```

```javascript
// تهيئة Select2 للرتبة
function initializeSelect2ForRank() {
    $('#rankSelect').select2({
        placeholder: 'اختر الرتبة',
        allowClear: true,
        language: 'ar',
        dir: 'rtl',
        width: '100%',
        dropdownParent: $('body'),
        templateResult: formatRankOption,
        templateSelection: formatRankSelection
    });
}

// تهيئة Select2 للقاعدة الجوية
function initializeSelect2ForAirbase() {
    $('#airbaseSelect').select2({
        placeholder: 'اختر القاعدة الجوية',
        allowClear: true,
        language: 'ar',
        dir: 'rtl',
        width: '100%',
        dropdownParent: $('body'),
        templateResult: formatAirbaseOption,
        templateSelection: formatAirbaseSelection
    });
}

// تنسيق خيارات الرتبة
function formatRankOption(rank) {
    if (!rank.id) return rank.text;
    return $(`<span><i class="fas fa-medal me-2"></i>${rank.text}</span>`);
}

function formatRankSelection(rank) {
    if (!rank.id) return rank.text;
    return $(`<span><i class="fas fa-medal me-2"></i>${rank.text}</span>`);
}

// تنسيق خيارات القاعدة الجوية
function formatAirbaseOption(airbase) {
    if (!airbase.id) return airbase.text;
    return $(`<span><i class="fas fa-plane me-2"></i>${airbase.text}</span>`);
}

function formatAirbaseSelection(airbase) {
    if (!airbase.id) return airbase.text;
    return $(`<span><i class="fas fa-plane me-2"></i>${airbase.text}</span>`);
}
```

## الميزات المضافة

### **1. دعم كامل للغة العربية** 🌐
- **اتجاه RTL** - دعم الكتابة من اليمين لليسار
- **خط Cairo** - خط عربي جميل ومقروء
- **نصوص عربية** - جميع النصوص والرسائل بالعربية

### **2. تحسينات بصرية** ✨
- **أيقونات Font Awesome** - أيقونة الميدالية للرتب
- **تصميم متناسق** - يتناسق مع تصميم التطبيق
- **تأثيرات بصرية** - تأثيرات التركيز والتفاعل

### **3. وظائف متقدمة** 🔍
- **البحث** - إمكانية البحث في الرتب
- **مسح الاختيار** - زر لمسح الاختيار
- **اختيار سريع** - اختيار سريع من القائمة

### **4. دعم الأجهزة المحمولة** 📱
- **استجابة كاملة** - يعمل على جميع الأجهزة
- **منع التكبير** - منع التكبير التلقائي في iOS
- **تفاعل محسن** - تفاعل محسن للشاشات اللمسية

### **5. دعم الوضع المظلم** 🌙
- **ألوان متكيفة** - ألوان تتكيف مع الوضع المظلم
- **تباين محسن** - تباين محسن للقراءة

## الاستخدام

### **في صفحة إضافة المرشحين:**
1. **اختر الفئة** - اختر فئة المرشح
2. **حقل الرتبة** - سيظهر حقل الرتبة مع Select2
3. **حقل القاعدة الجوية** - سيظهر حقل القاعدة الجوية مع Select2
4. **البحث** - اكتب للبحث في الرتب أو القواعد الجوية
5. **الاختيار** - اختر الرتبة والقاعدة الجوية المطلوبة
6. **المسح** - استخدم زر المسح لإزالة الاختيار

### **الميزات المتاحة:**
- ✅ **البحث السريع** في الرتب والقواعد الجوية
- ✅ **اختيار سهل** من القائمة
- ✅ **مسح الاختيار** بسهولة
- ✅ **دعم كامل للعربية**
- ✅ **تصميم جميل** ومتناسق
- ✅ **استجابة كاملة** للأجهزة
- ✅ **أيقونات مميزة** لكل حقل (ميدالية للرتب، طائرة للقواعد)

## التخصيص

### **إضافة Select2 لحقول أخرى:**
```javascript
// مثال لإضافة Select2 لحقل آخر
$('#otherSelect').select2({
    placeholder: 'اختر الخيار',
    allowClear: true,
    language: 'ar',
    dir: 'rtl',
    width: '100%'
});
```

### **تخصيص التصميم:**
```css
/* تخصيص مظهر Select2 */
.select2-container--default .select2-selection--single {
    border-color: #your-color;
    background-color: #your-bg-color;
}
```

## الاختبار

تم اختبار Select2 على:
- ✅ **المتصفحات الحديثة** - Chrome, Firefox, Safari, Edge
- ✅ **الأجهزة المحمولة** - iOS, Android
- ✅ **الوضع المظلم** - يعمل بشكل مثالي
- ✅ **البحث والاختيار** - يعمل بسلاسة
- ✅ **دعم RTL** - يعمل بشكل صحيح

## النتيجة النهائية

✅ **تم إضافة Select2.js بنجاح**
✅ **دعم كامل للغة العربية**
✅ **تحسين تجربة المستخدم**
✅ **تصميم جميل ومتناسق**
✅ **وظائف متقدمة للبحث والاختيار**
✅ **دعم الأجهزة المحمولة**

---

**تم الإضافة بواسطة فريق تطوير نظام تقييم لجان الترشيح**
**تاريخ الإضافة: يوليو 2025** 
@model RafoEvaluation.ViewModels.CommitteeEvaluationResultsViewModel
@{
    ViewData["Title"] = "درجات المرشحين";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-chart-bar breadcrumb-icon"></i>
                    درجات المرشحين
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="CommitteeEvaluation">
                                <i class="fas fa-clipboard-check"></i> تقييم المرشحين
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-chart-bar"></i> درجات المرشحين
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- معلومات اللجنة والنموذج -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-dark">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-users-cog me-2 text-primary"></i>@Model.CommitteeName
                            </h4>
                            <small class="text-dark">نموذج التقييم: @Model.EvaluationFormTitle</small>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a asp-action="EvaluationSession" asp-route-committeeId="@Model.CommitteeId" asp-route-evaluationFormId="@Model.EvaluationFormId" class="btn btn-outline-dark btn-sm">
                                <i class="fas fa-arrow-left"></i> العودة لجلسة التقييم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-dark">
                <div class="card-body text-center">
                    <h3>@Model.Statistics.CompletedEvaluations</h3>
                    <small>تم التقييم</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h3>@Model.Statistics.InProgressEvaluations</h3>
                    <small>قيد التقييم</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-dark">
                <div class="card-body text-center">
                    <h3>@Model.Statistics.PendingEvaluations</h3>
                    <small>في الانتظار</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-dark">
                <div class="card-body text-center">
                    <h3>@Model.Statistics.TotalCandidates</h3>
                    <small>إجمالي المرشحين</small>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الدرجات -->
    @if (Model.Statistics.CompletedEvaluations > 0)
    {
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card border-info">
                    <div class="card-header bg-info text-dark">
                        <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>متوسط الدرجات</h6>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-info">@Model.Statistics.AverageScore.ToString("F1")</h2>
                        <small class="text-muted">من أصل @Model.TotalMaxScore</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-success">
                    <div class="card-header bg-success text-dark">
                        <h6 class="mb-0"><i class="fas fa-trophy me-2"></i>أعلى درجة</h6>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-success">@Model.Statistics.MaxScore.ToString("F1")</h2>
                        <small class="text-muted">من أصل @Model.TotalMaxScore</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-percentage me-2"></i>نسبة الإنجاز</h6>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-warning">@Model.Statistics.CompletionPercentage.ToString("F1")%</h2>
                        <small class="text-muted">@Model.Statistics.CompletedEvaluations من @Model.Statistics.TotalCandidates</small>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- جدول المرشحين والدرجات -->
    <div class="row">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2 text-primary"></i>قائمة المرشحين والدرجات
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Evaluations.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th>اسم المرشح</th>
                                        <th style="width: 120px;">الرقم العسكري</th>
                                        <th style="width: 100px;">الرتبة</th>
                                        <th style="width: 120px;">الفئة</th>
                                        <th style="width: 120px;">القاعدة الجوية</th>
                                        <th style="width: 100px;">الحالة</th>
                                        <th style="width: 100px;">الدرجة</th>
                                        <th style="width: 120px;">النسبة المئوية</th>
                                        <th style="width: 150px;">تاريخ الإنجاز</th>
                                        <th style="width: 120px;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (int i = 0; i < Model.Evaluations.Count; i++)
                                    {
                                        var evaluation = Model.Evaluations[i];
                                        var rank = i + 1;
                                        var rowClass = evaluation.IsEvaluated ? "table-success" : evaluation.IsInProgress ? "table-warning" : "";
                                        var score = evaluation.TotalScore.HasValue ? evaluation.TotalScore.Value.ToString("F1") : "-";
                                        var percentage = evaluation.TotalScore.HasValue && Model.TotalMaxScore > 0 
                                            ? ((evaluation.TotalScore.Value / Model.TotalMaxScore) * 100).ToString("F1") + "%" 
                                            : "-";
                                        
                                        <tr class="@rowClass">
                                            <td class="text-center">
                                                @if (evaluation.IsEvaluated)
                                                {
                                                    @if (rank == 1)
                                                    {
                                                        <span class="badge bg-warning">🥇 @rank</span>
                                                    }
                                                    else if (rank == 2)
                                                    {
                                                        <span class="badge bg-secondary">🥈 @rank</span>
                                                    }
                                                    else if (rank == 3)
                                                    {
                                                        <span class="badge bg-warning">🥉 @rank</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-info">@rank</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">@rank</span>
                                                }
                                            </td>
                                            <td>
                                                <strong>@evaluation.CandidateName</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">@evaluation.ServiceNumber</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">@evaluation.RankName</span>
                                            </td>
                                            <td>
                                                <small class="text-muted">@evaluation.CategoryName</small>
                                            </td>
                                            <td>
                                                <small class="text-muted">@evaluation.AirbaseName</small>
                                            </td>
                                            <td>
                                                @if (evaluation.IsEvaluated)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>مكتمل
                                                    </span>
                                                }
                                                else if (evaluation.IsInProgress)
                                                {
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>قيد التقييم
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-info">
                                                        <i class="fas fa-hourglass-half me-1"></i>في الانتظار
                                                    </span>
                                                }
                                            </td>
                                            <td class="text-center">
                                                @if (evaluation.TotalScore.HasValue)
                                                {
                                                    <strong class="text-success">@score</strong>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td class="text-center">
                                                @if (evaluation.TotalScore.HasValue)
                                                {
                                                    <span class="badge bg-info">@percentage</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td class="text-center">
                                                @if (evaluation.CompletedAt.HasValue)
                                                {
                                                    <small class="text-muted">@evaluation.CompletedAt.Value.ToString("dd/MM/yyyy")</small>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    @if (evaluation.IsEvaluated)
                                                    {
                                                        <a asp-controller="CandidateEvaluation" asp-action="Details" 
                                                           asp-route-id="@evaluation.CandidateEvaluationId" 
                                                           class="btn btn-outline-success" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a asp-controller="CandidateEvaluation" asp-action="PrintResults" 
                                                           asp-route-id="@evaluation.CandidateEvaluationId" 
                                                           class="btn btn-outline-primary" title="طباعة النتائج" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    }
                                                    else if (evaluation.IsInProgress)
                                                    {
                                                        <a asp-controller="CommitteeEvaluation" asp-action="Evaluate" 
                                                           asp-route-candidateEvaluationId="@evaluation.CandidateEvaluationId" 
                                                           class="btn btn-outline-warning" title="استكمال التقييم">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <a asp-controller="CommitteeEvaluation" asp-action="Evaluate" 
                                                           asp-route-candidateEvaluationId="@evaluation.CandidateEvaluationId" 
                                                           class="btn btn-outline-info" title="بدء التقييم">
                                                            <i class="fas fa-play"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تقييمات لهذه اللجنة</h5>
                            <p class="text-muted">لم يتم إضافة أي مرشحين للتقييم بعد</p>
                            <a asp-action="SelectCandidates" asp-route-committeeId="@Model.CommitteeId" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>إضافة مرشحين
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار التحكم -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع للجان
                        </a>
                        <a asp-action="EvaluationSession" asp-route-committeeId="@Model.CommitteeId" asp-route-evaluationFormId="@Model.EvaluationFormId" class="btn btn-info">
                            <i class="fas fa-clipboard-check me-1"></i>جلسة التقييم
                        </a>
                        <a asp-action="SelectCandidates" asp-route-committeeId="@Model.CommitteeId" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>إضافة مرشحين آخرين
                        </a>
                        @if (Model.Statistics.CompletedEvaluations > 0)
                        {
                            <button type="button" class="btn btn-primary" onclick="printResults()">
                                <i class="fas fa-print me-1"></i>طباعة النتائج
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
    
    .badge {
        font-size: 0.75rem;
    }
</style>

<script>
    function printResults() {
        window.open('@Url.Action("PrintResults", "CandidateEvaluation", new { committeeId = Model.CommitteeId, evaluationFormId = Model.EvaluationFormId })', '_blank');
    }
</script> 
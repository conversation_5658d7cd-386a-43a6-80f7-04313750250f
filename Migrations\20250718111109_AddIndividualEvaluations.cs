﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class AddIndividualEvaluations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AbsentEvaluatorCount",
                table: "CandidateEvaluations",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "AverageScore",
                table: "CandidateEvaluations",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PresentEvaluatorCount",
                table: "CandidateEvaluations",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "IndividualEvaluations",
                columns: table => new
                {
                    IndividualEvaluationId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CandidateEvaluationId = table.Column<int>(type: "int", nullable: false),
                    EvaluatorId = table.Column<int>(type: "int", nullable: false),
                    TotalScore = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false),
                    EvaluatorNotes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    IsPresent = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    EvaluatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IndividualEvaluations", x => x.IndividualEvaluationId);
                    table.ForeignKey(
                        name: "FK_IndividualEvaluations_CandidateEvaluations_CandidateEvaluationId",
                        column: x => x.CandidateEvaluationId,
                        principalTable: "CandidateEvaluations",
                        principalColumn: "CandidateEvaluationId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_IndividualEvaluations_Users_EvaluatorId",
                        column: x => x.EvaluatorId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8436));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8914));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8930));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8931));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8932));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8933));

            migrationBuilder.CreateIndex(
                name: "IX_IndividualEvaluations_CandidateEvaluationId_EvaluatorId",
                table: "IndividualEvaluations",
                columns: new[] { "CandidateEvaluationId", "EvaluatorId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_IndividualEvaluations_EvaluatorId",
                table: "IndividualEvaluations",
                column: "EvaluatorId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "IndividualEvaluations");

            migrationBuilder.DropColumn(
                name: "AbsentEvaluatorCount",
                table: "CandidateEvaluations");

            migrationBuilder.DropColumn(
                name: "AverageScore",
                table: "CandidateEvaluations");

            migrationBuilder.DropColumn(
                name: "PresentEvaluatorCount",
                table: "CandidateEvaluations");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 11, 55, 28, 484, DateTimeKind.Utc).AddTicks(119));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 11, 55, 28, 484, DateTimeKind.Utc).AddTicks(585));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 11, 55, 28, 484, DateTimeKind.Utc).AddTicks(587));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 11, 55, 28, 484, DateTimeKind.Utc).AddTicks(588));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 11, 55, 28, 484, DateTimeKind.Utc).AddTicks(627));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 17, 11, 55, 28, 484, DateTimeKind.Utc).AddTicks(628));
        }
    }
}

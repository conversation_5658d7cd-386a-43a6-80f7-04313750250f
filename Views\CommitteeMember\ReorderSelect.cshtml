@model RafoEvaluation.ViewModels.CommitteeMemberReorderViewModel
@{
    ViewData["Title"] = "اختيار اللجنة لإعادة الترتيب";
}

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-controller="CommitteeMember" asp-action="Index">أعضاء اللجان</a></li>
            <li class="breadcrumb-item active" aria-current="page">اختيار اللجنة لإعادة الترتيب</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sort me-2"></i>
                                اختيار اللجنة لإعادة الترتيب
                            </h5>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a asp-action="Index" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تعليمات:</strong> اختر اللجنة التي تريد إعادة ترتيب أعضائها
                    </div>

                    @if (Model.AvailableCommittees.Any())
                    {
                        <div class="row">
                            @foreach (var committee in Model.AvailableCommittees)
                            {
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 border-primary">
                                        <div class="card-body text-center">
                                            <i class="fas fa-users-cog fa-3x text-primary mb-3"></i>
                                            <h5 class="card-title">@committee.CommitteeName</h5>
                                            @if (!string.IsNullOrEmpty(committee.Description))
                                            {
                                                <p class="card-text text-muted">@committee.Description</p>
                                            }
                                            <div class="mt-3">
                                                <a asp-action="Reorder" asp-route-committeeId="@committee.CommitteeId" 
                                                   class="btn btn-primary">
                                                    <i class="fas fa-sort me-2"></i>
                                                    إعادة ترتيب الأعضاء
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h4 class="text-warning">لا توجد لجان متاحة</h4>
                            <p class="text-muted">
                                لا توجد لجان نشطة لإعادة ترتيب أعضائها.
                            </p>
                            <a asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div> 
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RafoEvaluation.Models.Auth
{
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Column(TypeName = "varchar(50)")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [Display(Name = "الاسم الكامل")]
        [StringLength(200, ErrorMessage = "الاسم الكامل لا يمكن أن يتجاوز 200 حرف")]
        public string FullName { get; set; } = string.Empty;

        [Required]
        public int RankId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastLoginAt { get; set; }

        // Navigation Properties
        public virtual Rank Rank { get; set; } = null!;
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

        // Helper property to get role names
        [NotMapped]
        public IEnumerable<string> RoleNames => UserRoles.Select(ur => ur.Role.RoleName);

        // Helper method to check if user has a specific role
        public bool HasRole(string roleName)
        {
            return UserRoles.Any(ur => ur.Role.RoleName.Equals(roleName, StringComparison.OrdinalIgnoreCase));
        }

        // Helper method to check if user is admin
        public bool IsAdmin => HasRole("Admin");

        // Display name combining service number and rank
        [NotMapped]
        public string DisplayName => $"{Rank?.RankName} ({ServiceNumber})";

        // Full name for display purposes (now uses the database field)
        [NotMapped]
        public string DisplayFullName => string.IsNullOrEmpty(FullName) ? $"{Rank?.RankName} {ServiceNumber}" : FullName;
    }
}

using RafoEvaluation.Models;
using RafoEvaluation.Models.Auth;
using RafoEvaluation.Extensions;
using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    // New Committee-Based Evaluation ViewModels
    public class CommitteeEvaluationViewModel
    {
        public int CommitteeId { get; set; }
        public string CommitteeName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int MemberCount { get; set; }
        public int ActiveMemberCount { get; set; }
        public List<CommitteeMemberListItemViewModel> Members { get; set; } = new();
        public List<CandidateListItemViewModel> AvailableCandidates { get; set; } = new();
        public List<EvaluationFormListItemViewModel> AvailableForms { get; set; } = new();
    }

    public class CandidateListItemViewModel
    {
        public int CandidateId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string AirbaseName { get; set; } = string.Empty;
        public bool IsSelected { get; set; }
        public bool HasActiveEvaluation { get; set; }
    }

    public class EvaluationFormListItemViewModel
    {
        public int EvaluationFormId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int ItemCount { get; set; }
        public int TotalMaxScore { get; set; }
        public bool IsSelected { get; set; }
    }

    public class CommitteeEvaluationCreateViewModel
    {
        [Required(ErrorMessage = "اللجنة مطلوبة")]
        [Display(Name = "اللجنة")]
        public int CommitteeId { get; set; }

        [Required(ErrorMessage = "المرشحين مطلوبون")]
        [Display(Name = "المرشحين")]
        public List<int> SelectedCandidateIds { get; set; } = new();

        [Required(ErrorMessage = "نموذج التقييم مطلوب")]
        [Display(Name = "نموذج التقييم")]
        public int EvaluationFormId { get; set; }

        // For display
        public CommitteeEvaluationViewModel CommitteeInfo { get; set; } = new();
    }

    public class CommitteeEvaluationSessionViewModel
    {
        public int CommitteeId { get; set; }
        public string CommitteeName { get; set; } = string.Empty;
        public int EvaluationFormId { get; set; }
        public string EvaluationFormTitle { get; set; } = string.Empty;
        public List<CandidateEvaluationSessionViewModel> Candidates { get; set; } = new();
        public List<CommitteeMemberListItemViewModel> CommitteeMembers { get; set; } = new();
        public DateTime SessionDate { get; set; } = DateTime.Now;
    }

    public class CandidateEvaluationSessionViewModel
    {
        public int CandidateId { get; set; }
        public int CandidateEvaluationId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string AirbaseName { get; set; } = string.Empty;
        public EvaluationStatus Status { get; set; }
        public string StatusDisplayName => Status.GetDisplayName();
        public decimal? TotalScore { get; set; }
        public int EvaluatorCount { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public bool IsEvaluated => Status == EvaluationStatus.Completed;
        public bool IsInProgress => Status == EvaluationStatus.InProgress;
        public bool IsPending => Status == EvaluationStatus.Pending;
    }

    // Existing ViewModels...
    public class CandidateEvaluationViewModel
    {
        public int CandidateEvaluationId { get; set; }
        public int CandidateId { get; set; }
        public string CandidateName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string AirbaseName { get; set; } = string.Empty;
        public int EvaluationFormId { get; set; }
        public string EvaluationFormTitle { get; set; } = string.Empty;
        public string CommitteeName { get; set; } = string.Empty;
        public EvaluationStatus Status { get; set; }
        public string StatusDisplayName => Status.GetDisplayName();
        public decimal? TotalScore { get; set; }
        public int EvaluatorCount { get; set; }
        public int PresentMemberCount { get; set; }
        public int AbsentMemberCount { get; set; }
        public decimal? AverageScore { get; set; }
        public decimal? PercentageScore { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? GeneralNotes { get; set; }
        public List<EvaluationResultViewModel> Results { get; set; } = new();
    }

    public class EvaluationCreateViewModel
    {
        [Required(ErrorMessage = "المرشح مطلوب")]
        [Display(Name = "المرشح")]
        public int CandidateId { get; set; }

        [Required(ErrorMessage = "اللجنة مطلوبة")]
        [Display(Name = "اللجنة")]
        public int CommitteeId { get; set; }

        [Required(ErrorMessage = "نموذج التقييم مطلوب")]
        [Display(Name = "نموذج التقييم")]
        public int EvaluationFormId { get; set; }

        [Display(Name = "ملاحظات عامة")]
        [StringLength(2000, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 2000 حرف")]
        public string? GeneralNotes { get; set; }

        // For dropdowns
        public List<Candidate> AvailableCandidates { get; set; } = new();
        public List<Committee> AvailableCommittees { get; set; } = new();
        public List<EvaluationForm> AvailableForms { get; set; } = new();
    }

    public class EvaluationDetailsViewModel
    {
        public int CandidateEvaluationId { get; set; }
        public int CandidateId { get; set; }
        public string CandidateName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string AirbaseName { get; set; } = string.Empty;
        public int EvaluationFormId { get; set; }
        public string EvaluationFormTitle { get; set; } = string.Empty;
        public string CommitteeName { get; set; } = string.Empty;
        public EvaluationStatus Status { get; set; }
        public string StatusDisplayName => Status.GetDisplayName();
        public decimal? TotalScore { get; set; }
        public int EvaluatorCount { get; set; }
        public int PresentMemberCount { get; set; }
        public int AbsentMemberCount { get; set; }
        public decimal? AverageScore { get; set; }
        public decimal? PercentageScore { get; set; }
        public int TotalMaxScore { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? GeneralNotes { get; set; }

        // Evaluation Results
        public List<EvaluationResultViewModel> Results { get; set; } = new();
        
        // Committee Members
        public List<CommitteeMemberListItemViewModel> CommitteeMembers { get; set; } = new();
    }

    public class EvaluationResultViewModel
    {
        public int EvaluationFormItemId { get; set; }
        public string Criteria { get; set; } = string.Empty;
        public int MaxScore { get; set; }
        public decimal Score { get; set; }
        public int EvaluatorCount { get; set; }
    }

    public class EvaluationFormViewModel
    {
        public int EvaluationFormId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int ItemCount { get; set; }
        public int TotalMaxScore { get; set; }
        public List<EvaluationFormItemViewModel> Items { get; set; } = new();
    }

    public class EvaluationFormItemViewModel
    {
        public int EvaluationFormItemId { get; set; }
        public string Criteria { get; set; } = string.Empty;
        public int MaxScore { get; set; }
        public int DisplayOrder { get; set; }
    }

    public class EvaluationListViewModel
    {
        public List<CandidateEvaluationViewModel> Evaluations { get; set; } = new();
        public string SearchTerm { get; set; } = string.Empty;
        public int? CommitteeFilter { get; set; }
        public int? FormFilter { get; set; }
        public EvaluationStatus? StatusFilter { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 10;

        // For filters
        public List<Committee> AvailableCommittees { get; set; } = new();
        public List<EvaluationForm> AvailableForms { get; set; } = new();

        // Statistics
        public int TotalEvaluations { get; set; }
        public int PendingEvaluations { get; set; }
        public int CompletedEvaluations { get; set; }
        public int InProgressEvaluations { get; set; }
    }

    // Legacy ViewModels for backward compatibility
    public class CandidateEvaluationResultViewModel
    {
        public int CandidateEvaluationId { get; set; }
        public int CandidateId { get; set; }
        public string CandidateName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string AirbaseName { get; set; } = string.Empty;
        public EvaluationStatus Status { get; set; }
        public string StatusDisplayName => Status.GetDisplayName();
        public decimal? TotalScore { get; set; }
        public int EvaluatorCount { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? GeneralNotes { get; set; }
        public bool IsEvaluated => Status == EvaluationStatus.Completed;
        public bool IsInProgress => Status == EvaluationStatus.InProgress;
        public bool IsPending => Status == EvaluationStatus.Pending;
    }

    public class EvaluationStatisticsViewModel
    {
        public int TotalCandidates { get; set; }
        public int CompletedEvaluations { get; set; }
        public int InProgressEvaluations { get; set; }
        public int PendingEvaluations { get; set; }
        public decimal AverageScore { get; set; }
        public decimal MaxScore { get; set; }
        public decimal MinScore { get; set; }
        public decimal CompletionPercentage => TotalCandidates > 0 ? (CompletedEvaluations * 100.0m) / TotalCandidates : 0;
    }

    public class CommitteeEvaluationResultsViewModel
    {
        public int CommitteeId { get; set; }
        public string CommitteeName { get; set; } = string.Empty;
        public int EvaluationFormId { get; set; }
        public string EvaluationFormTitle { get; set; } = string.Empty;
        public int TotalMaxScore { get; set; }
        public List<CandidateEvaluationResultViewModel> Evaluations { get; set; } = new();
        public EvaluationStatisticsViewModel Statistics { get; set; } = new();
    }

    public class EvaluationScoreViewModel
    {
        public string Criteria { get; set; } = string.Empty;
        public int Score { get; set; }
        public int MaxScore { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    // ViewModels for Candidate Grades Display
    public class CandidateGradesViewModel
    {
        public int CandidateEvaluationId { get; set; }
        public string CandidateName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string CommitteeName { get; set; } = string.Empty;
        public string EvaluationFormTitle { get; set; } = string.Empty;
        public EvaluationStatus Status { get; set; }
        public decimal? TotalScore { get; set; }
        public decimal? AverageScore { get; set; }
        public int EvaluatorCount { get; set; }
        public int PresentEvaluatorCount { get; set; }
        public int AbsentEvaluatorCount { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? GeneralNotes { get; set; }
        public List<EvaluatorScoreViewModel> EvaluatorScores { get; set; } = new List<EvaluatorScoreViewModel>();
        public List<CriteriaScoreViewModel> CriteriaScores { get; set; } = new List<CriteriaScoreViewModel>();
    }

    public class EvaluatorScoreViewModel
    {
        public int IndividualEvaluationId { get; set; }
        public string EvaluatorName { get; set; } = string.Empty;
        public string EvaluatorRank { get; set; } = string.Empty;
        public string EvaluatorServiceNumber { get; set; } = string.Empty;
        public CommitteeMemberRole CommitteeRole { get; set; }
        public decimal TotalScore { get; set; }
        public string? EvaluatorNotes { get; set; }
        public bool IsPresent { get; set; }
        public DateTime EvaluatedAt { get; set; }
        public List<CriteriaScoreViewModel> CriteriaScores { get; set; } = new List<CriteriaScoreViewModel>();
    }

    public class CriteriaScoreViewModel
    {
        public int CriteriaId { get; set; }
        public string CriteriaName { get; set; } = string.Empty;
        public string CriteriaDescription { get; set; } = string.Empty;
        public decimal MaxScore { get; set; }
        public decimal Score { get; set; }
        public string? Notes { get; set; }
        public string EvaluatorName { get; set; } = string.Empty;
    }

    public class CandidateGradesListViewModel
    {
        public List<CandidateGradesViewModel> Candidates { get; set; } = new List<CandidateGradesViewModel>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public string? SearchTerm { get; set; }
        public int? CommitteeId { get; set; }
        public EvaluationStatus? Status { get; set; }
        public List<Committee> AvailableCommittees { get; set; } = new List<Committee>();
        public bool CanViewGrades { get; set; } = false;
        public string CurrentUserRole { get; set; } = string.Empty;
    }

    public class CandidateGradesFilterViewModel
    {
        public string? SearchTerm { get; set; }
        public int? CommitteeId { get; set; }
        public EvaluationStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? CategoryName { get; set; }
        public string? RankName { get; set; }
    }
} 
# إدراج بيانات المرشحين - نظام تقييم القوات الجوية

## نظرة عامة
تم إنشاء ملفات SQL لإدراج 30 مرشح على الأقل في نظام تقييم القوات الجوية الملكية السعودية مع مراعاة البيانات والمنطق الخاص بكل مرشح.

## الملفات المتوفرة

### 1. `complete_candidates_insert.sql` (الملف الرئيسي)
- **الوصف**: ملف شامل يحتوي على كل شيء مطلوب لإدراج المرشحين
- **المحتوى**:
  - إعداد البيانات الأساسية (الفئات، الرتب، القواعد الجوية)
  - إدراج 30 مرشح من 5 فئات مختلفة
  - عرض الإحصائيات والتوزيعات
- **الاستخدام**: قم بتنفيذ هذا الملف مباشرة في SQL Server

### 2. `setup_data.sql`
- **الوصف**: ملف منفصل لإعداد البيانات الأساسية فقط
- **المحتوى**: إدراج الفئات والرتب والقواعد الجوية
- **الاستخدام**: إذا كنت تريد إعداد البيانات الأساسية فقط

### 3. `insert_candidates.sql`
- **الوصف**: ملف منفصل لإدراج المرشحين فقط
- **المحتوى**: إدراج 30 مرشح مع البيانات الكاملة
- **الاستخدام**: إذا كانت البيانات الأساسية موجودة بالفعل

## كيفية الاستخدام

### الطريقة الأولى (المفضلة):
```sql
-- قم بتنفيذ الملف الرئيسي مباشرة
EXECUTE complete_candidates_insert.sql
```

### الطريقة الثانية (خطوة بخطوة):
```sql
-- 1. إعداد البيانات الأساسية
EXECUTE setup_data.sql

-- 2. إدراج المرشحين
EXECUTE insert_candidates.sql
```

## تفاصيل البيانات المدخلة

### الفئات (5 فئات):
1. **طيارين** - طيارين عسكريين
2. **مهندسين** - مهندسين عسكريين
3. **فنيين** - فنيين عسكريين
4. **إداريين** - إداريين عسكريين
5. **أطباء** - أطباء عسكريين

### الرتب (10 رتب):
1. ملازم
2. ملازم أول
3. نقيب
4. رائد
5. مقدم
6. عقيد
7. عميد
8. لواء
9. فريق
10. فريق أول

### القواعد الجوية (3 قواعد):
1. قاعدة الملك عبدالعزيز الجوية
2. قاعدة الملك خالد الجوية
3. قاعدة الملك فهد الجوية

### المرشحين (30 مرشح):
- **6 طيارين** - من كلية الملك فيصل الجوية
- **6 مهندسين** - من جامعات مختلفة (KFUPM, KSU, KAU)
- **6 فنيين** - من المعهد التقني للقوات الجوية
- **6 إداريين** - من جامعات مختلفة
- **6 أطباء** - من جامعات مختلفة

## خصائص البيانات

### أرقام الخدمة:
- نمط: `RAF-2024-XXX` (حيث XXX من 001 إلى 030)
- فريدة ومتسلسلة

### البيانات الشخصية:
- أسماء عربية واقعية مع أسماء عائلات سعودية
- أعمار تتراوح من 25 إلى 32 سنة
- أرقام هوية وطنية فريدة

### البيانات التعليمية:
- تخصصات مناسبة لكل فئة
- جامعات ومعاهد سعودية معروفة
- سنوات تخرج من 2017 إلى 2022
- درجات متنوعة (ممتاز، جيد جداً)

### البيانات الوظيفية:
- أقسام مناسبة لكل فئة
- مسميات وظيفية منطقية
- توزيع متوازن على القواعد الجوية
- رتب متنوعة ومنطقية

## التحقق من النتائج

بعد تنفيذ الملف، ستظهر الإحصائيات التالية:

```sql
-- إجمالي المرشحين: 30
-- المرشحين النشطين: 30

-- توزيع حسب الفئة:
-- طيارين: 6
-- مهندسين: 6
-- فنيين: 6
-- إداريين: 6
-- أطباء: 6

-- توزيع حسب القاعدة الجوية:
-- قاعدة الملك عبدالعزيز الجوية: 10
-- قاعدة الملك خالد الجوية: 10
-- قاعدة الملك فهد الجوية: 10
```

## ملاحظات مهمة

1. **التحقق من التكرار**: الملف يتحقق من عدم وجود مرشحين بنفس أرقام الخدمة
2. **البيانات الأساسية**: يتم التحقق من وجود البيانات الأساسية قبل الإدراج
3. **الأمان**: جميع المرشحين نشطين (IsActive = 1)
4. **التوافق**: البيانات متوافقة مع بنية قاعدة البيانات
5. **الواقعية**: جميع البيانات واقعية ومنطقية

## استكشاف الأخطاء

إذا واجهت أي مشاكل:

1. **تأكد من وجود الجداول**: Categories, Ranks, Airbases, Candidates
2. **تحقق من الصلاحيات**: تأكد من صلاحيات الكتابة على قاعدة البيانات
3. **راجع الأخطاء**: تحقق من رسائل الخطأ في SQL Server Management Studio
4. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية قبل التنفيذ

## الدعم

إذا كنت بحاجة إلى مساعدة إضافية أو تعديل البيانات، يرجى التواصل مع فريق التطوير. 
using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.Models.Auth
{
    public class Rank
    {
        [Key]
        public int RankId { get; set; }

        [Required]
        [StringLength(100)]
        public string RankName { get; set; } = string.Empty;

        public int DisplayOrder { get; set; }

        // Navigation Properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
}

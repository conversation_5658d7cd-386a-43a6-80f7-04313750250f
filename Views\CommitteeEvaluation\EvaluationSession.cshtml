@model RafoEvaluation.ViewModels.CommitteeEvaluationSessionViewModel
@{
    ViewData["Title"] = "جلسة التقييم";
    var hasEvaluatedCandidates = Model.Candidates.Any(c => c.Status == EvaluationStatus.Completed);
    var hasInProgressCandidates = Model.Candidates.Any(c => c.Status == EvaluationStatus.InProgress);
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-play breadcrumb-icon"></i>
                    جلسة التقييم
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="CommitteeEvaluation">
                                <i class="fas fa-clipboard-check"></i> تقييم المرشحين
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-play"></i> جلسة التقييم
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- معلومات الجلسة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-dark">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-users-cog me-2"></i>
                                @Model.CommitteeName
                            </h4>
                            <small class="text-dark">نموذج التقييم: @Model.EvaluationFormTitle</small>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="session-stats">
                                <span class="badge bg-success me-2">
                                    <i class="fas fa-check me-1"></i>@Model.Candidates.Count(c => c.Status == EvaluationStatus.Completed) مكتمل
                                </span>
                                <span class="badge bg-warning me-2">
                                    <i class="fas fa-clock me-1"></i>@Model.Candidates.Count(c => c.Status == EvaluationStatus.InProgress) قيد العمل
                                </span>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-pause me-1"></i>@Model.Candidates.Count(c => c.Status == EvaluationStatus.Pending) معلق
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ الجلسة
                            </h6>
                            <p class="text-muted">@Model.SessionDate.ToString("dddd, dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"))</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-user-friends me-1"></i>
                                أعضاء اللجنة
                            </h6>
                            <div class="committee-members">
                                @foreach (var member in Model.CommitteeMembers.Take(5))
                                {
                                    <span class="badge bg-light text-dark me-1 mb-1">
                                        @member.RankName @member.UserName (@member.ServiceNumber)
                                    </span>
                                }
                                @if (Model.CommitteeMembers.Count > 5)
                                {
                                    <span class="badge bg-secondary">+@(Model.CommitteeMembers.Count - 5) آخرين</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط التقدم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        تقدم الجلسة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="progress-stat">
                                <div class="progress-circle completed">
                                    <span class="progress-number">@Model.Candidates.Count(c => c.Status == EvaluationStatus.Completed)</span>
                                    <span class="progress-label">مكتمل</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="progress-stat">
                                <div class="progress-circle in-progress">
                                    <span class="progress-number">@Model.Candidates.Count(c => c.Status == EvaluationStatus.InProgress)</span>
                                    <span class="progress-label">قيد العمل</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="progress-stat">
                                <div class="progress-circle pending">
                                    <span class="progress-number">@Model.Candidates.Count(c => c.Status == EvaluationStatus.Pending)</span>
                                    <span class="progress-label">معلق</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="progress-stat">
                                <div class="progress-circle total">
                                    <span class="progress-number">@Model.Candidates.Count</span>
                                    <span class="progress-label">إجمالي</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="progress mt-3" style="height: 20px;">
                        @{
                            var completedPercentage = Model.Candidates.Count > 0 ? 
                                (Model.Candidates.Count(c => c.Status == EvaluationStatus.Completed) * 100.0 / Model.Candidates.Count) : 0;
                            var inProgressPercentage = Model.Candidates.Count > 0 ? 
                                (Model.Candidates.Count(c => c.Status == EvaluationStatus.InProgress) * 100.0 / Model.Candidates.Count) : 0;
                        }
                        <div class="progress-bar bg-success" style="width: @(completedPercentage)%" title="مكتمل: @(completedPercentage.ToString("F1"))%">
                            <small>@(completedPercentage.ToString("F1"))%</small>
                        </div>
                        <div class="progress-bar bg-warning" style="width: @(inProgressPercentage)%" title="قيد العمل: @(inProgressPercentage.ToString("F1"))%">
                            <small>@(inProgressPercentage.ToString("F1"))%</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المرشحين -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        المرشحين في الجلسة
                    </h5>
                    <div class="card-tools">
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="filterCandidates('all')">
                                <i class="fas fa-list me-1"></i>الكل
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="filterCandidates('completed')">
                                <i class="fas fa-check me-1"></i>مكتمل
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="filterCandidates('in-progress')">
                                <i class="fas fa-clock me-1"></i>قيد العمل
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="filterCandidates('pending')">
                                <i class="fas fa-pause me-1"></i>معلق
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (!Model.Candidates.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد مرشحين في هذه الجلسة</h4>
                            <p class="text-muted">يرجى إضافة مرشحين للبدء في التقييم</p>
                            <a asp-action="SelectCandidates" asp-route-committeeId="@Model.CommitteeId" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>إضافة مرشحين
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="row" id="candidatesContainer">
                            @foreach (var candidate in Model.Candidates)
                            {
                                <div class="col-lg-6 col-xl-4 mb-4 candidate-card" data-status="@candidate.Status.ToString().ToLower()">
                                    <div class="card h-100 @GetStatusCardClass(candidate.Status)">
                                        <div class="card-header @GetStatusHeaderClass(candidate.Status)">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="card-title mb-0 text-white">
                                                    <i class="fas fa-user me-1"></i>
                                                    @candidate.FullName
                                                </h6>
                                                <span class="badge @GetStatusBadgeClass(candidate.Status)">
                                                    @GetStatusText(candidate.Status)
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="candidate-info mb-3">
                                                <p class="text-muted mb-1">
                                                    <i class="fas fa-id-card me-1"></i>
                                                    @candidate.ServiceNumber
                                                </p>
                                                <p class="text-muted mb-1">
                                                    <i class="fas fa-star me-1"></i>
                                                    @candidate.RankName
                                                </p>
                                                <p class="text-muted mb-1">
                                                    <i class="fas fa-list me-1"></i>
                                                    @candidate.CategoryName
                                                </p>
                                                <p class="text-muted mb-0">
                                                    <i class="fas fa-building me-1"></i>
                                                    @candidate.AirbaseName
                                                </p>
                                            </div>
                                            
                                            @if (candidate.Status == EvaluationStatus.Completed && candidate.TotalScore.HasValue)
                                            {
                                                <div class="evaluation-result mb-3">
                                                    <div class="score-display">
                                                        <span class="score-number">@candidate.TotalScore.Value.ToString("F1")</span>
                                                        <span class="score-label">درجة</span>
                                                    </div>
                                                    <div class="progress mt-2" style="height: 8px;">
                                                        <div class="progress-bar bg-success" style="width: @(candidate.TotalScore.Value * 100 / 100)%"></div>
                                                    </div>
                                                </div>
                                            }
                                            else if (candidate.Status == EvaluationStatus.InProgress)
                                            {
                                                <div class="evaluation-progress mb-3">
                                                    <div class="progress" style="height: 8px;">
                                                        <div class="progress-bar bg-warning" style="width: 50%"></div>
                                                    </div>
                                                    <small class="text-muted">قيد التقييم</small>
                                                </div>
                                            }
                                            
                                            <div class="candidate-actions">
                                                @if (candidate.Status == EvaluationStatus.Completed)
                                                {
                                                    <div class="btn-group w-100" role="group">
                                                        <a asp-controller="CandidateEvaluation" asp-action="Details" 
                                                           asp-route-id="@candidate.CandidateEvaluationId" 
                                                           class="btn btn-outline-success btn-sm">
                                                            <i class="fas fa-eye me-1"></i>عرض النتائج
                                                        </a>
                                                        <a asp-controller="CandidateEvaluation" asp-action="PrintResults" 
                                                           asp-route-id="@candidate.CandidateEvaluationId" 
                                                           class="btn btn-outline-primary btn-sm" target="_blank">
                                                            <i class="fas fa-print me-1"></i>طباعة
                                                        </a>
                                                    </div>
                                                }
                                                else if (candidate.Status == EvaluationStatus.InProgress)
                                                {
                                                    <a asp-action="Evaluate" asp-route-candidateEvaluationId="@candidate.CandidateEvaluationId" 
                                                       class="btn btn-warning btn-sm w-100">
                                                        <i class="fas fa-edit me-1"></i>متابعة التقييم
                                                    </a>
                                                }
                                                else
                                                {
                                                    <a asp-action="Evaluate" asp-route-candidateEvaluationId="@candidate.CandidateEvaluationId" 
                                                       class="btn btn-primary btn-sm w-100">
                                                        <i class="fas fa-play me-1"></i>بدء التقييم
                                                    </a>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار التحكم -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع للجان
                        </a>
                        @if (hasEvaluatedCandidates)
                        {
                            <a asp-action="ViewResults" 
                               asp-route-committeeId="@Model.CommitteeId" 
                               asp-route-evaluationFormId="@Model.EvaluationFormId" 
                               class="btn btn-info">
                                <i class="fas fa-chart-bar me-1"></i>عرض درجات المرشحين
                            </a>
                        }
                        <a asp-action="SelectCandidates" asp-route-committeeId="@Model.CommitteeId" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>إضافة مرشحين آخرين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .session-stats .badge {
        font-size: 0.8rem;
    }
    
    .committee-members {
        max-height: 80px;
        overflow-y: auto;
    }
    
    .progress-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: white;
        font-weight: bold;
    }
    
    .progress-circle.completed {
        background: linear-gradient(135deg, #28a745, #20c997);
    }
    
    .progress-circle.in-progress {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
    }
    
    .progress-circle.pending {
        background: linear-gradient(135deg, #6c757d, #495057);
    }
    
    .progress-circle.total {
        background: linear-gradient(135deg, #007bff, #0056b3);
    }
    
    .progress-number {
        font-size: 1.5rem;
        line-height: 1;
    }
    
    .progress-label {
        font-size: 0.7rem;
        margin-top: 2px;
    }
    
    .candidate-card {
        transition: transform 0.2s ease-in-out;
    }
    
    .candidate-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .score-display {
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
    }
    
    .score-number {
        display: block;
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
        line-height: 1;
    }
    
    .score-label {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .candidate-info p {
        margin-bottom: 0.25rem;
    }
    
    .candidate-actions {
        margin-top: auto;
    }
    
    .progress {
        border-radius: 10px;
    }
    
    .progress-bar {
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: bold;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
    }
    
    .btn-group .btn:first-child {
        border-top-left-radius: 0.375rem !important;
        border-bottom-left-radius: 0.375rem !important;
    }
    
    .btn-group .btn:last-child {
        border-top-right-radius: 0.375rem !important;
        border-bottom-right-radius: 0.375rem !important;
    }
    
    /* تحسين وضوح النص في رؤوس البطاقات */
    .card-header.bg-success.text-dark,
    .card-header.bg-info.text-dark,
    .card-header.bg-primary.text-dark,
    .card-header.bg-secondary.text-dark {
        font-weight: 600;
    }
    
    .card-header.bg-success.text-dark .card-title,
    .card-header.bg-info.text-dark .card-title,
    .card-header.bg-primary.text-dark .card-title,
    .card-header.bg-secondary.text-dark .card-title {
        text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
    }
</style>

<script>
    function filterCandidates(status) {
        $('.candidate-card').show();
        
        if (status !== 'all') {
            $('.candidate-card').each(function() {
                var cardStatus = $(this).data('status');
                if (cardStatus !== status) {
                    $(this).hide();
                }
            });
        }
        
        // تحديث حالة الأزرار
        $('.btn-group .btn').removeClass('active');
        event.target.classList.add('active');
    }
    
    // إخفاء/إظهار المرشحين بناءً على الحالة
    $(document).ready(function() {
        // إضافة تأثيرات بصرية للبطاقات
        $('.candidate-card').hover(
            function() {
                $(this).find('.card').addClass('shadow-lg');
            },
            function() {
                $(this).find('.card').removeClass('shadow-lg');
            }
        );
    });
</script>

@functions {
    private string GetStatusCardClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.Completed => "border-success",
            EvaluationStatus.InProgress => "border-warning",
            EvaluationStatus.Pending => "border-secondary",
            _ => "border-light"
        };
    }
    
    private string GetStatusHeaderClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.Completed => "bg-success text-dark",
            EvaluationStatus.InProgress => "bg-warning text-dark",
            EvaluationStatus.Pending => "bg-secondary text-dark",
            _ => "bg-light text-dark"
        };
    }
    
    private string GetStatusBadgeClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.Completed => "bg-success",
            EvaluationStatus.InProgress => "bg-warning text-dark",
            EvaluationStatus.Pending => "bg-secondary",
            _ => "bg-light text-dark"
        };
    }
    
    private string GetStatusText(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.Completed => "مكتمل",
            EvaluationStatus.InProgress => "قيد العمل",
            EvaluationStatus.Pending => "معلق",
            _ => "غير محدد"
        };
    }
} 
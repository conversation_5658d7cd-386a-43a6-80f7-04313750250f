# إعادة تنظيم الصلاحيات - Roles Reorganization

## نظرة عامة
تم إعادة تنظيم الصلاحيات في النظام لتكون 4 صلاحيات فقط بدلاً من 6 صلاحيات، مع تحديد واضح لدور كل صلاحية.

## الصلاحيات الجديدة

### 1. المدير (Admin)
**الوصف**: مدير النظام مع صلاحيات كاملة
**الصلاحيات**:
- ✅ إدارة جميع المستخدمين والصلاحيات
- ✅ إدارة القواعد الجوية والرتب العسكرية والفئات
- ✅ إدارة اللجان وأعضاء اللجان
- ✅ إدارة نماذج التقييم
- ✅ إدارة المرشحين وتعيينهم للجان
- ✅ عرض جميع التقارير والإحصائيات
- ✅ حذف وتعديل أي بيانات في النظام
- ✅ إدارة الإعدادات العامة للنظام
- ✅ دليل المستخدم

### 2. المقيم (Evaluator)
**الوصف**: مقيم يقوم بتقييم المرشحين
**الصلاحيات**:
- ✅ الوصول لصفحة التقييم
- ✅ تقييم المرشحين المخصصين له
- ✅ عرض درجات المرشحين في اللجان التي ينتمي إليها
- ✅ عرض لوحة التحكم
- ✅ دليل المستخدم
- ❌ لا يمكنه إدارة المرشحين أو اللجان
- ❌ لا يمكنه إدارة نماذج التقييم
- ❌ لا يمكنه عرض التقارير الشاملة

### 3. المنسق (Coordinator)
**الوصف**: منسق التقييم يدير عملية التقييم
**الصلاحيات**:
- ✅ إدارة المرشحين وتعيينهم للجان
- ✅ إدارة اللجان وأعضاء اللجان
- ✅ إدارة نماذج التقييم
- ✅ عرض التقارير والإحصائيات
- ✅ عرض درجات المرشحين
- ✅ الوصول لصفحة التقييم
- ✅ عرض لوحة التحكم
- ✅ دليل المستخدم
- ❌ لا يمكنه إدارة المستخدمين والصلاحيات
- ❌ لا يمكنه إدارة الإعدادات العامة (القواعد، الرتب، الفئات)

### 4. الخلية الإدارية (Admin Cell)
**الوصف**: خلية إدارية تدير البيانات الأساسية والمرشحين والمقيمين
**الصلاحيات**:
- ✅ إدارة القواعد الجوية
- ✅ إدارة الرتب العسكرية
- ✅ إدارة الفئات
- ✅ إدارة المستخدمين (إضافة، تعديل، حذف)
- ✅ **إدارة المرشحين وإدخال أسمائهم**
- ✅ **تعيين المرشحين للتقويم**
- ✅ **تعيين المقيمين للجان**
- ✅ عرض التقارير والإحصائيات
- ✅ عرض لوحة التحكم
- ✅ دليل المستخدم
- ❌ لا يمكنه إدارة نماذج التقييم
- ❌ لا يمكنه الوصول لصفحة التقييم

## الصلاحيات المحذوفة

### الصلاحيات القديمة التي تم حذفها:
1. **Viewer** - تم تحويله إلى **Admin Cell**
2. **HR Manager** - تم دمج صلاحياته مع **Coordinator**
3. **Training Officer** - تم حذفه نهائياً

## التحديثات في القائمة الجانبية

### إدارة المرشحين
- **قبل**: Admin, Coordinator, HR Manager
- **بعد**: Admin, Coordinator, Admin Cell

### إدارة اللجان
- **قبل**: Admin, Coordinator, HR Manager
- **بعد**: Admin, Coordinator

### نماذج التقييم
- **قبل**: Admin, Coordinator, HR Manager
- **بعد**: Admin, Coordinator

### التقارير
- **قبل**: Admin, Coordinator, HR Manager
- **بعد**: Admin, Coordinator, Admin Cell

### دليل المستخدم
- **قبل**: Evaluator, ChairmanMember, CommitteeMember
- **بعد**: للجميع (Admin, Coordinator, Evaluator, Admin Cell)

## الملفات المعدلة

### 1. `Data/ApplicationDbContext.cs`
- تحديث بيانات الصلاحيات في قاعدة البيانات
- حذف الصلاحيات القديمة (HR Manager, Training Officer)
- تحديث وصف الصلاحيات الجديدة

### 2. `Views/Shared/_Layout.cshtml`
- تحديث شروط الصلاحيات في القائمة الجانبية
- إضافة Admin Cell للصلاحيات المناسبة
- جعل دليل المستخدم متاح للجميع

### 3. `Migrations/20250101000000_UpdateRolesToFourRoles.cs`
- Migration لتحديث قاعدة البيانات
- حذف الصلاحيات القديمة
- تحديث الصلاحيات الجديدة

## تدفق العمل الجديد

### 1. الخلية الإدارية (Admin Cell):
   - تدخل أسماء المرشحين
   - تعين المرشحين للتقويم
   - تعين المقيمين للجان
   - تدير البيانات الأساسية

### 2. المنسق (Coordinator):
   - يدير اللجان وأعضاء اللجان
   - يدير نماذج التقييم
   - يتابع عملية التقييم
   - يعرض التقارير

### 3. المقيم (Evaluator):
   - يقوم بالتقييم
   - يرى النتائج
   - يستخدم دليل المستخدم

### 4. المدير (Admin):
   - يشرف على كل شيء
   - يدير الإعدادات العامة
   - يدير المستخدمين والصلاحيات

## الفوائد من إعادة التنظيم

### 1. وضوح الأدوار
- كل دور له مسؤوليات واضحة ومحددة
- فصل واضح بين المهام الإدارية والتقييمية

### 2. أمان محسن
- فصل الصلاحيات حسب الوظيفة
- تقليل الصلاحيات الزائدة

### 3. سهولة الإدارة
- أدوار منطقية ومفهومة
- تقليل التعقيد في إدارة الصلاحيات

### 4. مرونة
- يمكن تعديل الصلاحيات حسب الحاجة
- سهولة إضافة صلاحيات جديدة في المستقبل

## ملاحظات مهمة

1. **الخلية الإدارية**: مسؤولة عن إدخال البيانات الأساسية والمرشحين
2. **المنسق**: مسؤول عن إدارة عملية التقييم
3. **المقيم**: مسؤول عن التقييم فقط
4. **المدير**: مسؤول عن الإشراف العام على النظام

## اختبار الصلاحيات الجديدة

### اختبار الخلية الإدارية:
1. تسجيل الدخول بصلاحية Admin Cell
2. التأكد من إمكانية إدارة المرشحين
3. التأكد من إمكانية إدارة المستخدمين
4. التأكد من عدم إمكانية الوصول لصفحة التقييم

### اختبار المنسق:
1. تسجيل الدخول بصلاحية Coordinator
2. التأكد من إمكانية إدارة اللجان
3. التأكد من إمكانية إدارة نماذج التقييم
4. التأكد من إمكانية عرض التقارير

### اختبار المقيم:
1. تسجيل الدخول بصلاحية Evaluator
2. التأكد من إمكانية الوصول لصفحة التقييم
3. التأكد من عدم إمكانية إدارة المرشحين
4. التأكد من إمكانية استخدام دليل المستخدم

### اختبار المدير:
1. تسجيل الدخول بصلاحية Admin
2. التأكد من إمكانية الوصول لجميع الصفحات
3. التأكد من إمكانية إدارة الإعدادات العامة 
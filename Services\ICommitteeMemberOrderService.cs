using RafoEvaluation.Models;

namespace RafoEvaluation.Services
{
    public interface ICommitteeMemberOrderService
    {
        Task<int> GetNextMemberNumber(int committeeId);
        Task<string> GetMemberDisplayName(int memberNumber, CommitteeMemberRole role);
        Task<int> GetEvaluatorCount(int committeeId); // عدد المقيمين فقط
        Task<List<CommitteeMember>> GetEvaluators(int committeeId); // المقيمين فقط
        Task<bool> CanMemberEvaluate(int committeeMemberId);
        Task<bool> CanMemberEvaluate(CommitteeMember member);
        Task<bool> ValidateMemberOrder(int committeeId);
        Task ReorderMembers(int committeeId);
        Task<string> GetFormattedMemberName(CommitteeMember member);
    }
} 
# دليل حل مشكلة الترميز العربي في SQL Server

## المشكلة
عند إدراج النصوص العربية في SQL Server، قد تظهر رموز غير واضحة أو علامات استفهام بدلاً من النصوص العربية.

## الحلول المتاحة

### الحل الأول: استخدام الملف المحسن (المفضل)
استخدم الملف `complete_candidates_insert_fixed.sql` الذي يحتوي على:
- استخدام `N` قبل النصوص العربية (Unicode)
- تعيين الترميز للجلسة
- إعدادات Collation مناسبة

### الحل الثاني: استخدام إجراء إصلاح الترميز
استخدم الملف `fix_arabic_encoding.sql` الذي يحتوي على:
- إجر<PERSON>ء شامل لإصلاح الترميز
- حذف وإعادة إدراج البيانات
- التحقق من الإعدادات

### الحل الثالث: الإعدادات اليدوية

#### 1. إعدادات قاعدة البيانات
```sql
-- التحقق من Collation الحالي
SELECT DATABASEPROPERTYEX(DB_NAME(), 'Collation');

-- تغيير Collation (إذا لزم الأمر)
ALTER DATABASE [YourDatabaseName] 
COLLATE Arabic_CI_AS;
```

#### 2. إعدادات الجداول
```sql
-- تغيير Collation للجداول
ALTER TABLE Categories 
ALTER COLUMN CategoryName NVARCHAR(100) COLLATE Arabic_CI_AS;

ALTER TABLE Ranks 
ALTER COLUMN RankName NVARCHAR(100) COLLATE Arabic_CI_AS;

ALTER TABLE Airbases 
ALTER COLUMN AirbaseName NVARCHAR(100) COLLATE Arabic_CI_AS;

ALTER TABLE Candidates 
ALTER COLUMN FullName NVARCHAR(200) COLLATE Arabic_CI_AS;
```

#### 3. إعدادات الجلسة
```sql
-- تعيين الترميز للجلسة
SET NAMES 'utf8';
SET CHARACTER SET utf8;
SET COLLATION_CONNECTION = 'utf8_unicode_ci';
```

## كيفية الاستخدام

### الطريقة الأولى (الأسهل):
```sql
-- تنفيذ الملف المحسن مباشرة
EXECUTE complete_candidates_insert_fixed.sql
```

### الطريقة الثانية (للإصلاح):
```sql
-- تنفيذ إجراء إصلاح الترميز
EXECUTE fix_arabic_encoding.sql
```

### الطريقة الثالثة (يدوية):
1. قم بتنفيذ إعدادات قاعدة البيانات
2. قم بتنفيذ إعدادات الجداول
3. قم بتنفيذ إعدادات الجلسة
4. قم بتنفيذ ملف إدراج المرشحين

## Collations الموصى بها للعربية

### للقوات الجوية السعودية:
- `Arabic_CI_AS` - Arabic, Case-Insensitive, Accent-Sensitive
- `Arabic_100_CI_AS` - Arabic 100, Case-Insensitive, Accent-Sensitive
- `Arabic_100_CI_AI` - Arabic 100, Case-Insensitive, Accent-Insensitive

### للاستخدام العام:
- `utf8_unicode_ci` - UTF-8 Unicode, Case-Insensitive
- `utf8_general_ci` - UTF-8 General, Case-Insensitive

## التحقق من النتائج

### 1. التحقق من الترميز:
```sql
-- عرض عينة من البيانات
SELECT TOP 5 
    FullName,
    ServiceNumber,
    JobTitle
FROM Candidates 
ORDER BY CandidateId;
```

### 2. التحقق من Collation:
```sql
-- عرض Collation للجداول
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLLATION_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' 
    AND DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar')
    AND TABLE_NAME IN ('Categories', 'Ranks', 'Airbases', 'Candidates')
ORDER BY TABLE_NAME, COLUMN_NAME;
```

### 3. التحقق من الإحصائيات:
```sql
-- عرض إحصائيات المرشحين
SELECT 
    N'إجمالي المرشحين' as Description,
    COUNT(*) as Count
FROM Candidates
UNION ALL
SELECT 
    N'المرشحين النشطين',
    COUNT(*)
FROM Candidates 
WHERE IsActive = 1;
```

## نصائح مهمة

### 1. استخدام Unicode:
- استخدم `N` قبل النصوص العربية: `N'نص عربي'`
- استخدم `NVARCHAR` بدلاً من `VARCHAR` للأعمدة العربية

### 2. إعدادات الملف:
- احفظ ملفات SQL بترميز UTF-8
- تأكد من إعدادات المحرر لدعم العربية

### 3. إعدادات SQL Server:
- تأكد من دعم SQL Server للغة العربية
- استخدم إصدار حديث من SQL Server

### 4. إعدادات العميل:
- تأكد من إعدادات العميل (SSMS, Azure Data Studio)
- استخدم خطوط تدعم العربية

## استكشاف الأخطاء

### إذا ظهرت علامات استفهام:
1. تحقق من Collation للجداول
2. تأكد من استخدام `N` قبل النصوص
3. تحقق من إعدادات العميل

### إذا ظهرت رموز غريبة:
1. تحقق من ترميز الملف
2. تأكد من إعدادات قاعدة البيانات
3. جرب تغيير Collation

### إذا لم تظهر النصوص:
1. تحقق من إعدادات العرض
2. تأكد من دعم الخطوط للعربية
3. جرب إعادة تشغيل العميل

## الدعم

إذا استمرت المشكلة:
1. تحقق من إصدار SQL Server
2. تأكد من إعدادات النظام
3. راجع سجلات الأخطاء
4. تواصل مع فريق الدعم التقني

## ملاحظات إضافية

- الملفات المحسنة تحتوي على `N` قبل جميع النصوص العربية
- إجراء الإصلاح يحذف البيانات القديمة ويعيد إدراجها
- تأكد من عمل نسخة احتياطية قبل التنفيذ
- اختبر على بيئة تطوير قبل الإنتاج 
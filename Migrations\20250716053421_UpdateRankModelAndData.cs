﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRankModelAndData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Clear existing rank data first - handle foreign key constraints
            migrationBuilder.Sql("DELETE FROM [CommitteeMembers]");
            migrationBuilder.Sql("DELETE FROM [Users]");
            migrationBuilder.Sql("DELETE FROM [Candidates]");
            migrationBuilder.Sql("DELETE FROM [Ranks]");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "Ranks");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Ranks");

            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "RankId", "DisplayOrder", "RankName" },
                values: new object[,]
                {
                    { 1, 1, "جندي" },
                    { 2, 2, "نائب عريف" },
                    { 3, 3, "عريف" },
                    { 4, 4, "رقيب" },
                    { 5, 5, "رقيب أول" },
                    { 6, 6, "وكيل" },
                    { 7, 7, "وكيل أول" },
                    { 8, 8, "ضابط مرشح" },
                    { 9, 9, "ملازم ثاني" },
                    { 10, 10, "ملازم أول" },
                    { 11, 11, "نقيب" },
                    { 12, 12, "رائد" },
                    { 13, 13, "مقدم" },
                    { 14, 14, "عقيد" },
                    { 15, 15, "عميد" },
                    { 16, 16, "لواء" }
                });

            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "RankId", "DisplayOrder", "RankName" },
                values: new object[,]
                {
                    { 17, 17, "فريق" },
                    { 18, 18, "مدني درجة 16" },
                    { 19, 19, "مدني درجة 15" },
                    { 20, 20, "مدني درجة 14" },
                    { 21, 21, "مدني درجة 13" },
                    { 22, 22, "مدني درجة 12" },
                    { 23, 23, "مدني درجة 11" },
                    { 24, 24, "مدني درجة 10" },
                    { 25, 25, "مدني درجة 9" },
                    { 26, 26, "ضابط مدني د8" },
                    { 27, 27, "ضابط مدني د9" },
                    { 28, 28, "ضابط مدني د7" },
                    { 29, 29, "ضابط مدني د6" },
                    { 30, 30, "ضابط مدني د5" },
                    { 31, 31, "ضابط مدني د4" },
                    { 32, 32, "ضابط مدني د3" },
                    { 33, 33, "ضابط مدني د2" },
                    { 34, 34, "ضابط مدني د1" }
                });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(53));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(856));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(858));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(859));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(861));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(862));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 17);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 18);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 19);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 20);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 21);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 22);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 23);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 24);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 25);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 26);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 27);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 28);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 29);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 30);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 31);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 32);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 33);

            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 34);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "Ranks",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETDATE()");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Ranks",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 658, DateTimeKind.Utc).AddTicks(9651), "أعلى رتبة في سلاح الجو", "مشير" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(626), "رتبة عالية في سلاح الجو", "فريق" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(628), "رتبة ضابط عالي", "عميد" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(629), "رتبة ضابط عالي", "عقيد" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(630), "رتبة ضابط عالي", "مقدم" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(631), "رتبة ضابط", "رائد" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(632), "رتبة ضابط", "نقيب" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(633), "رتبة ضابط صغير", "ملازم أول" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(633), "رتبة ضابط صغير", "ملازم" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(634), "رتبة وكيل ضابط", "وكيل ضابط" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(635), "رتبة ضابط صف", "رقيب أول" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(636), "رتبة ضابط صف", "رقيب" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(637), "رتبة ضابط صف صغير", "عريف" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(638), "رتبة جندي", "جندي أول" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(638), "رتبة جندي", "جندي" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(639), "بدون رتبة", "---" });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(5609));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6115));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6117));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6118));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6119));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6120));
        }
    }
}

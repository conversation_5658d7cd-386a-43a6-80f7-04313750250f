using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RafoEvaluation.Data;
using RafoEvaluation.Models;

namespace RafoEvaluation.Services
{
    public class EvaluationCalculationService : IEvaluationCalculationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EvaluationCalculationService> _logger;

        public EvaluationCalculationService(ApplicationDbContext context, ILogger<EvaluationCalculationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<decimal?> CalculateAverageFromActiveMembers(int candidateEvaluationId)
        {
            var candidateEvaluation = await _context.CandidateEvaluations
                .Include(ce => ce.IndividualEvaluations)
                    .ThenInclude(ie => ie.Evaluator)
                .Include(ce => ce.Committee)
                    .ThenInclude(c => c.CommitteeMembers.Where(cm => cm.IsActive))
                .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == candidateEvaluationId);

            if (candidateEvaluation == null)
                return null;

            // Get active committee members
            var activeCommitteeMembers = candidateEvaluation.Committee?.CommitteeMembers
                .Where(cm => cm.IsActive)
                .Select(cm => cm.User?.UserId)
                .Where(userId => userId.HasValue)
                .Select(userId => userId!.Value)
                .ToHashSet() ?? new HashSet<int>();

            // Filter evaluations from active members only
            var activeEvaluations = candidateEvaluation.IndividualEvaluations
                .Where(ie => ie.IsPresent && activeCommitteeMembers.Contains(ie.EvaluatorId))
                .ToList();

            if (!activeEvaluations.Any())
                return null;

            var average = activeEvaluations.Average(ie => ie.TotalScore);
            
            _logger.LogInformation($"Calculated average {average:F2} from {activeEvaluations.Count} active members for candidate evaluation {candidateEvaluationId}");
            
            return average;
        }

        public async Task<int> GetActiveMemberCount(int committeeId)
        {
            return await _context.CommitteeMembers
                .Where(cm => cm.CommitteeId == committeeId && cm.IsActive)
                .CountAsync();
        }

        public async Task UpdateCandidateEvaluationStatistics(int candidateEvaluationId)
        {
            var candidateEvaluation = await _context.CandidateEvaluations
                .Include(ce => ce.IndividualEvaluations)
                    .ThenInclude(ie => ie.Evaluator)
                .Include(ce => ce.Committee)
                    .ThenInclude(c => c.CommitteeMembers.Where(cm => cm.IsActive))
                .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == candidateEvaluationId);

            if (candidateEvaluation == null)
                return;

            var individualEvaluations = candidateEvaluation.IndividualEvaluations.ToList();

            // Filter evaluations from ACTIVE committee members only
            var activeCommitteeMembers = candidateEvaluation.Committee?.CommitteeMembers
                .Where(cm => cm.IsActive)
                .Select(cm => cm.User?.UserId)
                .Where(userId => userId.HasValue)
                .Select(userId => userId!.Value)
                .ToHashSet() ?? new HashSet<int>();

            var activeEvaluations = individualEvaluations
                .Where(ie => ie.IsPresent && activeCommitteeMembers.Contains(ie.EvaluatorId))
                .ToList();

            candidateEvaluation.EvaluatorCount = individualEvaluations.Count;
            candidateEvaluation.PresentEvaluatorCount = activeEvaluations.Count; // Only count active members
            candidateEvaluation.AbsentEvaluatorCount = individualEvaluations.Count - activeEvaluations.Count;

            if (activeEvaluations.Any())
            {
                candidateEvaluation.AverageScore = activeEvaluations.Average(ie => ie.TotalScore);
                candidateEvaluation.TotalScore = activeEvaluations.Sum(ie => ie.TotalScore);

                _logger.LogInformation($"Updated average score: {candidateEvaluation.AverageScore} from {activeEvaluations.Count} active committee members");
            }
            else
            {
                candidateEvaluation.AverageScore = null;
                candidateEvaluation.TotalScore = null;

                _logger.LogInformation("No evaluations from active committee members found");
            }

            // Update status based on active evaluations
            if (activeEvaluations.Count > 0)
            {
                candidateEvaluation.Status = EvaluationStatus.Completed;
                candidateEvaluation.CompletedAt = DateTime.UtcNow;
            }
            else if (individualEvaluations.Count > 0)
            {
                candidateEvaluation.Status = EvaluationStatus.InProgress;
            }
            else
            {
                candidateEvaluation.Status = EvaluationStatus.Pending;
            }

            candidateEvaluation.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }

        public async Task<bool> IsActiveMemberEvaluator(int userId, int committeeId)
        {
            return await _context.CommitteeMembers
                .AnyAsync(cm => cm.User != null && 
                               cm.User.UserId == userId && 
                               cm.CommitteeId == committeeId && 
                               cm.IsActive);
        }
    }
}

using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    public class LoginViewModel
    {
        [Required(ErrorMessage = "رقم الخدمة مطلوب")]
        [Display(Name = "رقم الخدمة")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [Display(Name = "كلمة المرور")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        [Display(Name = "تذكرني")]
        public bool RememberMe { get; set; }
    }

    public class DashboardViewModel
    {
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new List<string>();
        public DateTime? LastLoginAt { get; set; }
        public bool IsAdmin { get; set; }
        public int TotalEvaluations { get; set; }
        public int ActiveEvaluations { get; set; }
        public int CompletedEvaluations { get; set; }
        public int PendingEvaluations { get; set; }
    }

    public class ChangePasswordViewModel
    {
        [Required(ErrorMessage = "Current password is required")]
        [DataType(DataType.Password)]
        [Display(Name = "Current Password")]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "New password is required")]
        [DataType(DataType.Password)]
        [Display(Name = "New Password")]
        [StringLength(100, ErrorMessage = "Password must be at least {2} characters long", MinimumLength = 8)]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]",
            ErrorMessage = "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Please confirm your new password")]
        [DataType(DataType.Password)]
        [Display(Name = "Confirm New Password")]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public class UserManagementViewModel
    {
        public List<UserListItem> Users { get; set; } = new List<UserListItem>();
        public List<RankOption> Ranks { get; set; } = new List<RankOption>();
        public List<RoleOption> Roles { get; set; } = new List<RoleOption>();
    }

    public class UserListItem
    {
        public int UserId { get; set; }
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new List<string>();
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    public class RankOption
    {
        public int RankId { get; set; }
        public string RankName { get; set; } = string.Empty;
    }

    public class RoleOption
    {
        public int RoleId { get; set; }
        public string RoleName { get; set; } = string.Empty;
    }

    public class CreateUserViewModel
    {
        [Required(ErrorMessage = "Service Number is required")]
        [Display(Name = "Service Number")]
        [StringLength(50, ErrorMessage = "Service Number cannot exceed 50 characters")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        [StringLength(100, ErrorMessage = "Password must be at least {2} characters long", MinimumLength = 8)]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]",
            ErrorMessage = "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Please confirm the password")]
        [DataType(DataType.Password)]
        [Display(Name = "Confirm Password")]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Rank is required")]
        [Display(Name = "Rank")]
        public int RankId { get; set; }

        [Display(Name = "Roles")]
        public List<int> SelectedRoleIds { get; set; } = new List<int>();

        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        // Options for dropdowns
        public List<RankOption> AvailableRanks { get; set; } = new List<RankOption>();
        public List<RoleOption> AvailableRoles { get; set; } = new List<RoleOption>();
    }
}

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Models
{
    public class CommitteeMember
    {
        [Key]
        public int CommitteeMemberId { get; set; }

        [Required(ErrorMessage = "اللجنة مطلوبة")]
        [Display(Name = "اللجنة")]
        public int CommitteeId { get; set; }

        [Required(ErrorMessage = "رقم الخدمة مطلوب")]
        [Display(Name = "رقم الخدمة")]
        [StringLength(50, ErrorMessage = "رقم الخدمة لا يمكن أن يتجاوز 50 حرف")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "دور العضو مطلوب")]
        [Display(Name = "دور العضو")]
        public CommitteeMemberRole Role { get; set; } = CommitteeMemberRole.عضو;

        [Display(Name = "ملاحظات")]
        [StringLength(500, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 500 حرف")]
        public string? Notes { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "ترتيب العضو في اللجنة")]
        public int DisplayOrder { get; set; } = 0;

        [Display(Name = "رقم العضو")]
        public int MemberNumber { get; set; } = 0;

        [Display(Name = "يمكنه التقييم")]
        public bool CanEvaluate { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CommitteeId")]
        public virtual Committee Committee { get; set; } = null!;

        // Navigation to User (optional - for when user table is available)
        [ForeignKey("ServiceNumber")]
        public virtual User? User { get; set; }
    }

    public enum CommitteeMemberRole
    {
        [Display(Name = "رئيس اللجنة")]
        رئيس_اللجنة = 1,

        [Display(Name = "عضو")]
        عضو = 2,

        [Display(Name = "منسق")]
        منسق = 3
    }
} 
﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class AddCandidateCommitteeAssignments : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CandidateCommitteeAssignments",
                columns: table => new
                {
                    AssignmentId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CandidateId = table.Column<int>(type: "int", nullable: false),
                    CommitteeId = table.Column<int>(type: "int", nullable: false),
                    EvaluationFormId = table.Column<int>(type: "int", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    AssignedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CandidateCommitteeAssignments", x => x.AssignmentId);
                    table.ForeignKey(
                        name: "FK_CandidateCommitteeAssignments_Candidates_CandidateId",
                        column: x => x.CandidateId,
                        principalTable: "Candidates",
                        principalColumn: "CandidateId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateCommitteeAssignments_Committees_CommitteeId",
                        column: x => x.CommitteeId,
                        principalTable: "Committees",
                        principalColumn: "CommitteeId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateCommitteeAssignments_EvaluationForms_EvaluationFormId",
                        column: x => x.EvaluationFormId,
                        principalTable: "EvaluationForms",
                        principalColumn: "EvaluationFormId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9425));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9919));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9921));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9922));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9923));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9924));

            migrationBuilder.CreateIndex(
                name: "IX_CandidateCommitteeAssignments_CandidateId_CommitteeId_IsActive",
                table: "CandidateCommitteeAssignments",
                columns: new[] { "CandidateId", "CommitteeId", "IsActive" },
                unique: true,
                filter: "[IsActive] = 1");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateCommitteeAssignments_CommitteeId",
                table: "CandidateCommitteeAssignments",
                column: "CommitteeId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateCommitteeAssignments_EvaluationFormId",
                table: "CandidateCommitteeAssignments",
                column: "EvaluationFormId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CandidateCommitteeAssignments");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8436));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8914));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8930));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8931));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8932));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 11, 11, 8, 957, DateTimeKind.Utc).AddTicks(8933));
        }
    }
}

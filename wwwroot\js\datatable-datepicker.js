/**
 * DataTable Date Picker - Simplified Version
 * A lightweight, fast date picker that integrates with DataTables
 */

class DataTableDatePicker {
    constructor(options = {}) {
        this.options = {
            maxAge: options.maxAge || 100,
            messages: {
                invalidDate: 'تاريخ غير صحيح',
                futureDate: 'لا يمكن أن يكون تاريخ الميلاد في المستقبل',
                tooOld: `تاريخ الميلاد يجب أن يكون في آخر ${options.maxAge || 100} سنة`
            }
        };
        this.init();
    }

    /**
     * Initialize the date picker
     */
    init() {
        this.initializeDateInputs();
        this.setupBasicEventListeners();
    }

    /**
     * Initialize all date inputs
     */
    initializeDateInputs() {
        const dateInputs = document.querySelectorAll('input[type="date"], input[id="dateOfBirth"]');
        dateInputs.forEach(input => {
            this.setupDateInput(input);
        });
    }

    /**
     * Setup a single date input
     */
    setupDateInput(input) {
        // Ensure input type is date
        input.type = 'date';
        
        // Remove readonly and disabled attributes
        input.removeAttribute('readonly');
        input.removeAttribute('disabled');
        
        // Set min and max dates
        input.min = this.getMinDate();
        input.max = this.getMaxDate();
        
        // Set default value if empty
        if (!input.value) {
            input.value = '2005-01-01';
        }
        
        // Add event listener for date changes
        input.addEventListener('change', (e) => {
            this.handleDateChange(e.target);
        });
        
        // Add event listener for input
        input.addEventListener('input', (e) => {
            this.handleDateInput(e.target);
        });
        
        // Process existing value (including default)
        if (input.value) {
            this.handleDateChange(input);
        }
    }

    /**
     * Get maximum allowed date (today)
     */
    getMaxDate() {
        const today = new Date();
        return today.toISOString().split('T')[0];
    }

    /**
     * Get minimum allowed date
     */
    getMinDate() {
        const today = new Date();
        const minDate = new Date(today.getFullYear() - this.options.maxAge, today.getMonth(), today.getDate());
        return minDate.toISOString().split('T')[0];
    }

    /**
     * Handle date change event
     */
    handleDateChange(input) {
        const value = input.value;
        
        if (!value) {
            this.clearValidation(input);
            return;
        }

        const validation = this.validateDate(value);
        
        if (validation.isValid) {
            this.showValidState(input);
            this.showAge(input, value);
        } else {
            this.showInvalidState(input, validation.message);
        }
    }

    /**
     * Handle manual date input
     */
    handleDateInput(input) {
        const value = input.value.trim();
        
        // Try to parse different date formats
        const parsedDate = this.parseDate(value);
        if (parsedDate) {
            input.value = parsedDate;
            this.handleDateChange(input);
        }
    }

    /**
     * Parse date from various formats
     */
    parseDate(dateString) {
        const formats = [
            /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
            /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
            /^\d{2}-\d{2}-\d{4}$/, // MM-DD-YYYY
        ];
        
        for (const format of formats) {
            if (format.test(dateString)) {
                const date = new Date(dateString);
                if (!isNaN(date.getTime())) {
                    return date.toISOString().split('T')[0];
                }
            }
        }
        
        return null;
    }

    /**
     * Validate date
     */
    validateDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const minDate = new Date(today.getFullYear() - this.options.maxAge, today.getMonth(), today.getDate());
        
        if (isNaN(date.getTime())) {
            return { isValid: false, message: this.options.messages.invalidDate };
        }
        
        if (date > today) {
            return { isValid: false, message: this.options.messages.futureDate };
        }
        
        if (date < minDate) {
            return { isValid: false, message: this.options.messages.tooOld };
        }
        
        return { isValid: true };
    }

    /**
     * Calculate age
     */
    calculateAge(dateOfBirth) {
        const today = new Date();
        const birthDate = new Date(dateOfBirth);
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        
        return age;
    }

    /**
     * Get age category
     */
    getAgeCategory(age) {
        if (age < 18) return 'قاصر';
        if (age < 25) return 'شاب';
        if (age < 35) return 'شاب بالغ';
        if (age < 50) return 'بالغ';
        if (age < 65) return 'متوسط العمر';
        return 'كبير السن';
    }

    /**
     * Show valid state
     */
    showValidState(input) {
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');
        this.removeFeedback(input);
    }

    /**
     * Show invalid state
     */
    showInvalidState(input, message) {
        input.classList.remove('is-valid');
        input.classList.add('is-invalid');
        this.showFeedback(input, message);
    }

    /**
     * Clear validation state
     */
    clearValidation(input) {
        input.classList.remove('is-valid', 'is-invalid');
        this.removeFeedback(input);
        this.removeAgeDisplay(input);
    }

    /**
     * Show age display
     */
    showAge(input, dateValue) {
        const age = this.calculateAge(dateValue);
        const category = this.getAgeCategory(age);
        
        let ageDisplay = input.parentElement.querySelector('.age-display');
        if (!ageDisplay) {
            ageDisplay = document.createElement('div');
            ageDisplay.className = 'age-display valid';
            input.parentElement.appendChild(ageDisplay);
        }
        
        ageDisplay.innerHTML = `
            <i class="fas fa-birthday-cake me-2"></i>
            العمر: <strong>${age}</strong> سنة
            <span class="badge bg-secondary ms-2">${category}</span>
        `;
        ageDisplay.style.display = 'block';
    }

    /**
     * Remove age display
     */
    removeAgeDisplay(input) {
        const ageDisplay = input.parentElement.querySelector('.age-display');
        if (ageDisplay) {
            ageDisplay.style.display = 'none';
        }
    }

    /**
     * Show feedback message
     */
    showFeedback(input, message) {
        let feedback = input.parentElement.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            input.parentElement.appendChild(feedback);
        }
        feedback.textContent = message;
    }

    /**
     * Remove feedback message
     */
    removeFeedback(input) {
        const feedback = input.parentElement.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = '';
        }
    }

    /**
     * Setup basic event listeners
     */
    setupBasicEventListeners() {
        // Form submission validation
        document.addEventListener('submit', (e) => {
            const dateInputs = e.target.querySelectorAll('input[type="date"], input[id="dateOfBirth"]');
            let hasErrors = false;
            
            dateInputs.forEach(input => {
                if (input.value) {
                    const validation = this.validateDate(input.value);
                    if (!validation.isValid) {
                        hasErrors = true;
                        this.showInvalidState(input, validation.message);
                    }
                }
            });
            
            if (hasErrors) {
                e.preventDefault();
                alert('يرجى تصحيح الأخطاء في حقول التاريخ قبل الإرسال');
            }
        });
    }

    /**
     * Static method to initialize
     */
    static init(options = {}) {
        return new DataTableDatePicker(options);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    DataTableDatePicker.init({
        maxAge: 100
    });
});

// Export for global access
window.DataTableDatePicker = DataTableDatePicker;
@model RafoEvaluation.ViewModels.CandidateViewModel
@{
    ViewData["Title"] = "تفاصيل المرشح";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-users breadcrumb-icon"></i>
                    تفاصيل المرشح
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Candidate">
                                <i class="fas fa-users"></i> المرشحين
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-eye"></i> تفاصيل
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>تفاصيل المرشح
                    </h3>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.CandidateId" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-list me-1"></i>العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>المعلومات الشخصية
                            </h5>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">الاسم الكامل:</label>
                                <p class="form-control-plaintext">@Model.FullName</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">رقم الخدمة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-secondary">@Model.ServiceNumber</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">رقم الهوية الوطنية:</label>
                                <p class="form-control-plaintext">@Model.NationalIdNumber</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الميلاد:</label>
                                <p class="form-control-plaintext">
                                    @Model.DateOfBirth.ToString("dd MMMM yyyy")
                                    <span class="text-muted">(@Model.Age سنة)</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">المسمى الوظيفي:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.JobTitle))
                                    {
                                        @Model.JobTitle
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <!-- Official Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-id-badge me-2"></i>المعلومات الرسمية
                            </h5>

                            <div class="mb-3">
                                <label class="form-label fw-bold">الفئة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info">@Model.CategoryName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">الرتبة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-warning text-dark">@Model.RankName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">القاعدة الجوية:</label>
                                <p class="form-control-plaintext">@Model.AirbaseId</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">القسم:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.Department))
                                    {
                                        @Model.Department
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غير نشط</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Education Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-graduation-cap me-2"></i>معلومات تعليمية
                            </h5>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الجامعة:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.University))
                                    {
                                        @Model.University
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">التخصص:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.Major))
                                    {
                                        @Model.Major
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label fw-bold">سنة التخرج:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.GraduationYear.HasValue)
                                    {
                                        @Model.GraduationYear.Value
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label fw-bold">درجة التقدير:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.MarksGrade))
                                    {
                                        <span class="badge bg-success">@Model.MarksGrade</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>معلومات النظام
                            </h5>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                <p class="form-control-plaintext">
                                    @Model.CreatedAt.ToString("dd MMMM yyyy, HH:mm")
                                </p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ التحديث:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.UpdatedAt.HasValue)
                                    {
                                        @Model.UpdatedAt.Value.ToString("dd MMMM yyyy, HH:mm")
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">لم يتم التحديث</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

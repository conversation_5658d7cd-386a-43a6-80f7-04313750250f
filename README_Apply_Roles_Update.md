# كيفية تطبيق تحديث الصلاحيات للعربية

## المشكلة
صفحة إدارة المستخدمين لا تزال تعرض الصلاحيات باللغة الإنجليزية لأن قاعدة البيانات لم يتم تحديثها بعد.

## الحل

### الطريقة الأولى: تشغيل Script SQL مباشرة

1. **فتح قاعدة البيانات**:
   - افتح SQL Server Management Studio أو أي أداة إدارة قواعد البيانات
   - اتصل بقاعدة البيانات `RafoEvaluation`

2. **تشغيل Script التحديث**:
   ```sql
   -- حذف الصلاحيات القديمة غير المستخدمة
   DELETE FROM UserRoles WHERE RoleId IN (5, 6, 7, 8, 9, 10);
   DELETE FROM Roles WHERE RoleId IN (5, 6, 7, 8, 9, 10);

   -- تحديث أسماء الصلاحيات للعربية
   UPDATE Roles SET RoleName = 'مدير' WHERE RoleId = 1;
   UPDATE Roles SET RoleName = 'منسق' WHERE RoleId = 2;
   UPDATE Roles SET RoleName = 'مقيم' WHERE RoleId = 3;
   UPDATE Roles SET RoleName = 'خلية إدارية' WHERE RoleId = 4;

   -- التحقق من التحديث
   SELECT RoleId, RoleName, Description FROM Roles ORDER BY RoleId;
   ```

3. **التحقق من النتائج**:
   - يجب أن تظهر الصلاحيات باللغة العربية
   - يجب أن يكون هناك 4 صلاحيات فقط

### الطريقة الثانية: استخدام ملف SQL

1. **تشغيل الملف** `update_roles_to_arabic.sql`:
   - افتح الملف في أداة إدارة قواعد البيانات
   - قم بتشغيل المحتوى

### الطريقة الثالثة: استخدام PowerShell

```powershell
# تشغيل Script SQL من PowerShell
$connectionString = "Data Source=.;Initial Catalog=RafoEvaluation;Integrated Security=True"
$sqlScript = Get-Content "update_roles_to_arabic.sql" -Raw

$connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
$command = New-Object System.Data.SqlClient.SqlCommand($sqlScript, $connection)

try {
    $connection.Open()
    $command.ExecuteNonQuery()
    Write-Host "تم تحديث الصلاحيات بنجاح!"
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)"
} finally {
    $connection.Close()
}
```

## التحقق من التحديث

### 1. فحص قاعدة البيانات
```sql
SELECT RoleId, RoleName, Description FROM Roles ORDER BY RoleId;
```

**النتيجة المتوقعة**:
```
RoleId  RoleName        Description
1       مدير           مدير النظام مع صلاحيات كاملة
2       منسق           منسق التقييم يدير عملية التقييم
3       مقيم           مقيم يقوم بتقييم المرشحين
4       خلية إدارية    خلية إدارية تدير البيانات الأساسية والمرشحين والمقيمين
```

### 2. فحص صفحة إدارة المستخدمين
- افتح صفحة إدارة المستخدمين
- تحقق من أن الصلاحيات تظهر باللغة العربية
- تحقق من أن جميع الوظائف تعمل بشكل صحيح

### 3. فحص تسجيل الدخول
- جرب تسجيل الدخول بصلاحيات مختلفة
- تحقق من أن الصلاحيات تعمل بشكل صحيح

## تحديثات مطلوبة في الكود

### 1. تحديث Controllers
```csharp
// قبل التحديث
[Authorize(Roles = "Admin")]

// بعد التحديث
[Authorize(Roles = "مدير")]
```

### 2. تحديث Views
```html
<!-- قبل التحديث -->
@if (User.IsInRole("Admin"))

<!-- بعد التحديث -->
@if (User.IsInRole("مدير"))
```

## الملفات المحدثة

### 1. `Controllers/UserController.cs`
- تم تحديث `[Authorize(Roles = "Admin")]` إلى `[Authorize(Roles = "مدير")]`

### 2. `update_roles_to_arabic.sql`
- Script SQL لتحديث قاعدة البيانات

### 3. `README_Apply_Roles_Update.md`
- هذا الملف - دليل التطبيق

## ملاحظات مهمة

### 1. النسخ الاحتياطية
- قم بإنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث
- احتفظ بنسخة من البيانات الحالية

### 2. الاختبار
- اختبر النظام بعد التحديث
- تأكد من عمل جميع الوظائف
- تحقق من الصلاحيات

### 3. الأمان
- تأكد من أن التحديث لا يؤثر على الأمان
- تحقق من أن الصلاحيات تعمل بشكل صحيح

## استكشاف الأخطاء

### إذا لم تظهر الصلاحيات بالعربية:
1. تحقق من تشغيل Script SQL بنجاح
2. تحقق من أن قاعدة البيانات تم تحديثها
3. أعد تشغيل التطبيق

### إذا لم تعمل الصلاحيات:
1. تحقق من تحديث الكود
2. تحقق من أن أسماء الصلاحيات صحيحة
3. تحقق من قاعدة البيانات

### إذا حدث خطأ في Script:
1. تحقق من اتصال قاعدة البيانات
2. تحقق من صلاحيات المستخدم
3. تحقق من صحة SQL

## النتائج المتوقعة

### بعد التطبيق:
- ✅ أسماء الصلاحيات باللغة العربية
- ✅ حذف الصلاحيات غير المستخدمة
- ✅ نظام أكثر وضوحاً
- ✅ سهولة الاستخدام للمستخدمين العرب

### في واجهة المستخدم:
- ستظهر الصلاحيات بالعربية في جميع أنحاء النظام
- رسائل الخطأ والنجاح بالعربية
- قوائم منسدلة بالعربية 
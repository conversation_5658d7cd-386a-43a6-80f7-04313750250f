using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;

namespace RafoEvaluation.Services
{
    public class CommitteeMemberOrderService : ICommitteeMemberOrderService
    {
        private readonly ApplicationDbContext _context;

        public CommitteeMemberOrderService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<int> GetNextMemberNumber(int committeeId)
        {
            var maxNumber = await _context.CommitteeMembers
                .Where(cm => cm.CommitteeId == committeeId && 
                           cm.Role != CommitteeMemberRole.منسق)
                .MaxAsync(cm => (int?)cm.MemberNumber) ?? 0;
            
            return maxNumber + 1;
        }

        public async Task<string> GetMemberDisplayName(int memberNumber, CommitteeMemberRole role)
        {
            var roleText = role switch
            {
                CommitteeMemberRole.رئيس_اللجنة => "رئيس اللجنة (العضو الأول)",
                CommitteeMemberRole.عضو => $"العضو {GetArabicNumber(memberNumber)}",
                CommitteeMemberRole.منسق => "منسق",
                _ => "عضو"
            };
            
            return roleText;
        }

        public async Task<int> GetEvaluatorCount(int committeeId)
        {
            var members = await _context.CommitteeMembers
                .Where(cm => cm.CommitteeId == committeeId && 
                           cm.IsActive && 
                           cm.Role != CommitteeMemberRole.منسق)
                .ToListAsync();

            int count = 0;
            foreach (var member in members)
            {
                if (await CanMemberEvaluate(member))
                {
                    count++;
                }
            }

            return count;
        }

        public async Task<List<CommitteeMember>> GetEvaluators(int committeeId)
        {
            var members = await _context.CommitteeMembers
                .Include(cm => cm.User)
                .ThenInclude(u => u.Rank)
                .Where(cm => cm.CommitteeId == committeeId && 
                           cm.IsActive && 
                           cm.Role != CommitteeMemberRole.منسق)
                .OrderBy(cm => cm.MemberNumber)
                .ToListAsync();

            // تصفية الأعضاء بناءً على صلاحيات التقييم
            var evaluators = new List<CommitteeMember>();
            foreach (var member in members)
            {
                if (await CanMemberEvaluate(member))
                {
                    evaluators.Add(member);
                }
            }

            return evaluators;
        }

        public async Task<bool> CanMemberEvaluate(CommitteeMember member)
        {
            if (member == null || !member.IsActive)
                return false;

            // رئيس اللجنة وأعضاء اللجنة يمكنهم التقييم افتراضياً
            if (member.Role == CommitteeMemberRole.رئيس_اللجنة || member.Role == CommitteeMemberRole.عضو)
                return true;

            // المنسقون يمكنهم التقييم فقط إذا كان CanEvaluate = true
            if (member.Role == CommitteeMemberRole.منسق)
                return member.CanEvaluate;

            return false;
        }

        public async Task<bool> CanMemberEvaluate(int committeeMemberId)
        {
            var member = await _context.CommitteeMembers
                .FirstOrDefaultAsync(cm => cm.CommitteeMemberId == committeeMemberId);
            
            return await CanMemberEvaluate(member);
        }

        public async Task<bool> ValidateMemberOrder(int committeeId)
        {
            var members = await _context.CommitteeMembers
                .Where(cm => cm.CommitteeId == committeeId && 
                           cm.IsActive && 
                           cm.Role != CommitteeMemberRole.منسق)
                .OrderBy(cm => cm.MemberNumber)
                .ToListAsync();

            // التحقق من وجود رئيس لجنة واحد فقط
            var chairmen = members.Where(m => m.Role == CommitteeMemberRole.رئيس_اللجنة).ToList();
            if (chairmen.Count > 1)
                return false;

            // التحقق من أن رئيس اللجنة هو العضو الأول
            if (chairmen.Any() && chairmen.First().MemberNumber != 1)
                return false;

            // التحقق من عدم تكرار أرقام الأعضاء
            var memberNumbers = members.Select(m => m.MemberNumber).ToList();
            if (memberNumbers.Count != memberNumbers.Distinct().Count())
                return false;

            return true;
        }

        public async Task ReorderMembers(int committeeId)
        {
            var members = await _context.CommitteeMembers
                .Where(cm => cm.CommitteeId == committeeId && 
                           cm.IsActive && 
                           cm.Role != CommitteeMemberRole.منسق)
                .OrderBy(cm => cm.Role)
                .ThenBy(cm => cm.CreatedAt)
                .ToListAsync();

            int currentNumber = 1;
            foreach (var member in members)
            {
                member.MemberNumber = currentNumber;
                currentNumber++;
            }

            await _context.SaveChangesAsync();
        }

        public async Task<string> GetFormattedMemberName(CommitteeMember member)
        {
            var roleText = member.Role switch
            {
                CommitteeMemberRole.رئيس_اللجنة => "رئيس اللجنة (العضو الأول)",
                CommitteeMemberRole.عضو => $"العضو {GetArabicNumber(member.MemberNumber)}",
                CommitteeMemberRole.منسق => "منسق",
                _ => "عضو"
            };
            
            var fullName = !string.IsNullOrEmpty(member.User?.FullName) ? member.User.FullName : member.ServiceNumber;
            var rank = !string.IsNullOrEmpty(member.User?.Rank?.RankName) ? $"{member.User.Rank.RankName} " : "";
            
            return $"{rank}{fullName} ({roleText})";
        }

        private string GetArabicNumber(int number)
        {
            return number switch
            {
                1 => "الأول",
                2 => "الثاني", 
                3 => "الثالث",
                4 => "الرابع",
                5 => "الخامس",
                6 => "السادس",
                7 => "السابع",
                8 => "الثامن",
                9 => "التاسع",
                10 => "العاشر",
                _ => number.ToString()
            };
        }
    }
} 
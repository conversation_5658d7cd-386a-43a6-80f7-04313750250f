{"ConnectionStrings": {"DefaultConnection": "Server=ALMAZIDI\\MSSQLSERVER2022;Database=RafoEvaluation;Trusted_Connection=true;TrustServerCertificate=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30}}], "Enrich": ["FromLogContext"]}, "Development": {"SeedData": false, "EnableSwagger": false, "EnableDeveloperExceptionPage": true, "EnableDatabaseErrorPage": true}, "AllowedHosts": "*"}
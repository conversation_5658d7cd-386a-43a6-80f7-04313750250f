﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class FixMaxScoreValues : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Fix MaxScore values for EvaluationFormItems
            migrationBuilder.Sql("UPDATE EvaluationFormItems SET MaxScore = 10 WHERE EvaluationFormId = 1 AND MaxScore = 0");
            
            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(883));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1353));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1355));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1356));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1357));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1358));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(2047));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3790));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3798));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3800));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3803));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3805));
        }
    }
}

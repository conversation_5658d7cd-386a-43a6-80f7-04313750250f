# سكريبت إدراج 60 مرشح من مختلف الفئات

## نظرة عامة

تم إنشاء سكريبت SQL لإدراج 60 مرشح من مختلف الفئات في نظام تقييم المرشحين لسلاح الجو السلطاني العماني. السكريبت يتبع المتطلبات المحددة ويضمن التوزيع المناسب للفئات.

## المتطلبات المطلوبة

### التوزيع المطلوب:
- **المرشحين الطيارين**: الفئة الأكبر (15 مرشح)
- **المرشحين الجامعيين العسكريين**: الفئة الثانية (12 مرشح)
- **المرشحين الجامعيين المدنيين**: (8 مرشح)
- **ض<PERSON><PERSON><PERSON> الصف (رقباء/عرفاء)**: (10 مرشح)
- **ضباط الصف الكلية التقنية العسكرية**: (8 مرشح)
- **ضباط الصف المدنيين للترفيع**: (7 مرشح)
- **المرشحات الإناث**: (5 مرشحات)

## خصائص البيانات

### الأسماء:
- أسماء عمانية أصيلة مع أسماء عائلات عمانية معروفة
- استخدام أسماء العائلات: آل سعيد، البوسعيدي، العيسائي، الشحي، الكندي، الرواحي، السعدي، البادي، الزكواني، الشكيلي، المطروشي، العبري
- 5 مرشحات إناث بأسماء عمانية مناسبة

### أرقام الخدمة:
- نمط: `RAF-2024-XXX` (حيث XXX من 001 إلى 065)
- فريدة ومتسلسلة لكل مرشح

### أرقام الهوية الوطنية:
- نمط: `1234567XXX` (حيث XXX من 890 إلى 927)
- مطلوبة للفئات المدنية والطيارين
- غير مطلوبة لضباط الصف

### التوزيع على القواعد الجوية:
- **قاعدة غلا وأكاديمية السلطان قابوس الجوية**: الطيارين والمرشحات الطيارات
- **قيادة سلاح الجو السلطاني العماني**: الجامعيين العسكريين
- **قاعدة السيب الجوية**: الجامعيين المدنيين
- **قاعدة صلالة الجوية**: ضباط الصف (رقباء/عرفاء)
- **قاعدة المصنعة الجوية**: ضباط الصف الكلية التقنية
- **قاعدة مصيرة الجوية**: ضباط الصف المدنيين للترفيع

### الرتب:
- **ضابط مرشح (8)**: الطيارين والمرشحات الطيارات
- **ملازم ثاني (9)**: الجامعيين العسكريين
- **مدني درجة 16 (18)**: الجامعيين المدنيين
- **رقيب (4)**: جميع ضباط الصف

### البيانات التعليمية:
- **الطيارين**: أكاديمية السلطان قابوس الجوية، تخصص علوم الطيران
- **الجامعيين العسكريين**: جامعة السلطان قابوس، تخصصات هندسية متنوعة
- **الجامعيين المدنيين**: جامعة السلطان قابوس، تخصصات إدارية واقتصادية

### سنوات التخرج:
- **الطيارين**: 2023
- **الجامعيين العسكريين**: 2022
- **الجامعيين المدنيين**: 2021

### التقديرات:
- توزيع متوازن بين "ممتاز" و "جيد جداً"

## الفئات المدعومة

| الكود | اسم الفئة | عدد المرشحين |
|-------|-----------|---------------|
| CAT-PLT | المرشحين الطيارين | 16 (15 + 1 أنثى) |
| CAT-MUG | المرشحين الجامعيين العسكريين | 13 (12 + 1 أنثى) |
| CAT-CUG | المرشحين الجامعيين المدنيين | 9 (8 + 1 أنثى) |
| CAT-NCO | ضباط الصف (رقباء/عرفاء) | 11 (10 + 1 أنثى) |
| CAT-TCN | ضباط الصف الكلية التقنية العسكرية | 9 (8 + 1 أنثى) |
| CAT-CNP | ضباط الصف المدنيين للترفيع | 7 |

## المرشحات الإناث

1. **فاطمة بنت أحمد بن محمد العيسائية** - طيار مرشح
2. **مريم بنت سعيد بن علي البوسعيدية** - مهندسة طيران
3. **عائشة بنت محمد بن راشد الكندية** - مديرة إدارية
4. **خديجة بنت عبدالله بن أحمد الشحية** - رقيبة صيانة
5. **زينب بنت سليمان بن محمد الرواحية** - فنية تقنية

## متطلبات التشغيل

### قبل تشغيل السكريبت:
1. التأكد من وجود الفئات في جدول `Categories`
2. التأكد من وجود الرتب في جدول `Ranks`
3. التأكد من وجود القواعد الجوية في جدول `Airbases`

### التحقق من البيانات الأساسية:
```sql
-- التحقق من الفئات
SELECT * FROM Categories WHERE CategoryCode IN ('CAT-PLT', 'CAT-MUG', 'CAT-CUG', 'CAT-NCO', 'CAT-TCN', 'CAT-CNP');

-- التحقق من الرتب
SELECT * FROM Ranks WHERE RankId IN (4, 8, 9, 18);

-- التحقق من القواعد الجوية
SELECT * FROM Airbases WHERE AirbaseId IN (1, 2, 3, 4, 5, 6);
```

## تشغيل السكريبت

```sql
-- تشغيل السكريبت
EXEC insert_60_candidates.sql
```

## التحقق من النتائج

بعد تشغيل السكريبت، يمكن التحقق من النتائج باستخدام الاستعلامات المضمنة في السكريبت أو:

```sql
-- إجمالي المرشحين
SELECT COUNT(*) as TotalCandidates FROM Candidates WHERE ServiceNumber LIKE 'RAF-2024-%';

-- توزيع المرشحين حسب الفئة
SELECT 
    cat.CategoryName,
    COUNT(*) as Count
FROM Candidates c
JOIN Categories cat ON c.CategoryId = cat.CategoryId
WHERE c.ServiceNumber LIKE 'RAF-2024-%'
GROUP BY cat.CategoryName, cat.CategoryId
ORDER BY cat.CategoryId;

-- المرشحات الإناث
SELECT FullName, ServiceNumber, JobTitle
FROM Candidates 
WHERE FullName LIKE '%بنت%' AND ServiceNumber LIKE 'RAF-2024-%';
```

## ملاحظات مهمة

1. **المعاملة**: السكريبت يستخدم معاملة (Transaction) لضمان سلامة البيانات
2. **التحقق**: يتم التحقق من وجود البيانات الأساسية قبل الإدراج
3. **التوثيق**: تم توثيق السكريبت بواسطة AI Assistant كما هو مطلوب
4. **التتبع**: يمكن تتبع المرشحين المدرجين بواسطة نمط أرقام الخدمة `RAF-2024-*`

## الأمان والتحقق

- جميع البيانات تم إنشاؤها بواسطة AI Assistant وتم مراجعتها
- الأسماء مستوحاة من الأسماء العمانية الأصيلة
- أرقام الهوية الوطنية فريدة ومتسلسلة
- أرقام الخدمة فريدة ومتسلسلة

## الدعم

في حالة وجود أي مشاكل أو استفسارات، يرجى مراجعة:
1. سجلات الأخطاء في قاعدة البيانات
2. التحقق من وجود البيانات الأساسية المطلوبة
3. التأكد من صلاحيات المستخدم في قاعدة البيانات 
@model RafoEvaluation.ViewModels.UserDetailsViewModel
@{
    ViewData["Title"] = "حذف المستخدم";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-trash breadcrumb-icon"></i>
                    حذف المستخدم
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="User">
                                <i class="fas fa-users"></i> المستخدمين
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-trash"></i> حذف المستخدم
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="fas fa-exclamation-triangle ms-2 text-dark"></i>تأكيد حذف المستخدم
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> تحذير!</h5>
                        <p>هل أنت متأكد من حذف المستخدم <strong>@Model.ServiceNumber</strong>؟</p>
                        <p>هذا الإجراء سيحذف:</p>
                        <ul>
                            <li>حساب المستخدم بالكامل</li>
                            <li>جميع الصلاحيات المرتبطة به</li>
                            <li>جميع البيانات المرتبطة به في النظام</li>
                        </ul>
                        <p><strong>هذا الإجراء لا يمكن التراجع عنه.</strong></p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">رقم الخدمة:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.ServiceNumber</strong>
                                </dd>

                                <dt class="col-sm-4">الرتبة:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-warning text-dark">@Model.RankName</span>
                                </dd>

                                <dt class="col-sm-4">الصلاحيات:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.RoleNames.Any())
                                    {
                                        @foreach (var role in Model.RoleNames)
                                        {
                                            <span class="badge bg-primary me-1">@role</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">لا توجد صلاحيات</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">الحالة:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غير نشط</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                                <dd class="col-sm-8">
                                    <span class="text-muted">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                                </dd>

                                <dt class="col-sm-4">آخر تسجيل دخول:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.LastLoginAt.HasValue)
                                    {
                                        <span class="text-muted">@Model.LastLoginAt.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">لم يسجل دخول</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <form asp-action="Delete" method="post">
                        <div class="form-group">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash ms-1"></i>تأكيد الحذف
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.UserId" class="btn btn-secondary">
                                <i class="fas fa-arrow-right ms-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 
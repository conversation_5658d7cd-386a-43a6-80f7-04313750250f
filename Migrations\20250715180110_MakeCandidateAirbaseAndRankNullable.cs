﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class MakeCandidateAirbaseAndRankNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "RankId",
                table: "Candidates",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "AirbaseId",
                table: "Candidates",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(3734));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4607));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4609));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4610));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4611));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4612));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4613));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4628));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4629));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4630));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4632));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4633));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4634));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4635));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4636));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4637));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1029));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1635));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1637));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1638));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1639));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1640));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "RankId",
                table: "Candidates",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "AirbaseId",
                table: "Candidates",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(2906));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3557));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3559));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3559));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3560));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3561));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3562));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3563));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3564));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3565));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3565));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3566));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3567));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3568));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3569));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3570));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8250));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8751));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8753));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8754));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8755));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8756));
        }
    }
}

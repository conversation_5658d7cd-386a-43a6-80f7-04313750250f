using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.Models
{
    public class PathStage
    {
        [Key]
        public int PathStageId { get; set; }

        [Required]
        public int CommitteePathId { get; set; }

        [Required(ErrorMessage = "اسم المرحلة مطلوب")]
        [Display(Name = "اسم المرحلة")]
        [StringLength(100, ErrorMessage = "اسم المرحلة لا يمكن أن يتجاوز 100 حرف")]
        public string StageName { get; set; } = string.Empty;

        [Display(Name = "وصف المرحلة")]
        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "ترتيب المرحلة مطلوب")]
        [Display(Name = "ترتيب المرحلة")]
        [Range(1, 100, ErrorMessage = "الترتيب يجب أن يكون بين 1 و 100")]
        public int StageOrder { get; set; }

        [Display(Name = "المدة المتوقعة (بالأيام)")]
        [Range(1, 90, ErrorMessage = "المدة يجب أن تكون بين 1 و 90 يوم")]
        public int ExpectedDuration { get; set; } = 7;

        [Display(Name = "نوع المرحلة")]
        public StageType Type { get; set; }

        [Display(Name = "متطلبات المرحلة")]
        [StringLength(1000, ErrorMessage = "المتطلبات لا يمكن أن تتجاوز 1000 حرف")]
        public string? Requirements { get; set; }

        [Display(Name = "معايير النجاح")]
        [StringLength(1000, ErrorMessage = "معايير النجاح لا يمكن أن تتجاوز 1000 حرف")]
        public string? SuccessCriteria { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual CommitteePath CommitteePath { get; set; } = null!;
    }

    public enum StageType
    {
        [Display(Name = "مراجعة الوثائق")]
        DocumentReview = 1,
        
        [Display(Name = "مقابلة شخصية")]
        PersonalInterview = 2,
        
        [Display(Name = "اختبار نظري")]
        WrittenTest = 3,
        
        [Display(Name = "اختبار عملي")]
        PracticalTest = 4,
        
        [Display(Name = "فحص طبي")]
        MedicalExamination = 5,
        
        [Display(Name = "تقييم نفسي")]
        PsychologicalAssessment = 6,
        
        [Display(Name = "تقييم بدني")]
        PhysicalAssessment = 7,
        
        [Display(Name = "تقييم تقني")]
        TechnicalAssessment = 8,
        
        [Display(Name = "مراجعة نهائية")]
        FinalReview = 9,
        
        [Display(Name = "أخرى")]
        Other = 10
    }
} 
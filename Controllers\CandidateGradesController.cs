using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.Models.Auth;
using RafoEvaluation.ViewModels;
using System.Security.Claims;

namespace RafoEvaluation.Controllers
{
    [Authorize]
    public class CandidateGradesController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CandidateGradesController> _logger;

        public CandidateGradesController(ApplicationDbContext context, ILogger<CandidateGradesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: CandidateGrades
        public async Task<IActionResult> Index(string? searchTerm, int? committeeId, EvaluationStatus? status, int page = 1)
        {
            try
            {
                // التحقق من صلاحيات المستخدم
                var currentUser = await GetCurrentUser();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Account");
                }

                var canViewGrades = await CanUserViewGrades(currentUser);
                if (!canViewGrades)
                {
                    _logger.LogWarning($"المستخدم {currentUser.ServiceNumber} حاول الوصول لصفحة درجات المرشحين بدون صلاحية");
                    return RedirectToAction("AccessDenied", "Account");
                }

                var pageSize = 10;
                var query = _context.CandidateEvaluations
                    .Include(ce => ce.Candidate)
                        .ThenInclude(c => c.Rank)
                    .Include(ce => ce.Candidate)
                        .ThenInclude(c => c.Category)
                    .Include(ce => ce.Committee)
                    .Include(ce => ce.EvaluationForm)
                    .Include(ce => ce.IndividualEvaluations)
                        .ThenInclude(ie => ie.Evaluator)
                            .ThenInclude(e => e.Rank)
                    .AsQueryable();

                // تطبيق الفلاتر
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(ce => 
                        ce.Candidate.FullName.Contains(searchTerm) ||
                        ce.Candidate.ServiceNumber.Contains(searchTerm) ||
                        ce.Committee.CommitteeName.Contains(searchTerm));
                }

                if (committeeId.HasValue)
                {
                    query = query.Where(ce => ce.CommitteeId == committeeId.Value);
                }

                if (status.HasValue)
                {
                    query = query.Where(ce => ce.Status == status.Value);
                }

                // تطبيق صلاحيات المستخدم على اللجان
                if (!currentUser.IsAdmin)
                {
                    var userCommittees = await GetUserCommittees(currentUser);
                    query = query.Where(ce => userCommittees.Contains(ce.CommitteeId));
                }

                var totalCount = await query.CountAsync();
                var candidateEvaluations = await query
                    .OrderByDescending(ce => ce.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var viewModels = candidateEvaluations.Select(ce => new CandidateGradesViewModel
                {
                    CandidateEvaluationId = ce.CandidateEvaluationId,
                    CandidateName = ce.Candidate.FullName,
                    ServiceNumber = ce.Candidate.ServiceNumber,
                    RankName = ce.Candidate.Rank?.RankName ?? "",
                    CategoryName = ce.Candidate.Category?.CategoryName ?? "",
                    CommitteeName = ce.Committee.CommitteeName,
                    EvaluationFormTitle = ce.EvaluationForm.Title,
                    Status = ce.Status,
                    TotalScore = ce.TotalScore,
                    AverageScore = ce.AverageScore,
                    EvaluatorCount = ce.EvaluatorCount,
                    PresentEvaluatorCount = ce.PresentEvaluatorCount,
                    AbsentEvaluatorCount = ce.AbsentEvaluatorCount,
                    StartedAt = ce.StartedAt,
                    CompletedAt = ce.CompletedAt,
                    CreatedAt = ce.CreatedAt,
                    GeneralNotes = ce.GeneralNotes,
                    EvaluatorScores = ce.IndividualEvaluations.Select(ie => new EvaluatorScoreViewModel
                    {
                        IndividualEvaluationId = ie.IndividualEvaluationId,
                        EvaluatorName = ie.Evaluator.FullName,
                        EvaluatorRank = ie.Evaluator.Rank?.RankName ?? "",
                        EvaluatorServiceNumber = ie.Evaluator.ServiceNumber,
                        CommitteeRole = GetCommitteeRole(ie.Evaluator.ServiceNumber, ce.CommitteeId),
                        TotalScore = ie.TotalScore,
                        EvaluatorNotes = ie.EvaluatorNotes,
                        IsPresent = ie.IsPresent,
                        EvaluatedAt = ie.EvaluatedAt,
                        CriteriaScores = ie.CriteriaScores.Select(iec => new CriteriaScoreViewModel
                        {
                            CriteriaId = iec.EvaluationFormItemId,
                            CriteriaName = iec.EvaluationFormItem.Criteria,
                            CriteriaDescription = iec.EvaluationFormItem.Description ?? "",
                            MaxScore = iec.EvaluationFormItem.MaxScore,
                            Score = iec.Score,
                            Notes = null, // يمكن إضافة حقل للملاحظات لاحقاً
                            EvaluatorName = ie.Evaluator.FullName
                        }).ToList()
                    }).ToList()
                }).ToList();

                var availableCommittees = await _context.Committees
                    .Where(c => c.Status == CommitteeStatus.Active)
                    .OrderBy(c => c.CommitteeName)
                    .ToListAsync();

                var viewModel = new CandidateGradesListViewModel
                {
                    Candidates = viewModels,
                    TotalCount = totalCount,
                    PageNumber = page,
                    PageSize = pageSize,
                    SearchTerm = searchTerm,
                    CommitteeId = committeeId,
                    Status = status,
                    AvailableCommittees = availableCommittees,
                    CanViewGrades = canViewGrades,
                    CurrentUserRole = GetUserRole(currentUser)
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عرض درجات المرشحين");
                TempData["Error"] = "حدث خطأ أثناء عرض درجات المرشحين";
                return RedirectToAction("Index", "Home");
            }
        }

        // GET: CandidateGrades/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var currentUser = await GetCurrentUser();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Account");
                }

                var canViewGrades = await CanUserViewGrades(currentUser);
                if (!canViewGrades)
                {
                    return RedirectToAction("AccessDenied", "Account");
                }

                var candidateEvaluation = await _context.CandidateEvaluations
                    .Include(ce => ce.Candidate)
                        .ThenInclude(c => c.Rank)
                    .Include(ce => ce.Candidate)
                        .ThenInclude(c => c.Category)
                    .Include(ce => ce.Committee)
                    .Include(ce => ce.EvaluationForm)
                    .Include(ce => ce.IndividualEvaluations)
                        .ThenInclude(ie => ie.Evaluator)
                            .ThenInclude(e => e.Rank)
                    .Include(ce => ce.IndividualEvaluations)
                        .ThenInclude(ie => ie.CriteriaScores)
                            .ThenInclude(iec => iec.EvaluationFormItem)
                    .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == id);

                if (candidateEvaluation == null)
                {
                    return NotFound();
                }

                // التحقق من صلاحية الوصول للجنة
                if (!currentUser.IsAdmin)
                {
                    var userCommittees = await GetUserCommittees(currentUser);
                    if (!userCommittees.Contains(candidateEvaluation.CommitteeId))
                    {
                        return RedirectToAction("AccessDenied", "Account");
                    }
                }

                var viewModel = new CandidateGradesViewModel
                {
                    CandidateEvaluationId = candidateEvaluation.CandidateEvaluationId,
                    CandidateName = candidateEvaluation.Candidate.FullName,
                    ServiceNumber = candidateEvaluation.Candidate.ServiceNumber,
                    RankName = candidateEvaluation.Candidate.Rank?.RankName ?? "",
                    CategoryName = candidateEvaluation.Candidate.Category?.CategoryName ?? "",
                    CommitteeName = candidateEvaluation.Committee.CommitteeName,
                    EvaluationFormTitle = candidateEvaluation.EvaluationForm.Title,
                    Status = candidateEvaluation.Status,
                    TotalScore = candidateEvaluation.TotalScore,
                    AverageScore = candidateEvaluation.AverageScore,
                    EvaluatorCount = candidateEvaluation.EvaluatorCount,
                    PresentEvaluatorCount = candidateEvaluation.PresentEvaluatorCount,
                    AbsentEvaluatorCount = candidateEvaluation.AbsentEvaluatorCount,
                    StartedAt = candidateEvaluation.StartedAt,
                    CompletedAt = candidateEvaluation.CompletedAt,
                    CreatedAt = candidateEvaluation.CreatedAt,
                    GeneralNotes = candidateEvaluation.GeneralNotes,
                    EvaluatorScores = candidateEvaluation.IndividualEvaluations.Select(ie => new EvaluatorScoreViewModel
                    {
                        IndividualEvaluationId = ie.IndividualEvaluationId,
                        EvaluatorName = ie.Evaluator.FullName,
                        EvaluatorRank = ie.Evaluator.Rank?.RankName ?? "",
                        EvaluatorServiceNumber = ie.Evaluator.ServiceNumber,
                        CommitteeRole = GetCommitteeRole(ie.Evaluator.ServiceNumber, candidateEvaluation.CommitteeId),
                        TotalScore = ie.TotalScore,
                        EvaluatorNotes = ie.EvaluatorNotes,
                        IsPresent = ie.IsPresent,
                        EvaluatedAt = ie.EvaluatedAt,
                        CriteriaScores = ie.CriteriaScores.Select(iec => new CriteriaScoreViewModel
                        {
                            CriteriaId = iec.EvaluationFormItemId,
                            CriteriaName = iec.EvaluationFormItem.Criteria,
                            CriteriaDescription = iec.EvaluationFormItem.Description ?? "",
                            MaxScore = iec.EvaluationFormItem.MaxScore,
                            Score = iec.Score,
                            Notes = null, // يمكن إضافة حقل للملاحظات لاحقاً
                            EvaluatorName = ie.Evaluator.FullName
                        }).ToList()
                    }).ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عرض تفاصيل درجات المرشح");
                TempData["Error"] = "حدث خطأ أثناء عرض تفاصيل درجات المرشح";
                return RedirectToAction("Index");
            }
        }

        // GET: CandidateGrades/Print/5
        public async Task<IActionResult> Print(int id)
        {
            try
            {
                var currentUser = await GetCurrentUser();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Account");
                }

                var canViewGrades = await CanUserViewGrades(currentUser);
                if (!canViewGrades)
                {
                    return RedirectToAction("AccessDenied", "Account");
                }

                var candidateEvaluation = await _context.CandidateEvaluations
                    .Include(ce => ce.Candidate)
                        .ThenInclude(c => c.Rank)
                    .Include(ce => ce.Candidate)
                        .ThenInclude(c => c.Category)
                    .Include(ce => ce.Committee)
                    .Include(ce => ce.EvaluationForm)
                    .Include(ce => ce.IndividualEvaluations)
                        .ThenInclude(ie => ie.Evaluator)
                            .ThenInclude(e => e.Rank)
                    .Include(ce => ce.IndividualEvaluations)
                        .ThenInclude(ie => ie.CriteriaScores)
                            .ThenInclude(iec => iec.EvaluationFormItem)
                    .FirstOrDefaultAsync(ce => ce.CandidateEvaluationId == id);

                if (candidateEvaluation == null)
                {
                    return NotFound();
                }

                // التحقق من صلاحية الوصول للجنة
                if (!currentUser.IsAdmin)
                {
                    var userCommittees = await GetUserCommittees(currentUser);
                    if (!userCommittees.Contains(candidateEvaluation.CommitteeId))
                    {
                        return RedirectToAction("AccessDenied", "Account");
                    }
                }

                var viewModel = new CandidateGradesViewModel
                {
                    CandidateEvaluationId = candidateEvaluation.CandidateEvaluationId,
                    CandidateName = candidateEvaluation.Candidate.FullName,
                    ServiceNumber = candidateEvaluation.Candidate.ServiceNumber,
                    RankName = candidateEvaluation.Candidate.Rank?.RankName ?? "",
                    CategoryName = candidateEvaluation.Candidate.Category?.CategoryName ?? "",
                    CommitteeName = candidateEvaluation.Committee.CommitteeName,
                    EvaluationFormTitle = candidateEvaluation.EvaluationForm.Title,
                    Status = candidateEvaluation.Status,
                    TotalScore = candidateEvaluation.TotalScore,
                    AverageScore = candidateEvaluation.AverageScore,
                    EvaluatorCount = candidateEvaluation.EvaluatorCount,
                    PresentEvaluatorCount = candidateEvaluation.PresentEvaluatorCount,
                    AbsentEvaluatorCount = candidateEvaluation.AbsentEvaluatorCount,
                    StartedAt = candidateEvaluation.StartedAt,
                    CompletedAt = candidateEvaluation.CompletedAt,
                    CreatedAt = candidateEvaluation.CreatedAt,
                    GeneralNotes = candidateEvaluation.GeneralNotes,
                    EvaluatorScores = candidateEvaluation.IndividualEvaluations.Select(ie => new EvaluatorScoreViewModel
                    {
                        IndividualEvaluationId = ie.IndividualEvaluationId,
                        EvaluatorName = ie.Evaluator.FullName,
                        EvaluatorRank = ie.Evaluator.Rank?.RankName ?? "",
                        EvaluatorServiceNumber = ie.Evaluator.ServiceNumber,
                        CommitteeRole = GetCommitteeRole(ie.Evaluator.ServiceNumber, candidateEvaluation.CommitteeId),
                        TotalScore = ie.TotalScore,
                        EvaluatorNotes = ie.EvaluatorNotes,
                        IsPresent = ie.IsPresent,
                        EvaluatedAt = ie.EvaluatedAt,
                        CriteriaScores = ie.CriteriaScores.Select(iec => new CriteriaScoreViewModel
                        {
                            CriteriaId = iec.EvaluationFormItemId,
                            CriteriaName = iec.EvaluationFormItem.Criteria,
                            CriteriaDescription = iec.EvaluationFormItem.Description ?? "",
                            MaxScore = iec.EvaluationFormItem.MaxScore,
                            Score = iec.Score,
                            Notes = null, // يمكن إضافة حقل للملاحظات لاحقاً
                            EvaluatorName = ie.Evaluator.FullName
                        }).ToList()
                    }).ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في طباعة درجات المرشح");
                TempData["Error"] = "حدث خطأ أثناء طباعة درجات المرشح";
                return RedirectToAction("Index");
            }
        }

        #region Helper Methods

        private async Task<User?> GetCurrentUser()
        {
            var serviceNumber = User.FindFirstValue(ClaimTypes.Name);
            if (string.IsNullOrEmpty(serviceNumber))
                return null;

            return await _context.Users
                .Include(u => u.Rank)
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.ServiceNumber == serviceNumber);
        }

        private async Task<bool> CanUserViewGrades(User user)
        {
            // المدير يمكنه رؤية جميع الدرجات
            if (user.IsAdmin)
                return true;

            // التحقق من دور المستخدم في اللجان
            var committeeRoles = await _context.CommitteeMembers
                .Where(cm => cm.ServiceNumber == user.ServiceNumber && cm.IsActive)
                .Select(cm => cm.Role)
                .ToListAsync();

            return committeeRoles.Any(r => r == CommitteeMemberRole.رئيس_اللجنة || r == CommitteeMemberRole.منسق);
        }

        private async Task<List<int>> GetUserCommittees(User user)
        {
            return await _context.CommitteeMembers
                .Where(cm => cm.ServiceNumber == user.ServiceNumber && cm.IsActive)
                .Select(cm => cm.CommitteeId)
                .ToListAsync();
        }

        private CommitteeMemberRole GetCommitteeRole(string serviceNumber, int committeeId)
        {
            var member = _context.CommitteeMembers
                .FirstOrDefault(cm => cm.ServiceNumber == serviceNumber && cm.CommitteeId == committeeId);
            
            return member?.Role ?? CommitteeMemberRole.عضو;
        }

        private string GetUserRole(User user)
        {
            if (user.IsAdmin)
                return "مدير";

            var committeeRole = _context.CommitteeMembers
                .Where(cm => cm.ServiceNumber == user.ServiceNumber && cm.IsActive)
                .OrderBy(cm => cm.Role)
                .Select(cm => cm.Role)
                .FirstOrDefault();

            return committeeRole switch
            {
                CommitteeMemberRole.رئيس_اللجنة => "رئيس لجنة",
                CommitteeMemberRole.منسق => "منسق لجنة",
                CommitteeMemberRole.عضو => "عضو لجنة",
                _ => "مستخدم"
            };
        }

        #endregion
    }
} 
@model RafoEvaluation.ViewModels.AirbaseViewModel
@{
    ViewData["Title"] = "Airbase Details";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Airbase Details
                    </h3>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.AirbaseId" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-list me-1"></i>Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Airbase ID:</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-primary">@Model.AirbaseId</span>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Airbase Name:</label>
                        <p class="form-control-plaintext">@Model.AirbaseName</p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Total Candidates:</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-info">@Model.TotalCandidates</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

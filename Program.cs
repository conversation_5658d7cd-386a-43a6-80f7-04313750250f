using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.Cookies;
using RafoEvaluation.Data;
using RafoEvaluation.Services;
using Serilog;

using Microsoft.AspNetCore.Server.IISIntegration;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

builder.Host.UseSerilog();

try
{
    Log.Information("Starting RafoEvaluation application");

    // Add services to the container.
    builder.Services.AddControllersWithViews();

    // Configure IIS Integration
    builder.Services.Configure<IISServerOptions>(options =>
    {
        options.MaxRequestBodySize = 30000000; // 30MB
        options.AllowSynchronousIO = false;
    });

    // Add Entity Framework
    builder.Services.AddDbContext<ApplicationDbContext>(options =>
    {
        options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
        
        // Enable detailed errors in development
        if (builder.Environment.IsDevelopment())
        {
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        }
    });

    // Add Authentication
 
    builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/Account/Login";
        options.LogoutPath = "/Account/Logout";
        options.AccessDeniedPath = "/Account/AccessDenied";
        options.ExpireTimeSpan = TimeSpan.FromDays(30);
        options.SlidingExpiration = true;
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = builder.Environment.IsDevelopment()
            ? CookieSecurePolicy.SameAsRequest
            : CookieSecurePolicy.Always;
           
    });

    // Add Authorization
    builder.Services.AddAuthorization();

    // Register services
    builder.Services.AddScoped<RafoEvaluation.Services.IAuthenticationService, RafoEvaluation.Services.AuthenticationService>();
    builder.Services.AddScoped<ICommitteeMemberOrderService, CommitteeMemberOrderService>();
    builder.Services.AddScoped<EvaluationCalculationService>();



    var app = builder.Build();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        // Development-specific middleware
        if (builder.Configuration.GetValue<bool>("Development:EnableDeveloperExceptionPage"))
        {
            app.UseDeveloperExceptionPage();
        }
        
        if (builder.Configuration.GetValue<bool>("Development:EnableDatabaseErrorPage"))
        {
            app.UseDeveloperExceptionPage();
        }
    
    }
    else
    {
        app.UseExceptionHandler("/Home/Error");
        app.UseHsts();
    }

    app.UseHttpsRedirection();
    app.UseStaticFiles();

    app.UseRouting();

    app.UseAuthentication();
    app.UseAuthorization();

    app.MapStaticAssets();

    app.MapControllerRoute(
        name: "default",
        pattern: "{controller=Home}/{action=Index}/{id?}")
        .WithStaticAssets();

   // Database migration and seeding
if (app.Environment.IsDevelopment() && builder.Configuration.GetValue<bool>("Development:SeedData"))
{
    using var scope = app.Services.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    
    // Use migrations instead of EnsureCreated
    await context.Database.MigrateAsync();
    
    // Add your seed data logic here if needed
    // await SeedDataAsync(context);
    
    Log.Information("Database migrations applied successfully");
}

    Log.Information("Application configured successfully");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

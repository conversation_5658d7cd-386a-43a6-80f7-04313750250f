using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.Models
{
    public class CommitteePath
    {
        [Key]
        public int CommitteePathId { get; set; }

        [Required(ErrorMessage = "اسم المسار مطلوب")]
        [Display(Name = "اسم المسار")]
        [StringLength(100, ErrorMessage = "اسم المسار لا يمكن أن يتجاوز 100 حرف")]
        public string PathName { get; set; } = string.Empty;

        [Display(Name = "وصف المسار")]
        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الفئة المستهدفة مطلوبة")]
        [Display(Name = "الفئة المستهدفة")]
        public int CategoryId { get; set; }

        [Display(Name = "اللجنة المسؤولة")]
        public int? CommitteeId { get; set; }

        [Display(Name = "المدة المتوقعة (بالأيام)")]
        [Range(1, 365, ErrorMessage = "المدة يجب أن تكون بين 1 و 365 يوم")]
        public int ExpectedDuration { get; set; } = 30;

        [Display(Name = "الحالة")]
        public PathStatus Status { get; set; } = PathStatus.Active;

        [Display(Name = "ترتيب المسار")]
        public int DisplayOrder { get; set; } = 1;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Category Category { get; set; } = null!;
        public virtual Committee? Committee { get; set; }
        public virtual ICollection<PathStage> Stages { get; set; } = new List<PathStage>();
    }

    public enum PathStatus
    {
        [Display(Name = "نشط")]
        Active = 1,
        
        [Display(Name = "غير نشط")]
        Inactive = 2,
        
        [Display(Name = "قيد التطوير")]
        UnderDevelopment = 3,
        
        [Display(Name = "معلق")]
        Suspended = 4
    }
} 
﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class CleanOldEvaluations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Clean old evaluation data for testing phase
            migrationBuilder.Sql("DELETE FROM IndividualEvaluationCriteria");
            migrationBuilder.Sql("DELETE FROM IndividualEvaluations");
            migrationBuilder.Sql("DELETE FROM CandidateEvaluations");
            
            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6233));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6747));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6749));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6750));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6750));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6751));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(883));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1353));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1355));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1356));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1357));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 6, 24, 257, DateTimeKind.Utc).AddTicks(1358));
        }
    }
}

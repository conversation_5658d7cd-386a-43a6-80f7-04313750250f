# 🧪 دليل اختبار أوزان الفئات

## ✅ ما تم تنفيذه:

### 1. **تحديث قاعدة البيانات**
- تم إضافة عمود `EvaluationWeight` إلى جدول `Categories`
- تم تحديث الأوزان حسب نوع الفئة:
  - **طيار/Pilot**: 40%
  - **مهندس/Engineer**: 35%
  - **فني/Technician**: 25%
  - **إداري/Administrative**: 20%
  - **طبيب/Doctor**: 45%
  - **ممرض/Nurse**: 30%
  - **محامي/Lawyer**: 35%
  - **محاسب/Accountant**: 25%
  - **مترجم/Translator**: 20%
  - **مدرب/Trainer**: 30%
  - **أخرى**: 30%

### 2. **صفحة إدارة الأوزان**
- رابط جديد: `/Category/ManageWeights`
- واجهة سهلة لتعديل الأوزان
- التحقق من صحة القيم (0-100%)
- عرض إحصائيات سريعة

### 3. **التحديث في التقارير**
- عمود "وزن الفئة" في تقارير المرشحين
- عمود "النتيجة الموزونة" (الدرجة × الوزن)
- تحديث تقارير اللجان

## 🔍 خطوات الاختبار:

### **المرحلة 1: التحقق من الأوزان الحالية**
1. اذهب إلى: `https://192.168.100.16:5001/Category`
2. تأكد من وجود عمود "الوزن" في الجدول
3. اضغط على "إدارة الأوزان"
4. تحقق من الأوزان المعينة لكل فئة

### **المرحلة 2: تعديل الأوزان**
1. في صفحة "إدارة الأوزان"
2. عدل وزن فئة "طيار" إلى 45%
3. عدل وزن فئة "مهندس" إلى 38%
4. احفظ التغييرات
5. تحقق من رسالة النجاح

### **المرحلة 3: اختبار التقارير**
1. اذهب إلى: `https://192.168.100.16:5001/Reports/CandidateGrades`
2. تأكد من وجود الأعمدة الجديدة:
   - "وزن الفئة"
   - "النتيجة الموزونة"
3. تحقق من صحة الحسابات

### **المرحلة 4: اختبار تقارير اللجان**
1. اذهب إلى: `https://192.168.100.16:5001/Reports/CommitteeReports`
2. اختر لجنة
3. تحقق من الأعمدة الجديدة في التقرير

## 📊 مثال على الحسابات:

**مرشح طيار:**
- الدرجة: 85%
- وزن الفئة: 40%
- النتيجة الموزونة: 85 × 0.40 = 34 نقطة

**مرشح مهندس:**
- الدرجة: 78%
- وزن الفئة: 35%
- النتيجة الموزونة: 78 × 0.35 = 27.3 نقطة

## ⚠️ ملاحظات مهمة:

1. **النظام الحالي لم يتأثر** - TotalScore و AverageScore تبقى كما هي
2. **الأوزان قابلة للتعديل** من صفحة إدارة الأوزان
3. **التحقق من صحة القيم** يتم تلقائياً
4. **التصدير يعمل** مع الأعمدة الجديدة

## 🚨 إذا واجهت مشاكل:

1. **تحقق من قاعدة البيانات**: تأكد من وجود عمود `EvaluationWeight`
2. **تحقق من الأخطاء**: افتح F12 في المتصفح
3. **تحقق من Logs**: راجع console التطبيق

---
**تم إنشاؤه بواسطة AI Assistant** 🤖
**التاريخ**: 21 يوليو 2025 
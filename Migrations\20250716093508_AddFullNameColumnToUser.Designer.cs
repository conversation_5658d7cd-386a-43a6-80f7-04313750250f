﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using RafoEvaluation.Data;

#nullable disable

namespace RafoEvaluation.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250716093508_AddFullNameColumnToUser")]
    partial class AddFullNameColumnToUser
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("RafoEvaluation.Models.Airbase", b =>
                {
                    b.Property<int>("AirbaseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AirbaseId"));

                    b.Property<string>("AirbaseName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("AirbaseId");

                    b.ToTable("Airbases");

                    b.HasData(
                        new
                        {
                            AirbaseId = 1,
                            AirbaseName = "قيادة سلاح الجو السلطاني العماني"
                        },
                        new
                        {
                            AirbaseId = 2,
                            AirbaseName = "قاعدة غلا وأكاديمية السلطان قابوس الجوية"
                        },
                        new
                        {
                            AirbaseId = 3,
                            AirbaseName = "قاعدة السيب الجوية"
                        },
                        new
                        {
                            AirbaseId = 4,
                            AirbaseName = "قاعدة صلالة الجوية"
                        },
                        new
                        {
                            AirbaseId = 5,
                            AirbaseName = "قاعدة المصنعة الجوية"
                        },
                        new
                        {
                            AirbaseId = 6,
                            AirbaseName = "قاعدة مصيرة الجوية"
                        },
                        new
                        {
                            AirbaseId = 7,
                            AirbaseName = "قاعدة أدم الجوية"
                        },
                        new
                        {
                            AirbaseId = 8,
                            AirbaseName = "قاعدة ثمريت الجوية"
                        },
                        new
                        {
                            AirbaseId = 9,
                            AirbaseName = "قاعدة خصب الجوية"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Rank", b =>
                {
                    b.Property<int>("RankId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RankId"));

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("RankName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("RankId");

                    b.HasIndex("RankName")
                        .IsUnique()
                        .HasDatabaseName("IX_Ranks_RankName");

                    b.ToTable("Ranks");

                    b.HasData(
                        new
                        {
                            RankId = 1,
                            DisplayOrder = 1,
                            RankName = "جندي"
                        },
                        new
                        {
                            RankId = 2,
                            DisplayOrder = 2,
                            RankName = "نائب عريف"
                        },
                        new
                        {
                            RankId = 3,
                            DisplayOrder = 3,
                            RankName = "عريف"
                        },
                        new
                        {
                            RankId = 4,
                            DisplayOrder = 4,
                            RankName = "رقيب"
                        },
                        new
                        {
                            RankId = 5,
                            DisplayOrder = 5,
                            RankName = "رقيب أول"
                        },
                        new
                        {
                            RankId = 6,
                            DisplayOrder = 6,
                            RankName = "وكيل"
                        },
                        new
                        {
                            RankId = 7,
                            DisplayOrder = 7,
                            RankName = "وكيل أول"
                        },
                        new
                        {
                            RankId = 8,
                            DisplayOrder = 8,
                            RankName = "ضابط مرشح"
                        },
                        new
                        {
                            RankId = 9,
                            DisplayOrder = 9,
                            RankName = "ملازم ثاني"
                        },
                        new
                        {
                            RankId = 10,
                            DisplayOrder = 10,
                            RankName = "ملازم أول"
                        },
                        new
                        {
                            RankId = 11,
                            DisplayOrder = 11,
                            RankName = "نقيب"
                        },
                        new
                        {
                            RankId = 12,
                            DisplayOrder = 12,
                            RankName = "رائد"
                        },
                        new
                        {
                            RankId = 13,
                            DisplayOrder = 13,
                            RankName = "مقدم"
                        },
                        new
                        {
                            RankId = 14,
                            DisplayOrder = 14,
                            RankName = "عقيد"
                        },
                        new
                        {
                            RankId = 15,
                            DisplayOrder = 15,
                            RankName = "عميد"
                        },
                        new
                        {
                            RankId = 16,
                            DisplayOrder = 16,
                            RankName = "لواء"
                        },
                        new
                        {
                            RankId = 17,
                            DisplayOrder = 17,
                            RankName = "فريق"
                        },
                        new
                        {
                            RankId = 18,
                            DisplayOrder = 18,
                            RankName = "مدني درجة 16"
                        },
                        new
                        {
                            RankId = 19,
                            DisplayOrder = 19,
                            RankName = "مدني درجة 15"
                        },
                        new
                        {
                            RankId = 20,
                            DisplayOrder = 20,
                            RankName = "مدني درجة 14"
                        },
                        new
                        {
                            RankId = 21,
                            DisplayOrder = 21,
                            RankName = "مدني درجة 13"
                        },
                        new
                        {
                            RankId = 22,
                            DisplayOrder = 22,
                            RankName = "مدني درجة 12"
                        },
                        new
                        {
                            RankId = 23,
                            DisplayOrder = 23,
                            RankName = "مدني درجة 11"
                        },
                        new
                        {
                            RankId = 24,
                            DisplayOrder = 24,
                            RankName = "مدني درجة 10"
                        },
                        new
                        {
                            RankId = 25,
                            DisplayOrder = 25,
                            RankName = "مدني درجة 9"
                        },
                        new
                        {
                            RankId = 26,
                            DisplayOrder = 26,
                            RankName = "ضابط مدني د8"
                        },
                        new
                        {
                            RankId = 27,
                            DisplayOrder = 27,
                            RankName = "ضابط مدني د9"
                        },
                        new
                        {
                            RankId = 28,
                            DisplayOrder = 28,
                            RankName = "ضابط مدني د7"
                        },
                        new
                        {
                            RankId = 29,
                            DisplayOrder = 29,
                            RankName = "ضابط مدني د6"
                        },
                        new
                        {
                            RankId = 30,
                            DisplayOrder = 30,
                            RankName = "ضابط مدني د5"
                        },
                        new
                        {
                            RankId = 31,
                            DisplayOrder = 31,
                            RankName = "ضابط مدني د4"
                        },
                        new
                        {
                            RankId = 32,
                            DisplayOrder = 32,
                            RankName = "ضابط مدني د3"
                        },
                        new
                        {
                            RankId = 33,
                            DisplayOrder = 33,
                            RankName = "ضابط مدني د2"
                        },
                        new
                        {
                            RankId = 34,
                            DisplayOrder = 34,
                            RankName = "ضابط مدني د1"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Role", b =>
                {
                    b.Property<int>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RoleId"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("RoleId");

                    b.HasIndex("RoleName")
                        .IsUnique()
                        .HasDatabaseName("IX_Roles_RoleName");

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            RoleId = 1,
                            CreatedAt = new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9213),
                            Description = "System Administrator with full access",
                            RoleName = "Admin"
                        },
                        new
                        {
                            RoleId = 2,
                            CreatedAt = new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9963),
                            Description = "Evaluation Coordinator",
                            RoleName = "Coordinator"
                        },
                        new
                        {
                            RoleId = 3,
                            CreatedAt = new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9965),
                            Description = "Personnel Evaluator",
                            RoleName = "Evaluator"
                        },
                        new
                        {
                            RoleId = 4,
                            CreatedAt = new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9967),
                            Description = "Read-only access to evaluations",
                            RoleName = "Viewer"
                        },
                        new
                        {
                            RoleId = 5,
                            CreatedAt = new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9967),
                            Description = "Human Resources Manager",
                            RoleName = "HR Manager"
                        },
                        new
                        {
                            RoleId = 6,
                            CreatedAt = new DateTime(2025, 7, 16, 9, 35, 8, 142, DateTimeKind.Utc).AddTicks(9969),
                            Description = "Training and Development Officer",
                            RoleName = "Training Officer"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserId"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("RankId")
                        .HasColumnType("int");

                    b.Property<string>("ServiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("UserId");

                    b.HasIndex("RankId");

                    b.HasIndex("ServiceNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_ServiceNumber");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            UserId = 1,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            FullName = "",
                            IsActive = true,
                            Password = "$2a$11$N9qo8uLOickgx2ZMRZoMye.IjdQXvbVxVv0v6XVHbOxLuvh.LbHSi",
                            RankId = 3,
                            ServiceNumber = "RAFO001"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.UserRole", b =>
                {
                    b.Property<int>("UserRoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserRoleId"));

                    b.Property<DateTime>("AssignedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("UserRoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserRoles_UserId_RoleId");

                    b.ToTable("UserRoles");

                    b.HasData(
                        new
                        {
                            UserRoleId = 1,
                            AssignedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            RoleId = 1,
                            UserId = 1
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Candidate", b =>
                {
                    b.Property<int>("CandidateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CandidateId"));

                    b.Property<int?>("AirbaseId")
                        .HasColumnType("int");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("Department")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("GraduationYear")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("JobTitle")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Major")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MarksGrade")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("NationalIdNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("RankId")
                        .HasColumnType("int");

                    b.Property<string>("ServiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("University")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("CandidateId");

                    b.HasIndex("AirbaseId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("FullName")
                        .HasDatabaseName("IX_Candidates_FullName");

                    b.HasIndex("NationalIdNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Candidates_NationalIdNumber")
                        .HasFilter("[NationalIdNumber] IS NOT NULL AND [NationalIdNumber] != ''");

                    b.HasIndex("RankId");

                    b.HasIndex("ServiceNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Candidates_ServiceNumber")
                        .HasFilter("[ServiceNumber] IS NOT NULL AND [ServiceNumber] != ''");

                    b.ToTable("Candidates");
                });

            modelBuilder.Entity("RafoEvaluation.Models.CandidateEvaluation", b =>
                {
                    b.Property<int>("CandidateEvaluationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CandidateEvaluationId"));

                    b.Property<int>("CandidateId")
                        .HasColumnType("int");

                    b.Property<int>("CommitteeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<int>("EvaluationFormId")
                        .HasColumnType("int");

                    b.Property<int>("EvaluatorCount")
                        .HasColumnType("int");

                    b.Property<string>("GeneralNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<decimal?>("TotalScore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("CandidateEvaluationId");

                    b.HasIndex("CommitteeId")
                        .HasDatabaseName("IX_CandidateEvaluations_CommitteeId");

                    b.HasIndex("EvaluationFormId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CandidateEvaluations_Status");

                    b.HasIndex("CandidateId", "EvaluationFormId", "CommitteeId")
                        .IsUnique()
                        .HasDatabaseName("IX_CandidateEvaluations_CandidateId_FormId_CommitteeId");

                    b.ToTable("CandidateEvaluations");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Category", b =>
                {
                    b.Property<int>("CategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CategoryId"));

                    b.Property<string>("CategoryCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("CategoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("CategoryId");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            CategoryId = 1,
                            CategoryCode = "CAT-PLT",
                            CategoryName = "المرشحيين الطيارين",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "فئة المرشحين للالتحاق بسلاح الجو السلطاني العماني كمرشحين طيارين.",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 2,
                            CategoryCode = "CAT-MUG",
                            CategoryName = "المرشحيين الجامعيين العسكريين",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط عسكريين جامعيين",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 3,
                            CategoryCode = "CAT-CUG",
                            CategoryName = "المرشحيين الجامعيين المدنيين",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط مدنيين جامعيين",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 4,
                            CategoryCode = "CAT-LSO",
                            CategoryName = "ضباط الخدمة المحدودة",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط الصف من ذوي الخدمة المحدودة وسسبق لهم العمل في السلاح وهم برتبة وكيل فأعلى",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 5,
                            CategoryCode = "CAT-NCO",
                            CategoryName = "ضباط الصف ( رقباء / عرفاء)",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط صف برتبة رقيب أو عريف سبق لهم العمل بالسلاح",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 6,
                            CategoryCode = "CAT-TCN",
                            CategoryName = "ضباط الصف الكلية التقنية العسكرية",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط صف خريجوا الكلية العسكرية التقنية",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 7,
                            CategoryCode = "CAT-CNP",
                            CategoryName = "ضباط الصف المدنيين للترفيع",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط صف مدنيين مرشحين للترقية بالصفة المدنية",
                            IsActive = true
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Committee", b =>
                {
                    b.Property<int>("CommitteeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CommitteeId"));

                    b.Property<string>("CommitteeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("CommitteeId");

                    b.HasIndex("CommitteeName")
                        .HasDatabaseName("IX_Committees_CommitteeName");

                    b.ToTable("Committees");

                    b.HasData(
                        new
                        {
                            CommitteeId = 1,
                            CommitteeName = "لجنة تقييم الطيارين",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "تقييم المرشحين لمناصب الطيران",
                            Status = 1
                        },
                        new
                        {
                            CommitteeId = 2,
                            CommitteeName = "لجنة المقابلات الشخصية",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "إجراء المقابلات للمرشحين",
                            Status = 1
                        },
                        new
                        {
                            CommitteeId = 3,
                            CommitteeName = "لجنة الفحص الطبي",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "فحص اللياقة الطبية للمرشحين",
                            Status = 1
                        },
                        new
                        {
                            CommitteeId = 4,
                            CommitteeName = "لجنة التقييم التقني",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "تقييم المهارات التقنية والهندسية",
                            Status = 1
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.CommitteeMember", b =>
                {
                    b.Property<int>("CommitteeMemberId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CommitteeMemberId"));

                    b.Property<int>("CommitteeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("Role")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(3);

                    b.Property<string>("ServiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("CommitteeMemberId");

                    b.HasIndex("CommitteeId")
                        .HasDatabaseName("IX_CommitteeMembers_CommitteeId");

                    b.HasIndex("ServiceNumber")
                        .HasDatabaseName("IX_CommitteeMembers_ServiceNumber");

                    b.ToTable("CommitteeMembers");
                });

            modelBuilder.Entity("RafoEvaluation.Models.EvaluationCriteria", b =>
                {
                    b.Property<int>("CriteriaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CriteriaId"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("CriteriaName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("MaxScore")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("CriteriaId");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_EvaluationCriteria_DisplayOrder");

                    b.ToTable("EvaluationCriteria");

                    b.HasData(
                        new
                        {
                            CriteriaId = 1,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CriteriaName = "المظهر العام واللياقة البدنية",
                            Description = "تقييم المظهر العام واللياقة البدنية للمرشح",
                            DisplayOrder = 1,
                            IsActive = true,
                            MaxScore = 10
                        },
                        new
                        {
                            CriteriaId = 2,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CriteriaName = "الثقافة العامة",
                            Description = "تقييم مستوى الثقافة العامة والمعرفة",
                            DisplayOrder = 2,
                            IsActive = true,
                            MaxScore = 15
                        },
                        new
                        {
                            CriteriaId = 3,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CriteriaName = "المهارات اللغوية",
                            Description = "تقييم مهارات اللغة العربية والإنجليزية",
                            DisplayOrder = 3,
                            IsActive = true,
                            MaxScore = 15
                        },
                        new
                        {
                            CriteriaId = 4,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CriteriaName = "المهارات التقنية",
                            Description = "تقييم المهارات التقنية والمعلوماتية",
                            DisplayOrder = 4,
                            IsActive = true,
                            MaxScore = 20
                        },
                        new
                        {
                            CriteriaId = 5,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CriteriaName = "الشخصية والقيادة",
                            Description = "تقييم الشخصية ومهارات القيادة",
                            DisplayOrder = 5,
                            IsActive = true,
                            MaxScore = 20
                        },
                        new
                        {
                            CriteriaId = 6,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CriteriaName = "الدوافع والطموح",
                            Description = "تقييم دوافع المرشح وطموحاته المهنية",
                            DisplayOrder = 6,
                            IsActive = true,
                            MaxScore = 10
                        },
                        new
                        {
                            CriteriaId = 7,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CriteriaName = "الخبرة العملية",
                            Description = "تقييم الخبرة العملية السابقة",
                            DisplayOrder = 7,
                            IsActive = true,
                            MaxScore = 10
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.EvaluationForm", b =>
                {
                    b.Property<int>("EvaluationFormId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EvaluationFormId"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("EvaluationFormId");

                    b.HasIndex("Title")
                        .HasDatabaseName("IX_EvaluationForms_Title");

                    b.ToTable("EvaluationForms");
                });

            modelBuilder.Entity("RafoEvaluation.Models.EvaluationFormItem", b =>
                {
                    b.Property<int>("EvaluationFormItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EvaluationFormItemId"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Criteria")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<int>("EvaluationFormId")
                        .HasColumnType("int");

                    b.Property<int>("MaxScore")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(10);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Weight")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(3,1)")
                        .HasDefaultValue(1.0m);

                    b.HasKey("EvaluationFormItemId");

                    b.HasIndex("EvaluationFormId")
                        .HasDatabaseName("IX_EvaluationFormItems_FormId");

                    b.ToTable("EvaluationFormItems");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.User", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Auth.Rank", "Rank")
                        .WithMany("Users")
                        .HasForeignKey("RankId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Rank");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.UserRole", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Auth.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.Auth.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Candidate", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Airbase", "Airbase")
                        .WithMany("Candidates")
                        .HasForeignKey("AirbaseId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("RafoEvaluation.Models.Category", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.Auth.Rank", "Rank")
                        .WithMany()
                        .HasForeignKey("RankId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Airbase");

                    b.Navigation("Category");

                    b.Navigation("Rank");
                });

            modelBuilder.Entity("RafoEvaluation.Models.CandidateEvaluation", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Candidate", "Candidate")
                        .WithMany()
                        .HasForeignKey("CandidateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.Committee", "Committee")
                        .WithMany()
                        .HasForeignKey("CommitteeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.EvaluationForm", "EvaluationForm")
                        .WithMany("CandidateEvaluations")
                        .HasForeignKey("EvaluationFormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Candidate");

                    b.Navigation("Committee");

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("RafoEvaluation.Models.CommitteeMember", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Committee", "Committee")
                        .WithMany("CommitteeMembers")
                        .HasForeignKey("CommitteeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.Auth.User", "User")
                        .WithMany()
                        .HasForeignKey("ServiceNumber")
                        .HasPrincipalKey("ServiceNumber")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Committee");

                    b.Navigation("User");
                });

            modelBuilder.Entity("RafoEvaluation.Models.EvaluationFormItem", b =>
                {
                    b.HasOne("RafoEvaluation.Models.EvaluationForm", "EvaluationForm")
                        .WithMany("Items")
                        .HasForeignKey("EvaluationFormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Airbase", b =>
                {
                    b.Navigation("Candidates");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Rank", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Role", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.User", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Committee", b =>
                {
                    b.Navigation("CommitteeMembers");
                });

            modelBuilder.Entity("RafoEvaluation.Models.EvaluationForm", b =>
                {
                    b.Navigation("CandidateEvaluations");

                    b.Navigation("Items");
                });
#pragma warning restore 612, 618
        }
    }
}

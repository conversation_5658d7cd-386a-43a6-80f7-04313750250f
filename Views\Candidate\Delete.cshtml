@model RafoEvaluation.ViewModels.CandidateViewModel
@{
    ViewData["Title"] = "حذف المرشح";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-users breadcrumb-icon"></i>
                    حذف المرشح
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Candidate">
                                <i class="fas fa-users"></i> المرشحين
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-trash"></i> حذف
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>


<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-trash me-2"></i>حذف المرشح
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه!</strong> أنت على وشك حذف هذا المرشح بشكل نهائي. لا يمكن التراجع عن هذا الإجراء.
                    </div>

                    <h5 class="mb-3">هل أنت متأكد أنك تريد حذف هذا المرشح؟</h5>
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الاسم الكامل:</label>
                                <p class="form-control-plaintext">@Model.FullName</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">رقم الخدمة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-secondary">@Model.ServiceNumber</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">رقم الهوية الوطنية:</label>
                                <p class="form-control-plaintext">@Model.NationalIdNumber</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">العمر:</label>
                                <p class="form-control-plaintext">@Model.Age سنة</p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الفئة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info">@Model.CategoryName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">الرتبة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-warning text-dark">@Model.RankName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">القسم:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.Department))
                                    {
                                        @Model.Department
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غير نشط</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Education Information (if available) -->
                    @if (!string.IsNullOrEmpty(Model.University) || !string.IsNullOrEmpty(Model.Major) || Model.GraduationYear.HasValue)
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6 class="text-primary">معلومات تعليمية:</h6>
                            </div>
                            
                            @if (!string.IsNullOrEmpty(Model.University))
                            {
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الجامعة:</label>
                                        <p class="form-control-plaintext">@Model.University</p>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(Model.Major))
                            {
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">التخصص:</label>
                                        <p class="form-control-plaintext">@Model.Major</p>
                                    </div>
                                </div>
                            }

                            @if (Model.GraduationYear.HasValue)
                            {
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">سنة التخرج:</label>
                                        <p class="form-control-plaintext">@Model.GraduationYear.Value</p>
                                    </div>
                                </div>
                            }
                        </div>
                    }

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end">
                                <a asp-action="Index" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </a>
                                <form asp-action="Delete" method="post" class="d-inline" id="deleteForm">
                                    <input type="hidden" asp-for="CandidateId" />
                                    <button type="submit" class="btn btn-danger" id="deleteButton">
                                        <i class="fas fa-trash me-1"></i>حذف نهائي
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#deleteButton').click(function(e) {
                e.preventDefault();
                
                if (confirm('هل أنت متأكد أنك تريد حذف هذا المرشح نهائياً؟ لا يمكن التراجع عن هذا الإجراء.')) {
                    $('#deleteForm').submit();
                }
            });
        });
    </script>
}

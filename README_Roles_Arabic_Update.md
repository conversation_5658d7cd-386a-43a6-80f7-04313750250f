# تحديث الصلاحيات للعربية وحذف الصلاحيات القديمة

## نظرة عامة
تم تحديث أسماء الصلاحيات في النظام لتكون باللغة العربية بدلاً من الإنجليزية، مع حذف الصلاحيات القديمة غير المستخدمة من قاعدة البيانات.

## الصلاحيات الجديدة باللغة العربية

### قبل التحديث (الإنجليزية):
1. **Admin** - مدير النظام مع صلاحيات كاملة
2. **Coordinator** - منسق التقييم يدير عملية التقييم  
3. **Evaluator** - مقيم يقوم بتقييم المرشحين
4. **Admin Cell** - خلية إدارية تدير البيانات الأساسية والمرشحين والمقيمين

### بعد التحديث (العربية):
1. **مدير** - مدير النظام مع صلاحيات كاملة
2. **منسق** - منسق التقييم يدير عملية التقييم
3. **مقيم** - مقيم يقوم بتقييم المرشحين
4. **خلية إدارية** - خلية إدارية تدير البيانات الأساسية والمرشحين والمقيمين

## الصلاحيات المحذوفة

### الصلاحيات القديمة التي تم حذفها نهائياً:
- **HR Manager** (مدير الموارد البشرية)
- **Training Officer** (ضابط التدريب)
- **Viewer** (مشاهد)
- أي صلاحيات أخرى غير مستخدمة

## الملفات المعدلة

### 1. `Data/ApplicationDbContext.cs`
```csharp
// قبل التحديث
new Role { RoleId = 1, RoleName = "Admin", Description = "مدير النظام مع صلاحيات كاملة" },
new Role { RoleId = 2, RoleName = "Coordinator", Description = "منسق التقييم يدير عملية التقييم" },
new Role { RoleId = 3, RoleName = "Evaluator", Description = "مقيم يقوم بتقييم المرشحين" },
new Role { RoleId = 4, RoleName = "Admin Cell", Description = "خلية إدارية تدير البيانات الأساسية والمرشحين والمقيمين" }

// بعد التحديث
new Role { RoleId = 1, RoleName = "مدير", Description = "مدير النظام مع صلاحيات كاملة" },
new Role { RoleId = 2, RoleName = "منسق", Description = "منسق التقييم يدير عملية التقييم" },
new Role { RoleId = 3, RoleName = "مقيم", Description = "مقيم يقوم بتقييم المرشحين" },
new Role { RoleId = 4, RoleName = "خلية إدارية", Description = "خلية إدارية تدير البيانات الأساسية والمرشحين والمقيمين" }
```

### 2. `Migrations/20250101000001_UpdateRolesToArabicAndCleanup.cs`
Migration جديد يقوم بـ:
- حذف الصلاحيات القديمة من قاعدة البيانات
- تحديث أسماء الصلاحيات للعربية
- تنظيف البيانات غير المستخدمة

## Migration Details

### Up() Method:
```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    // حذف الصلاحيات القديمة
    migrationBuilder.Sql("DELETE FROM UserRoles WHERE RoleId IN (5, 6, 7, 8, 9, 10)");
    migrationBuilder.Sql("DELETE FROM Roles WHERE RoleId IN (5, 6, 7, 8, 9, 10)");

    // تحديث الصلاحيات للعربية
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 1, 
        columns: new[] { "RoleName" }, values: new object[] { "مدير" });
    
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 2, 
        columns: new[] { "RoleName" }, values: new object[] { "منسق" });
    
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 3, 
        columns: new[] { "RoleName" }, values: new object[] { "مقيم" });
    
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 4, 
        columns: new[] { "RoleName" }, values: new object[] { "خلية إدارية" });
}
```

### Down() Method:
```csharp
protected override void Down(MigrationBuilder migrationBuilder)
{
    // استعادة الأسماء الإنجليزية
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 1, 
        columns: new[] { "RoleName" }, values: new object[] { "Admin" });
    
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 2, 
        columns: new[] { "RoleName" }, values: new object[] { "Coordinator" });
    
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 3, 
        columns: new[] { "RoleName" }, values: new object[] { "Evaluator" });
    
    migrationBuilder.UpdateData(table: "Roles", keyColumn: "RoleId", keyValue: 4, 
        columns: new[] { "RoleName" }, values: new object[] { "Admin Cell" });
}
```

## تأثير التحديث على النظام

### 1. واجهة المستخدم
- ستظهر أسماء الصلاحيات باللغة العربية في جميع أنحاء النظام
- رسائل الخطأ والنجاح ستظهر بالعربية
- القوائم المنسدلة ستظهر الصلاحيات بالعربية

### 2. قاعدة البيانات
- تم حذف الصلاحيات القديمة نهائياً
- تم تحديث أسماء الصلاحيات الموجودة
- تم تنظيف البيانات المرتبطة بالصلاحيات المحذوفة

### 3. الكود
- يجب تحديث أي مراجع للصلاحيات في الكود
- يجب استخدام الأسماء العربية الجديدة في فحوصات الصلاحيات

## تحديثات مطلوبة في الكود

### في Controllers:
```csharp
// قبل التحديث
if (User.IsInRole("Admin") || User.IsInRole("Coordinator"))

// بعد التحديث
if (User.IsInRole("مدير") || User.IsInRole("منسق"))
```

### في Views:
```html
<!-- قبل التحديث -->
@if (User.IsInRole("Admin") || User.IsInRole("Coordinator"))

<!-- بعد التحديث -->
@if (User.IsInRole("مدير") || User.IsInRole("منسق"))
```

## الفوائد من التحديث

### 1. وضوح أفضل
- أسماء الصلاحيات واضحة ومفهومة للمستخدمين العرب
- تقليل الالتباس بين الصلاحيات المختلفة

### 2. تنظيف النظام
- إزالة الصلاحيات غير المستخدمة
- تقليل تعقيد النظام
- تحسين الأداء

### 3. سهولة الصيانة
- نظام صلاحيات مبسط وواضح
- سهولة إضافة صلاحيات جديدة في المستقبل

## خطوات التطبيق

### 1. تطبيق Migration
```bash
dotnet ef database update
```

### 2. تحديث الكود
- تحديث جميع مراجع الصلاحيات في Controllers
- تحديث جميع مراجع الصلاحيات في Views
- اختبار النظام للتأكد من عدم وجود أخطاء

### 3. اختبار النظام
- اختبار تسجيل الدخول بجميع الصلاحيات
- اختبار الوصول للصفحات المختلفة
- اختبار العمليات المختلفة

## ملاحظات مهمة

### 1. النسخ الاحتياطية
- تم إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث
- يمكن الرجوع للنسخة السابقة إذا لزم الأمر

### 2. التوافق
- النظام متوافق مع الإصدارات السابقة
- لا توجد تغييرات في هيكل قاعدة البيانات

### 3. الأمان
- تم الحفاظ على جميع إعدادات الأمان
- لم يتم تغيير آلية التحقق من الصلاحيات

## اختبار التحديث

### اختبار الصلاحيات الجديدة:
1. **مدير**: اختبار جميع الصلاحيات
2. **منسق**: اختبار صلاحيات التنسيق
3. **مقيم**: اختبار صلاحيات التقييم
4. **خلية إدارية**: اختبار صلاحيات الإدارة

### اختبار الحذف:
- التأكد من عدم وجود مراجع للصلاحيات المحذوفة
- التأكد من عدم وجود أخطاء في النظام

## النتائج المتوقعة

### بعد التحديث:
- ✅ أسماء الصلاحيات باللغة العربية
- ✅ حذف الصلاحيات غير المستخدمة
- ✅ نظام أكثر وضوحاً وبساطة
- ✅ أداء محسن
- ✅ سهولة الصيانة

### تحسينات إضافية:
- 🌐 واجهة مستخدم أكثر وضوحاً
- 🧹 نظام نظيف ومنظم
- ⚡ أداء محسن
- 🔧 سهولة الصيانة والتطوير 
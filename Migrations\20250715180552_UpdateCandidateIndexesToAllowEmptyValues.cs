﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class UpdateCandidateIndexesToAllowEmptyValues : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Candidates_NationalIdNumber",
                table: "Candidates");

            migrationBuilder.DropIndex(
                name: "IX_Candidates_ServiceNumber",
                table: "Candidates");

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(2457));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3412));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3415));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3417));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3432));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3433));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3434));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3436));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3437));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3438));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3439));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3440));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3441));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3442));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3443));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 176, DateTimeKind.Utc).AddTicks(3444));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 177, DateTimeKind.Utc).AddTicks(517));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 177, DateTimeKind.Utc).AddTicks(1154));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 177, DateTimeKind.Utc).AddTicks(1157));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 177, DateTimeKind.Utc).AddTicks(1158));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 177, DateTimeKind.Utc).AddTicks(1159));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 5, 52, 177, DateTimeKind.Utc).AddTicks(1160));

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_NationalIdNumber",
                table: "Candidates",
                column: "NationalIdNumber",
                unique: true,
                filter: "[NationalIdNumber] IS NOT NULL AND [NationalIdNumber] != ''");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_ServiceNumber",
                table: "Candidates",
                column: "ServiceNumber",
                unique: true,
                filter: "[ServiceNumber] IS NOT NULL AND [ServiceNumber] != ''");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Candidates_NationalIdNumber",
                table: "Candidates");

            migrationBuilder.DropIndex(
                name: "IX_Candidates_ServiceNumber",
                table: "Candidates");

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(3734));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4607));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4609));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4610));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4611));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4612));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4613));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4628));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4629));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4630));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4632));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4633));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4634));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4635));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4636));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 719, DateTimeKind.Utc).AddTicks(4637));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1029));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1635));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1637));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1638));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1639));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 1, 9, 720, DateTimeKind.Utc).AddTicks(1640));

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_NationalIdNumber",
                table: "Candidates",
                column: "NationalIdNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_ServiceNumber",
                table: "Candidates",
                column: "ServiceNumber",
                unique: true);
        }
    }
}

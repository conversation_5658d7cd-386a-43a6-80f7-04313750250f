# تحديث صلاحيات التقييم لرئيس اللجنة وأعضاء اللجنة

## نظرة عامة

تم تحديث نظام صلاحيات التقييم لضمان أن رئيس اللجنة وأعضاء اللجنة يمكنهم التقييم افتراضياً حتى لو لم يتم تحديد `CanEvaluate` بشكل صريح.

## المشكلة السابقة

كان النظام يتحقق من حقل `CanEvaluate` مباشرة، مما يعني أن:
- رئيس اللجنة وأعضاء اللجنة لا يمكنهم التقييم إذا لم يتم تحديد `CanEvaluate = true`
- المنسقون فقط هم من يمكنهم التقييم إذا كان `CanEvaluate = true`

## الحل الجديد

تم إنشاء منطق جديد للصلاحيات يعتمد على دور العضو في اللجنة:

### 1. رئيس اللجنة (رئيس_اللجنة)
- **يمكنه التقييم افتراضياً** بغض النظر عن قيمة `CanEvaluate`
- لا يمكن إلغاء صلاحية التقييم لرئيس اللجنة

### 2. أعضاء اللجنة (عضو)
- **يمكنهم التقييم افتراضياً** بغض النظر عن قيمة `CanEvaluate`
- لا يمكن إلغاء صلاحية التقييم لأعضاء اللجنة

### 3. المنسقون (منسق)
- **يمكنهم التقييم فقط إذا كان `CanEvaluate = true`**
- يمكن إلغاء صلاحية التقييم للمنسقين

## التحديثات التقنية

### 1. AuthenticationService

#### دالة جديدة: `CanUserEvaluateInCommitteeAsync`
```csharp
public async Task<bool> CanUserEvaluateInCommitteeAsync(string serviceNumber, int committeeId)
{
    var member = await _context.CommitteeMembers
        .FirstOrDefaultAsync(cm => cm.ServiceNumber == serviceNumber && 
                                 cm.CommitteeId == committeeId && 
                                 cm.IsActive);
    
    if (member == null)
        return false;

    // رئيس اللجنة وأعضاء اللجنة يمكنهم التقييم افتراضياً
    if (member.Role == CommitteeMemberRole.رئيس_اللجنة || member.Role == CommitteeMemberRole.عضو)
        return true;

    // المنسقون يمكنهم التقييم فقط إذا كان CanEvaluate = true
    if (member.Role == CommitteeMemberRole.منسق)
        return member.CanEvaluate;

    return false;
}
```

### 2. CommitteeMemberOrderService

#### دالة جديدة: `CanMemberEvaluate(CommitteeMember member)`
```csharp
public async Task<bool> CanMemberEvaluate(CommitteeMember member)
{
    if (member == null || !member.IsActive)
        return false;

    // رئيس اللجنة وأعضاء اللجنة يمكنهم التقييم افتراضياً
    if (member.Role == CommitteeMemberRole.رئيس_اللجنة || member.Role == CommitteeMemberRole.عضو)
        return true;

    // المنسقون يمكنهم التقييم فقط إذا كان CanEvaluate = true
    if (member.Role == CommitteeMemberRole.منسق)
        return member.CanEvaluate;

    return false;
}
```

#### تحديث دالة `GetEvaluators`
```csharp
public async Task<List<CommitteeMember>> GetEvaluators(int committeeId)
{
    var members = await _context.CommitteeMembers
        .Include(cm => cm.User)
        .ThenInclude(u => u.Rank)
        .Where(cm => cm.CommitteeId == committeeId && cm.IsActive)
        .OrderBy(cm => cm.MemberNumber)
        .ToListAsync();

    // تصفية الأعضاء بناءً على صلاحيات التقييم
    var evaluators = new List<CommitteeMember>();
    foreach (var member in members)
    {
        if (await CanMemberEvaluate(member))
        {
            evaluators.Add(member);
        }
    }

    return evaluators;
}
```

### 3. تحديث الكنترولرز

#### IndividualEvaluationController
- تم تحديث التحقق من الصلاحيات في دالة `Create` لاستخدام `CanUserEvaluateInCommitteeAsync`

#### ReportsController
- تم تحديث عرض أعضاء اللجنة لاستخدام `GetEvaluators`
- تم تحديث حساب الإحصائيات لاستخدام `GetEvaluatorCount`

#### CommitteeMemberController
- تم تحديث عرض قائمة الأعضاء لاستخدام `CanMemberEvaluate`
- تم تحديث حساب الإحصائيات لاستخدام `GetEvaluatorCount`

## الفوائد

### 1. مرونة أكبر
- رئيس اللجنة وأعضاء اللجنة يمكنهم التقييم تلقائياً
- لا حاجة لتحديد `CanEvaluate` لكل عضو

### 2. منطق واضح
- الصلاحيات تعتمد على الدور وليس على حقل منفصل
- سهولة الفهم والصيانة

### 3. توافق مع المتطلبات
- رئيس اللجنة وأعضاء اللجنة يمكنهم التقييم افتراضياً
- المنسقون يمكنهم التقييم فقط إذا تم تحديد ذلك

## الاختبار

### سيناريوهات الاختبار

#### 1. رئيس اللجنة
- [ ] يمكنه التقييم حتى لو كان `CanEvaluate = false`
- [ ] يظهر في قائمة المقيمين
- [ ] يمكنه الوصول لصفحات التقييم

#### 2. أعضاء اللجنة
- [ ] يمكنهم التقييم حتى لو كان `CanEvaluate = false`
- [ ] يظهرون في قائمة المقيمين
- [ ] يمكنهم الوصول لصفحات التقييم

#### 3. المنسقون
- [ ] يمكنهم التقييم فقط إذا كان `CanEvaluate = true`
- [ ] لا يظهرون في قائمة المقيمين إذا كان `CanEvaluate = false`
- [ ] لا يمكنهم الوصول لصفحات التقييم إذا كان `CanEvaluate = false`

## التطوير المستقبلي

### ميزات مقترحة
- **إعدادات إضافية** للصلاحيات حسب نوع التقييم
- **صلاحيات مؤقتة** لفترات محددة
- **تتبع التغييرات** في الصلاحيات

### تحسينات تقنية
- **تخزين مؤقت** لنتائج التحقق من الصلاحيات
- **أداء محسن** للاستعلامات المتكررة
- **واجهة API** للتحقق من الصلاحيات

## الخلاصة

تم حل مشكلة صلاحيات التقييم بنجاح، حيث أصبح رئيس اللجنة وأعضاء اللجنة قادرين على التقييم افتراضياً. النظام الآن أكثر مرونة ووضوحاً، ويتوافق مع المتطلبات المطلوبة. 
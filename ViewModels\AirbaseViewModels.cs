using RafoEvaluation.Models;
using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    public class AirbaseViewModel
    {
        public int AirbaseId { get; set; }

        [Required]
        [Display(Name = "Airbase Name")]
        public string AirbaseName { get; set; } = string.Empty;

        // Additional properties for display
        public int TotalCandidates { get; set; }
    }

    public class AirbaseCreateViewModel
    {
        [Required]
        [Display(Name = "Airbase Name")]
        public string AirbaseName { get; set; } = string.Empty;
    }

    public class AirbaseEditViewModel
    {
        public int AirbaseId { get; set; }

        [Required]
        [Display(Name = "Airbase Name")]
        public string AirbaseName { get; set; } = string.Empty;
    }

    public class AirbaseListViewModel
    {
        public List<AirbaseViewModel> Airbases { get; set; } = new();
        public string SearchTerm { get; set; } = string.Empty;
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 10;
    }
}

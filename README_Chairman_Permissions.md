# صلاحيات رئيس اللجنة في نظام التقييم البسيط

## نظرة عامة

تم إضافة صلاحيات خاصة لرئيس اللجنة في نظام التقييم البسيط، مما يتيح له مراقبة حالة تقييم جميع أعضاء اللجنة وعرض إحصائيات مفصلة.

## الميزات الجديدة

### 1. عرض حالة تقييم أعضاء اللجنة

رئيس اللجنة يمكنه رؤية:
- **قائمة جميع أعضاء اللجنة** مع معلوماتهم الأساسية
- **عدد المرشحين المخصصين** لكل عضو
- **عدد المرشحين الذين تم تقييمهم** من قبل كل عضو
- **عدد المرشحين في الانتظار** لكل عضو
- **نسبة الإنجاز** لكل عضو (مع شريط تقدم بصري)
- **تاريخ آخر تقييم** لكل عضو

### 2. إشارات بصرية لرئيس اللجنة

- **شارة خاصة** في رأس الصفحة تشير إلى أن المستخدم هو رئيس لجنة
- **لون مميز** لقسم حالة أعضاء اللجنة (أصفر)
- **أيقونة تاج** للإشارة إلى دور رئيس اللجنة

### 3. ترتيب وتصفية البيانات

- **ترتيب تلقائي** حسب نسبة الإنجاز (تنازلياً)
- **بحث وتصفية** في جدول أعضاء اللجنة
- **ترقيم صفحات** للتنقل بين النتائج

## التنفيذ التقني

### 1. النماذج (ViewModels)

#### SimpleEvaluationListViewModel
```csharp
public class SimpleEvaluationListViewModel
{
    // ... الخصائص الموجودة
    
    // صلاحيات رئيس اللجنة
    public bool IsCommitteeChair { get; set; } = false;
    public List<CommitteeMemberEvaluationStatusViewModel> CommitteeMembersStatus { get; set; } = new();
}
```

#### CommitteeMemberEvaluationStatusViewModel
```csharp
public class CommitteeMemberEvaluationStatusViewModel
{
    public string ServiceNumber { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string RankName { get; set; } = string.Empty;
    public CommitteeMemberRole Role { get; set; }
    public int TotalCandidates { get; set; }
    public int EvaluatedCandidates { get; set; }
    public int PendingCandidates { get; set; }
    public decimal CompletionPercentage { get; set; }
    public DateTime? LastEvaluationDate { get; set; }
}
```

### 2. الكنترولر (Controller)

#### التحقق من دور رئيس اللجنة
```csharp
// التحقق من أن المستخدم هو رئيس لجنة في أي لجنة
var isCommitteeChair = await _context.CommitteeMembers
    .AnyAsync(cm => cm.ServiceNumber == serviceNumber && 
                   cm.IsActive && 
                   cm.Role == CommitteeMemberRole.رئيس_اللجنة);
```

#### حساب حالة تقييم الأعضاء
```csharp
private async Task<List<CommitteeMemberEvaluationStatusViewModel>> GetCommitteeMembersEvaluationStatus(List<int> committeeIds)
{
    var membersStatus = new List<CommitteeMemberEvaluationStatusViewModel>();

    foreach (var committeeId in committeeIds)
    {
        // الحصول على جميع أعضاء اللجنة
        var committeeMembers = await _context.CommitteeMembers
            .Include(cm => cm.User)
            .ThenInclude(u => u.Rank)
            .Where(cm => cm.CommitteeId == committeeId && cm.IsActive)
            .ToListAsync();

        // الحصول على جميع المرشحين المخصصين للجنة
        var committeeCandidates = await _context.CandidateCommitteeAssignments
            .Where(cca => cca.CommitteeId == committeeId && cca.IsActive)
            .Select(cca => cca.CandidateId)
            .ToListAsync();

        foreach (var member in committeeMembers)
        {
            // الحصول على تقييمات العضو لهذه اللجنة
            var memberEvaluations = await _context.IndividualEvaluations
                .Include(ie => ie.CandidateEvaluation)
                .Where(ie => ie.EvaluatorId == member.User.UserId && 
                           ie.CandidateEvaluation.CommitteeId == committeeId)
                .ToListAsync();

            // حساب الإحصائيات
            var totalCandidates = committeeCandidates.Count;
            var evaluatedCandidates = memberEvaluations.Count;
            var pendingCandidates = totalCandidates - evaluatedCandidates;
            var completionPercentage = totalCandidates > 0 ? 
                Math.Round((decimal)evaluatedCandidates / totalCandidates * 100, 1) : 0;
            var lastEvaluationDate = memberEvaluations.Any() ? 
                memberEvaluations.Max(ie => ie.EvaluatedAt) : null;

            membersStatus.Add(new CommitteeMemberEvaluationStatusViewModel
            {
                ServiceNumber = member.ServiceNumber,
                FullName = member.User?.FullName ?? "غير محدد",
                RankName = member.User?.Rank?.RankName ?? "غير محدد",
                Role = member.Role,
                TotalCandidates = totalCandidates,
                EvaluatedCandidates = evaluatedCandidates,
                PendingCandidates = pendingCandidates,
                CompletionPercentage = completionPercentage,
                LastEvaluationDate = lastEvaluationDate
            });
        }
    }

    return membersStatus.OrderBy(ms => ms.Role).ThenBy(ms => ms.FullName).ToList();
}
```

### 3. واجهة المستخدم (Views)

#### عرض قسم رئيس اللجنة
```html
<!-- حالة تقييم أعضاء اللجنة (عرض رئيس اللجنة) -->
@if (Model.IsCommitteeChair && Model.CommitteeMembersStatus.Any())
{
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        حالة تقييم أعضاء اللجنة
                    </h5>
                    <small class="text-muted">عرض خاص لرئيس اللجنة</small>
                </div>
                <!-- محتوى الجدول -->
            </div>
        </div>
    </div>
}
```

#### إشارة رئيس اللجنة
```html
@if (Model.IsCommitteeChair)
{
    <span class="badge bg-warning text-dark ms-2">
        <i class="fas fa-crown me-1"></i>رئيس اللجنة
    </span>
}
```

## الألوان والتصميم

### ألوان الأدوار
- **رئيس اللجنة**: أحمر (`bg-danger`)
- **نائب رئيس اللجنة**: أصفر (`bg-warning`)
- **منسق**: أخضر (`bg-success`)
- **عضو**: رمادي (`bg-secondary`)

### ألوان نسبة الإنجاز
- **100%**: أخضر (`bg-success`)
- **75-99%**: أصفر (`bg-warning`)
- **أقل من 75%**: أحمر (`bg-danger`)

## الأمان والصلاحيات

### التحقق من الصلاحيات
- يتم التحقق من دور المستخدم في كل طلب
- فقط رئيس اللجنة يمكنه رؤية قسم حالة الأعضاء
- يتم التحقق من عضوية المستخدم في اللجنة قبل عرض البيانات

### حماية البيانات
- لا يمكن لرئيس اللجنة رؤية تقييمات أعضاء لجان أخرى
- يتم تطبيق نفس قواعد الأمان الموجودة في النظام

## الاستخدام

### للمدير
1. تعيين عضو كرئيس لجنة من خلال إدارة أعضاء اللجان
2. رئيس اللجنة سيرى تلقائياً القسم الجديد عند الدخول لصفحة التقييم

### لرئيس اللجنة
1. الدخول لصفحة "تقييم المرشحين"
2. رؤية قسم "حالة تقييم أعضاء اللجنة" في أعلى الصفحة
3. مراقبة تقدم الأعضاء وتحديد من يحتاج متابعة
4. استخدام أدوات البحث والترتيب للعثور على معلومات محددة

## التطوير المستقبلي

### ميزات مقترحة
- **إشعارات تلقائية** للأعضاء المتأخرين في التقييم
- **تقارير مفصلة** عن أداء كل عضو
- **مقارنة الأداء** بين اللجان المختلفة
- **إحصائيات زمنية** لتتبع التقدم عبر الوقت

### تحسينات تقنية
- **تخزين مؤقت** للبيانات لتحسين الأداء
- **تصدير البيانات** إلى Excel أو PDF
- **واجهة API** للوصول للبيانات من تطبيقات أخرى 
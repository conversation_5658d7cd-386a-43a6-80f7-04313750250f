using System.ComponentModel.DataAnnotations;
using RafoEvaluation.Models;
using RafoEvaluation.Extensions;

namespace RafoEvaluation.ViewModels
{
    public class CommitteeListViewModel
    {
        public List<CommitteeListItemViewModel> Committees { get; set; } = new List<CommitteeListItemViewModel>();
        public string SearchTerm { get; set; } = string.Empty;
        public CommitteeStatus? StatusFilter { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCommittees { get; set; }
        public int ActiveCommittees { get; set; }
        public int InactiveCommittees { get; set; }
    }

    public class CommitteeListItemViewModel
    {
        public int CommitteeId { get; set; }
        public string CommitteeName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public CommitteeStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CommitteeCreateViewModel
    {
        [Required(ErrorMessage = "اسم اللجنة مطلوب")]
        [Display(Name = "اسم اللجنة")]
        [StringLength(100, ErrorMessage = "اسم اللجنة لا يمكن أن يتجاوز 100 حرف")]
        public string CommitteeName { get; set; } = string.Empty;

        [Display(Name = "وصف اللجنة")]
        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        public string? Description { get; set; }

        [Display(Name = "الحالة")]
        public CommitteeStatus Status { get; set; } = CommitteeStatus.Active;
    }

    public class CommitteeEditViewModel
    {
        public int CommitteeId { get; set; }

        [Required(ErrorMessage = "اسم اللجنة مطلوب")]
        [Display(Name = "اسم اللجنة")]
        [StringLength(100, ErrorMessage = "اسم اللجنة لا يمكن أن يتجاوز 100 حرف")]
        public string CommitteeName { get; set; } = string.Empty;

        [Display(Name = "وصف اللجنة")]
        [StringLength(500, ErrorMessage = "الوصف لا يمكن أن يتجاوز 500 حرف")]
        public string? Description { get; set; }

        [Display(Name = "الحالة")]
        public CommitteeStatus Status { get; set; }
    }

    public class CommitteeDetailsViewModel
    {
        public int CommitteeId { get; set; }
        public string CommitteeName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public CommitteeStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<CommitteeMemberDetailsViewModel> Members { get; set; } = new List<CommitteeMemberDetailsViewModel>();
    }


} 
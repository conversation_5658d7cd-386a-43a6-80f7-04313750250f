﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class RemoveMemberNameFromCommitteeMembers : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CommitteeMembers_Ranks_RankId",
                table: "CommitteeMembers");

            migrationBuilder.DropIndex(
                name: "IX_CommitteeMembers_RankId",
                table: "CommitteeMembers");

            migrationBuilder.DropColumn(
                name: "MemberName",
                table: "CommitteeMembers");

            migrationBuilder.DropColumn(
                name: "RankId",
                table: "CommitteeMembers");

            migrationBuilder.AlterColumn<string>(
                name: "ServiceNumber",
                table: "CommitteeMembers",
                type: "varchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Role",
                table: "CommitteeMembers",
                type: "int",
                nullable: false,
                defaultValue: 3,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 4);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_Users_ServiceNumber",
                table: "Users",
                column: "ServiceNumber");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 8, 56, 9, 320, DateTimeKind.Utc).AddTicks(448));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 8, 56, 9, 320, DateTimeKind.Utc).AddTicks(1113));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 8, 56, 9, 320, DateTimeKind.Utc).AddTicks(1115));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 8, 56, 9, 320, DateTimeKind.Utc).AddTicks(1116));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 8, 56, 9, 320, DateTimeKind.Utc).AddTicks(1117));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 8, 56, 9, 320, DateTimeKind.Utc).AddTicks(1118));

            migrationBuilder.AddForeignKey(
                name: "FK_CommitteeMembers_Users_ServiceNumber",
                table: "CommitteeMembers",
                column: "ServiceNumber",
                principalTable: "Users",
                principalColumn: "ServiceNumber",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CommitteeMembers_Users_ServiceNumber",
                table: "CommitteeMembers");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_Users_ServiceNumber",
                table: "Users");

            migrationBuilder.AlterColumn<string>(
                name: "ServiceNumber",
                table: "CommitteeMembers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<int>(
                name: "Role",
                table: "CommitteeMembers",
                type: "int",
                nullable: false,
                defaultValue: 4,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 3);

            migrationBuilder.AddColumn<string>(
                name: "MemberName",
                table: "CommitteeMembers",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "RankId",
                table: "CommitteeMembers",
                type: "int",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(53));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(856));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(858));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(859));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(861));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 16, 5, 34, 20, 209, DateTimeKind.Utc).AddTicks(862));

            migrationBuilder.CreateIndex(
                name: "IX_CommitteeMembers_RankId",
                table: "CommitteeMembers",
                column: "RankId");

            migrationBuilder.AddForeignKey(
                name: "FK_CommitteeMembers_Ranks_RankId",
                table: "CommitteeMembers",
                column: "RankId",
                principalTable: "Ranks",
                principalColumn: "RankId",
                onDelete: ReferentialAction.Restrict);
        }
    }
}

# التقارير الإحصائية - نظام تقييم لجان الترشيح

## نظرة عامة

تم إضافة نظام تقارير إحصائية شامل إلى نظام تقييم لجان الترشيح، يوفر تحليلات متقدمة ورسوم بيانية تفاعلية لعرض البيانات الإحصائية المختلفة.

## الميزات الرئيسية

### 📊 **الإحصائيات العامة**
- إجمالي التقييمات
- التقييمات المكتملة
- التقييمات قيد التنفيذ
- التقييمات في الانتظار

### 📈 **إحصائيات الدرجات**
- متوسط الدرجات
- أعلى درجة
- أقل درجة
- الانحراف المعياري

### 🎯 **الرسوم البيانية التفاعلية**

#### 1. **توزيع الفئات (Pie Chart)**
- عرض توزيع المرشحين حسب الفئات المختلفة
- النسب المئوية لكل فئة
- ألوان مميزة لكل فئة

#### 2. **توزيع اللجان (Pie Chart)**
- عرض توزيع التقييمات حسب اللجان
- النسب المئوية لكل لجنة
- إحصائيات مفصلة لكل لجنة

#### 3. **توزيع الدرجات (Bar Chart)**
- توزيع الدرجات في نطاقات (0-20، 20-40، 40-60، 60-80، 80-100)
- تحليل أداء المرشحين
- تحديد نقاط القوة والضعف

#### 4. **الاتجاهات الشهرية (Line Chart)**
- عرض عدد التقييمات خلال آخر 12 شهر
- تحليل الاتجاهات الزمنية
- تحديد فترات الذروة والانخفاض

### 📋 **جداول الأداء**

#### 1. **الأداء حسب الفئة**
- متوسط الدرجة لكل فئة
- عدد التقييمات
- أعلى وأقل درجة
- تحليل شامل للأداء

#### 2. **الأداء حسب اللجنة**
- متوسط الدرجة لكل لجنة
- عدد التقييمات
- أعلى وأقل درجة
- مقارنة أداء اللجان

### 📝 **آخر التقييمات**
- عرض آخر 10 تقييمات
- تفاصيل المرشح واللجنة
- الدرجة والحالة
- تاريخ التقييم

## كيفية الوصول

### للمديرين (Admin)
1. الدخول إلى النظام
2. الانتقال إلى "التقارير" من القائمة الجانبية
3. اختيار "التقارير الإحصائية"
4. عرض جميع الإحصائيات والرسوم البيانية

### لمنسقي اللجان ورؤساء اللجان
1. الدخول إلى النظام
2. الانتقال إلى "التقارير" من القائمة الجانبية
3. اختيار "التقارير الإحصائية"
4. عرض الإحصائيات الخاصة باللجان المسؤول عنها

## التقنيات المستخدمة

### Frontend
- **Chart.js**: لإنشاء الرسوم البيانية التفاعلية
- **Bootstrap 5**: للتصميم المتجاوب
- **Font Awesome**: للأيقونات
- **CSS Variables**: لنظام الألوان الموحد

### Backend
- **ASP.NET Core MVC**: إطار العمل الرئيسي
- **Entity Framework Core**: للوصول للبيانات
- **LINQ**: لاستعلامات البيانات المعقدة
- **C#**: لغة البرمجة

## الحسابات الإحصائية

### الانحراف المعياري
```csharp
private double CalculateStandardDeviation(IEnumerable<decimal> values)
{
    if (!values.Any()) return 0;
    
    var avg = values.Average();
    var sumOfSquares = values.Sum(x => Math.Pow((double)(x - avg), 2));
    return Math.Sqrt(sumOfSquares / values.Count());
}
```

### النسب المئوية
- يتم حساب النسب المئوية لكل فئة ولجنة
- عرض النسب في الرسوم البيانية
- تحديث النسب تلقائياً

### توزيع الدرجات
- تقسيم الدرجات إلى 5 نطاقات
- حساب عدد المرشحين في كل نطاق
- تحليل توزيع الأداء

## الأمان والصلاحيات

### صلاحيات الوصول
- **المديرون**: يمكنهم الوصول لجميع الإحصائيات
- **منسقو اللجان**: يمكنهم الوصول لإحصائيات لجانهم فقط
- **رؤساء اللجان**: يمكنهم الوصول لإحصائيات لجانهم فقط

### فلترة البيانات
- فلترة حسب الفئة
- فلترة حسب اللجنة
- فلترة حسب نموذج التقييم
- البحث بالاسم أو الرقم العسكري

## الاستجابة للشاشات المختلفة

### الشاشات الكبيرة (≥1200px)
- عرض 4 بطاقات إحصائيات في الصف
- رسوم بيانية كبيرة وواضحة
- جداول كاملة مع جميع الأعمدة

### الأجهزة اللوحية (768px-1024px)
- عرض 2 بطاقة إحصائيات في الصف
- رسوم بيانية متوسطة الحجم
- جداول مع أعمدة أساسية

### الهواتف المحمولة (≤576px)
- عرض بطاقة إحصائيات واحدة في الصف
- رسوم بيانية مضغوطة
- جداول قابلة للتمرير

## الملفات المضافة/المعدلة

### Controllers
- `ReportsController.cs`: إضافة `StatisticsReports` action

### ViewModels
- `ReportsViewModels.cs`: إضافة ViewModels جديدة
  - `StatisticsReportsViewModel`
  - `ChartDataViewModel`
  - `PerformanceDataViewModel`
  - `RecentEvaluationViewModel`

### Views
- `Views/Reports/StatisticsReports.cshtml`: الصفحة الرئيسية للتقارير الإحصائية
- `Views/Reports/Index.cshtml`: إضافة رابط للتقارير الإحصائية

## الميزات المستقبلية

### مخططات للتطوير
- [ ] إضافة رسوم بيانية أكثر تفصيلاً
- [ ] إمكانية تصدير التقارير بصيغ مختلفة
- [ ] إضافة مقاييس أداء إضافية
- [ ] إمكانية مقارنة فترات زمنية مختلفة
- [ ] إضافة تنبيهات للاتجاهات غير العادية

### تحسينات مقترحة
- [ ] إضافة رسوم بيانية تفاعلية أكثر
- [ ] إمكانية تخصيص التقارير
- [ ] إضافة تحليلات متقدمة
- [ ] دعم المزيد من أنواع الرسوم البيانية

## الدعم والمساعدة

### في حالة وجود مشاكل
1. التحقق من صلاحيات المستخدم
2. التأكد من وجود بيانات في النظام
3. مراجعة سجلات الأخطاء
4. التواصل مع فريق الدعم الفني

### للمطورين
- مراجعة الكود في `Controllers/ReportsController.cs`
- فحص ViewModels في `ViewModels/ReportsViewModels.cs`
- مراجعة التصميم في `Views/Reports/StatisticsReports.cshtml`

---

**تاريخ الإنشاء**: 2025-01-27  
**الإصدار**: 1.0  
**المطور**: فريق تطوير نظام تقييم لجان الترشيح
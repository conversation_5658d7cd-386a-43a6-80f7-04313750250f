﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class AddIndividualEvaluationCriteria : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "IndividualEvaluationCriteria",
                columns: table => new
                {
                    IndividualEvaluationCriteriaId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IndividualEvaluationId = table.Column<int>(type: "int", nullable: false),
                    EvaluationFormItemId = table.Column<int>(type: "int", nullable: false),
                    Score = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IndividualEvaluationCriteria", x => x.IndividualEvaluationCriteriaId);
                    table.ForeignKey(
                        name: "FK_IndividualEvaluationCriteria_EvaluationFormItems_EvaluationFormItemId",
                        column: x => x.EvaluationFormItemId,
                        principalTable: "EvaluationFormItems",
                        principalColumn: "EvaluationFormItemId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_IndividualEvaluationCriteria_IndividualEvaluations_IndividualEvaluationId",
                        column: x => x.IndividualEvaluationId,
                        principalTable: "IndividualEvaluations",
                        principalColumn: "IndividualEvaluationId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(2047));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3790));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3798));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3800));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3803));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 7, 12, 55, 950, DateTimeKind.Utc).AddTicks(3805));

            migrationBuilder.CreateIndex(
                name: "IX_IndividualEvaluationCriteria_EvaluationFormItemId",
                table: "IndividualEvaluationCriteria",
                column: "EvaluationFormItemId");

            migrationBuilder.CreateIndex(
                name: "IX_IndividualEvaluationCriteria_IndividualEvaluationId_EvaluationFormItemId",
                table: "IndividualEvaluationCriteria",
                columns: new[] { "IndividualEvaluationId", "EvaluationFormItemId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "IndividualEvaluationCriteria");

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9425));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9919));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9921));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9922));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9923));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 18, 12, 23, 4, 417, DateTimeKind.Utc).AddTicks(9924));
        }
    }
}

@model RafoEvaluation.ViewModels.CommitteeMemberDetailsViewModel
@{
    ViewData["Title"] = "حذف عضو اللجنة";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-trash breadcrumb-icon"></i>
                    حذف عضو اللجنة
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="CommitteeMember">
                                <i class="fas fa-users-cog"></i> أعضاء اللجان
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-trash"></i> حذف العضو
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle ms-2 text-danger"></i>تأكيد حذف العضو
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> تحذير!</h5>
                        <p>هل أنت متأكد من حذف العضو <strong>@Model.ServiceNumber</strong> من اللجنة <strong>@Model.CommitteeName</strong>؟</p>
                        <p>هذا الإجراء لا يمكن التراجع عنه.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">رقم الخدمة:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.ServiceNumber</strong>
                                </dd>

                                <dt class="col-sm-4">اسم المستخدم:</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.UserName))
                                    {
                                        <span class="badge bg-secondary">@Model.UserName</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">الرتبة:</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.RankName))
                                    {
                                        <span class="badge bg-warning text-dark">@Model.RankName</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">غير محدد</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">اللجنة:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-info">@Model.CommitteeName</span>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">الدور:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-primary">@Model.RoleDisplayName</span>
                                </dd>

                                <dt class="col-sm-4">الحالة:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غير نشط</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">تاريخ الإضافة:</dt>
                                <dd class="col-sm-8">
                                    <span class="text-muted">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <form asp-action="Delete" method="post">
                        <div class="form-group">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash ms-1"></i>تأكيد الحذف
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.CommitteeMemberId" class="btn btn-secondary">
                                <i class="fas fa-arrow-right ms-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 
@model List<RafoEvaluation.ViewModels.CandidateEvaluationResultViewModel>

@{
    ViewData["Title"] = "نتائج التقييم";
    var evaluationForm = ViewBag.EvaluationForm as RafoEvaluation.Models.EvaluationForm;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">نتائج التقييم - @(evaluationForm?.Title ?? "غير محدد")</h3>
                    <div class="card-tools">
                        <a asp-controller="CandidateEvaluation" asp-action="PrintResults" asp-route-evaluationFormId="@(evaluationForm?.EvaluationFormId ?? 0)" class="btn btn-info" target="_blank">
                            <i class="fas fa-print"></i> طباعة النتائج
                        </a>
                        <a asp-controller="CandidateEvaluation" asp-action="Index" asp-route-evaluationFormId="@(evaluationForm?.EvaluationFormId ?? 0)" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-users"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي المرشحين</span>
                                    <span class="info-box-number">@Model.Count</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-trophy"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">أعلى درجة</span>
                                    <span class="info-box-number">@(Model.Any() ? Model.Max(r => r.TotalScore ?? 0).ToString("F2") : "0.00")</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-chart-line"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">المعدل العام</span>
                                    <span class="info-box-number">@(Model.Any() ? Model.Where(r => r.TotalScore.HasValue).Average(r => r.TotalScore!.Value).ToString("F2") : "0.00")</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger">
                                    <i class="fas fa-sort-amount-down"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">أقل درجة</span>
                                    <span class="info-box-number">@(Model.Any() ? Model.Where(r => r.TotalScore.HasValue).Min(r => r.TotalScore!.Value).ToString("F2") : "0.00")</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="resultsTable">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 60px;">الترتيب</th>
                                        <th>الاسم</th>
                                        <th>الرتبة</th>
                                        <th>القاعدة الجوية</th>
                                        <th style="width: 120px;">الدرجة النهائية</th>
                                        <th style="width: 120px;">المجموع الموزون</th>
                                        <th style="width: 100px;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (int i = 0; i < Model.Count; i++)
                                    {
                                        var result = Model[i];
                                        var rank = i + 1;
                                        var rankClass = rank <= 3 ? "table-success" : "";
                                        
                                        <tr class="@rankClass">
                                            <td class="text-center">
                                                @if (rank == 1)
                                                {
                                                    <span class="badge badge-warning">🥇 @rank</span>
                                                }
                                                else if (rank == 2)
                                                {
                                                    <span class="badge badge-secondary">🥈 @rank</span>
                                                }
                                                else if (rank == 3)
                                                {
                                                    <span class="badge badge-warning">🥉 @rank</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-info">@rank</span>
                                                }
                                            </td>
                                            <td>@result.CandidateName</td>
                                            <td>@result.RankName</td>
                                            <td>@result.AirbaseName</td>
                                            <td class="text-center">
                                                <strong>@(result.TotalScore?.ToString("F2") ?? "-")</strong>
                                            </td>
                                            <td class="text-center">@(result.TotalScore?.ToString("F2") ?? "-")</td>
                                            <td>
                                                <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#detailsModal@(i)">
                                                    <i class="fas fa-eye"></i> التفاصيل
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Detail Modals -->
                        @for (int i = 0; i < Model.Count; i++)
                        {
                            var result = Model[i];
                            <div class="modal fade" id="detailsModal@(i)" tabindex="-1" role="dialog">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تفاصيل تقييم - @result.CandidateName</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <strong>الاسم:</strong> @result.CandidateName<br>
                                                    <strong>الرتبة:</strong> @result.RankName<br>
                                                    <strong>القاعدة الجوية:</strong> @result.AirbaseName
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>الدرجة النهائية:</strong> @(result.TotalScore?.ToString("F2") ?? "-")<br>
                                                    <strong>المجموع:</strong> @(result.TotalScore?.ToString("F2") ?? "-")<br>
                                                    <strong>الحالة:</strong> @result.StatusDisplayName
                                                </div>
                                            </div>
                                            
                                            <h6>تفاصيل المعايير</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>المعيار</th>
                                                            <th>الدرجة</th>
                                                            <th>الدرجة القصوى</th>
                                                            <th>ملاحظات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @* Removed EvaluationFormItems as it's not available in the new model *@
                                                        <tr>
                                                            <td colspan="4" class="text-center text-muted">تفاصيل المعايير غير متوفرة</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            لا توجد نتائج تقييم مكتملة لعرضها.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .info-box {
        display: block;
        min-height: 80px;
        background: #fff;
        width: 100%;
        box-shadow: 0 1px 1px rgba(0,0,0,0.1);
        border-radius: 2px;
        margin-bottom: 15px;
    }
    .info-box-icon {
        border-radius: 2px 0 0 2px;
        display: block;
        float: left;
        height: 80px;
        width: 80px;
        text-align: center;
        font-size: 40px;
        line-height: 80px;
        background: rgba(0,0,0,0.2);
    }
    .info-box-content {
        padding: 5px 10px;
        margin-left: 80px;
    }
    .info-box-text {
        display: block;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .info-box-number {
        display: block;
        font-weight: bold;
        font-size: 18px;
    }
</style>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#resultsTable').DataTable({
                "language": {
                    "url": "/localization/ar.json"
                },
                "order": [[4, "desc"]], // Sort by final score descending
                "pageLength": 25,
                "responsive": true
            });
        });
    </script>
} 
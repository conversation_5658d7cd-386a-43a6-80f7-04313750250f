# 🧪 اختبار النظام الحالي والتعديلات

## 📋 قائمة الاختبارات الشاملة

### **1. اختبار إدارة أعضاء اللجان**

#### **1.1 اختبار إضافة عضو جديد**
- [ ] الدخول كمدير النظام
- [ ] الذهاب إلى "إدارة أعضاء اللجان"
- [ ] إضافة عضو جديد مع:
  - [ ] اختيار لجنة
  - [ ] اختيار مستخدم
  - [ ] تعيين دور "رئيس اللجنة"
  - [ ] التأكد من تفعيل "يمكنه التقييم"
- [ ] التحقق من تعيين `MemberNumber = 1` تلقائياً

#### **1.2 اختبار إضافة أعضاء إضافيين**
- [ ] إضافة عضو ثاني بدور "عضو"
- [ ] التحقق من تعيين `MemberNumber = 2` تلقائياً
- [ ] إضافة عضو ثالث بدور "منسق"
- [ ] التحقق من إلغاء تفعيل "يمكنه التقييم" تلقائياً
- [ ] التحقق من تعيين `MemberNumber = 3`

#### **1.3 اختبار عرض الأعضاء**
- [ ] التحقق من ترتيب الأعضاء حسب `MemberNumber`
- [ ] التحقق من عرض الأسماء المنسقة:
  - [ ] "رئيس اللجنة (العضو الأول)"
  - [ ] "العضو الثاني"
  - [ ] "منسق"
- [ ] التحقق من ألوان الأدوار:
  - [ ] رئيس اللجنة: أحمر
  - [ ] عضو: أزرق
  - [ ] منسق: أخضر

### **2. اختبار التقارير المحدثة**

#### **2.1 اختبار تقرير تفاصيل اللجنة**
- [ ] الذهاب إلى "التقارير" > "تقارير اللجان"
- [ ] اختيار لجنة مع أعضاء
- [ ] التحقق من الإحصائيات المحسنة:
  - [ ] إجمالي الأعضاء
  - [ ] عدد المقيمين
  - [ ] نسبة الإنجاز
- [ ] التحقق من جدول الأعضاء:
  - [ ] ترتيب حسب `MemberNumber`
  - [ ] عرض الأسماء المنسقة
  - [ ] عرض قابلية التقييم

#### **2.2 اختبار تقرير التقييم المفصل**
- [ ] الذهاب إلى "التقارير" > "التقييمات المفصلة"
- [ ] اختيار تقييم مكتمل
- [ ] التحقق من ترتيب المقيمين:
  - [ ] رئيس اللجنة أولاً
  - [ ] الأعضاء بالترتيب الصحيح
  - [ ] عدم ظهور المنسقين
- [ ] التحقق من الأسماء المنسقة في الجدول

#### **2.3 اختبار تقرير الطباعة**
- [ ] طباعة تقرير التقييم المفصل
- [ ] التحقق من ترتيب المقيمين في النسخة المطبوعة
- [ ] التحقق من الأسماء المنسقة

### **3. اختبار منطق الترتيب**

#### **3.1 اختبار الترتيب التلقائي**
- [ ] حذف عضو من اللجنة
- [ ] إضافة عضو جديد
- [ ] التحقق من إعادة ترتيب `MemberNumber`
- [ ] التحقق من عدم تكرار الأرقام

#### **3.2 اختبار تغيير الأدوار**
- [ ] تغيير دور عضو من "عضو" إلى "رئيس اللجنة"
- [ ] التحقق من إعادة ترتيب الأعضاء
- [ ] التحقق من تعيين `MemberNumber = 1` للرئيس الجديد

#### **3.3 اختبار قابلية التقييم**
- [ ] تغيير دور عضو إلى "منسق"
- [ ] التحقق من إلغاء تفعيل "يمكنه التقييم"
- [ ] التحقق من عدم ظهوره في تقارير التقييم

### **4. اختبار واجهة المستخدم**

#### **4.1 اختبار الإحصائيات**
- [ ] التحقق من عرض الإحصائيات الصحيحة في صفحة الأعضاء
- [ ] التحقق من تحديث الإحصائيات عند إضافة/حذف أعضاء

#### **4.2 اختبار الألوان والتصميم**
- [ ] التحقق من ألوان الأدوار
- [ ] التحقق من ألوان قابلية التقييم
- [ ] التحقق من التصميم العام

#### **4.3 اختبار التفاعل**
- [ ] اختبار JavaScript في صفحة الإضافة/التعديل
- [ ] التحقق من تفعيل/إلغاء تفعيل "يمكنه التقييم" حسب الدور

### **5. اختبار قاعدة البيانات**

#### **5.1 اختبار الحقول الجديدة**
- [ ] التحقق من وجود حقل `MemberNumber` في جدول `CommitteeMembers`
- [ ] التحقق من وجود حقل `CanEvaluate` في جدول `CommitteeMembers`
- [ ] التحقق من القيم الافتراضية

#### **5.2 اختبار العلاقات**
- [ ] التحقق من العلاقة بين `CommitteeMembers` و `Users`
- [ ] التحقق من العلاقة بين `CommitteeMembers` و `Committees`

### **6. اختبار الأداء**

#### **6.1 اختبار سرعة التحميل**
- [ ] قياس وقت تحميل صفحة الأعضاء
- [ ] قياس وقت تحميل التقارير
- [ ] التحقق من عدم وجود بطء في الأداء

#### **6.2 اختبار الاستعلامات**
- [ ] التحقق من كفاءة استعلامات قاعدة البيانات
- [ ] التحقق من عدم وجود استعلامات مكررة

## 🚨 الاختبارات الحرجة

### **اختبارات يجب أن تعمل 100%:**
1. ✅ ترتيب الأعضاء حسب `MemberNumber`
2. ✅ عرض الأسماء المنسقة في جميع التقارير
3. ✅ استبعاد المنسقين من تقارير التقييم
4. ✅ تفعيل/إلغاء تفعيل "يمكنه التقييم" حسب الدور
5. ✅ عدم تكرار أرقام الأعضاء

### **اختبارات الأداء:**
1. ✅ سرعة تحميل الصفحات
2. ✅ استجابة واجهة المستخدم
3. ✅ كفاءة قاعدة البيانات

## 📊 نتائج الاختبار

### **الاختبارات المنجزة:**
- [ ] إدارة أعضاء اللجان
- [ ] التقارير المحدثة
- [ ] منطق الترتيب
- [ ] واجهة المستخدم
- [ ] قاعدة البيانات
- [ ] الأداء

### **المشاكل المكتشفة:**
- [ ] قائمة المشاكل

### **التحسينات المقترحة:**
- [ ] قائمة التحسينات

## 🎯 ملاحظات الاختبار

### **بيئة الاختبار:**
- النظام: Windows 10
- المتصفح: Chrome/Firefox/Edge
- قاعدة البيانات: SQLite
- الإطار: ASP.NET Core 9.0

### **بيانات الاختبار:**
- لجنة اختبار واحدة
- 5 أعضاء (رئيس + 3 أعضاء + منسق)
- 3 تقييمات مكتملة

### **المستخدمين:**
- مدير النظام (Admin)
- رئيس اللجنة
- عضو لجنة
- منسق

---

## 📝 تعليمات الاختبار

1. **تشغيل النظام:** `dotnet run`
2. **الدخول:** استخدام بيانات الاختبار
3. **اتباع قائمة الاختبارات:** واحدة تلو الأخرى
4. **تسجيل النتائج:** في هذا الملف
5. **الإبلاغ عن المشاكل:** مع تفاصيل كاملة

---

*تم إنشاء هذا الملف في: @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")* 
@model RafoEvaluation.ViewModels.IndividualEvaluationCreateViewModel
@{
    ViewData["Title"] = "إضافة تقييم فردي";
    var candidateName = ViewBag.CandidateName as string;
    var committeeName = ViewBag.CommitteeName as string;
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus"></i>
                        إضافة تقييم فردي
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Candidate Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-user"></i> معلومات المرشح</h6>
                                <p class="mb-1"><strong>الاسم:</strong> @candidateName</p>
                                <p class="mb-0"><strong>اللجنة:</strong> @committeeName</p>
                            </div>
                        </div>
                    </div>

                    <form asp-action="Create" method="post">
                        <input type="hidden" asp-for="CandidateEvaluationId" />
                        <input type="hidden" asp-for="EvaluatorId" />

                        <!-- Total Score -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="TotalScore" class="form-label">
                                    <i class="fas fa-star"></i>
                                    الدرجة الإجمالية
                                </label>
                                <div class="input-group">
                                    <input asp-for="TotalScore" class="form-control" type="number" 
                                           min="0" max="100" step="0.1" 
                                           onchange="updatePercentage()" />
                                    <span class="input-group-text">/ 100</span>
                                </div>
                                <div class="form-text">
                                    <span id="percentageDisplay">0%</span>
                                    <span id="scoreLevel" class="badge ms-2"></span>
                                </div>
                                <span asp-validation-for="TotalScore" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">أزرار سريعة للدرجات</label>
                                <div class="d-grid gap-2">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="setScore(95)">ممتاز (95)</button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="setScore(90)">جيد جداً (90)</button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="setScore(85)">جيد (85)</button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="setScore(80)">مقبول (80)</button>
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="setScore(75)">ضعيف (75)</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Attendance -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="form-check">
                                    <input asp-for="IsPresent" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsPresent" class="form-check-label">
                                        <i class="fas fa-check-circle"></i>
                                        حضر التقييم
                                    </label>
                                </div>
                                <div class="form-text">
                                    إذا لم تحضر التقييم، سيتم تسجيلك كغائب
                                </div>
                            </div>
                        </div>

                        <!-- Evaluator Notes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label asp-for="EvaluatorNotes" class="form-label">
                                    <i class="fas fa-comment"></i>
                                    ملاحظات المقيم
                                </label>
                                <textarea asp-for="EvaluatorNotes" class="form-control" rows="4" 
                                          placeholder="أضف ملاحظاتك حول التقييم (اختياري)"></textarea>
                                <div class="form-text">
                                    يمكنك إضافة ملاحظات حول نقاط القوة أو الضعف أو أي ملاحظات أخرى
                                </div>
                                <span asp-validation-for="EvaluatorNotes" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save"></i>
                                    حفظ التقييم
                                </button>
                                <a href="@Url.Action("Index", new { candidateEvaluationId = Model.CandidateEvaluationId })" 
                                   class="btn btn-secondary btn-lg">
                                    <i class="fas fa-arrow-left"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Evaluation Guidelines -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        إرشادات التقييم
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>معايير الدرجات:</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-success">95-100</span> ممتاز</li>
                                <li><span class="badge bg-success">90-94</span> جيد جداً</li>
                                <li><span class="badge bg-warning">85-89</span> جيد</li>
                                <li><span class="badge bg-warning">80-84</span> مقبول</li>
                                <li><span class="badge bg-danger">75-79</span> ضعيف</li>
                                <li><span class="badge bg-danger">أقل من 75</span> غير مقبول</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>نصائح للتقييم:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> كن موضوعياً في التقييم</li>
                                <li><i class="fas fa-check text-success"></i> ركز على الأداء الفعلي</li>
                                <li><i class="fas fa-check text-success"></i> اكتب ملاحظات واضحة</li>
                                <li><i class="fas fa-check text-success"></i> تجنب التحيز الشخصي</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        function setScore(score) {
            document.getElementById('TotalScore').value = score;
            updatePercentage();
        }

        function updatePercentage() {
            var score = parseFloat(document.getElementById('TotalScore').value) || 0;
            var percentage = (score / 100) * 100;
            var percentageDisplay = document.getElementById('percentageDisplay');
            var scoreLevel = document.getElementById('scoreLevel');
            
            percentageDisplay.textContent = percentage.toFixed(1) + '%';
            
            // Update score level badge
            var level = '';
            var badgeClass = '';
            
            if (score >= 95) {
                level = 'ممتاز';
                badgeClass = 'bg-success';
            } else if (score >= 90) {
                level = 'جيد جداً';
                badgeClass = 'bg-success';
            } else if (score >= 85) {
                level = 'جيد';
                badgeClass = 'bg-warning';
            } else if (score >= 80) {
                level = 'مقبول';
                badgeClass = 'bg-warning';
            } else if (score >= 75) {
                level = 'ضعيف';
                badgeClass = 'bg-danger';
            } else {
                level = 'غير مقبول';
                badgeClass = 'bg-danger';
            }
            
            scoreLevel.textContent = level;
            scoreLevel.className = 'badge ms-2 ' + badgeClass;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updatePercentage();
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            var score = parseFloat(document.getElementById('TotalScore').value) || 0;
            var isPresent = document.getElementById('IsPresent').checked;
            
            if (score < 0 || score > 100) {
                e.preventDefault();
                alert('الدرجة يجب أن تكون بين 0 و 100');
                return false;
            }
            
            if (!isPresent && score > 0) {
                if (!confirm('أنت مسجل كغائب ولكنك أعطيت درجة. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    </script>
} 
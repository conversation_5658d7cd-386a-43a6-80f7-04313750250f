@model RafoEvaluation.ViewModels.RankViewModel
@{
    ViewData["Title"] = "حذف الرتبة";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-medal breadcrumb-icon"></i>
                    حذف الرتبة
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Rank">
                                <i class="fas fa-medal"></i> الرتب
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-trash"></i> حذف
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle ms-2"></i>تأكيد حذف الرتبة
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>تحذير!
                        </h5>
                        <p class="mb-0">
                            أنت على وشك حذف الرتبة التالية. هذا الإجراء لا يمكن التراجع عنه.
                            تأكد من عدم وجود مستخدمين مرتبطين بهذه الرتبة قبل المتابعة.
                        </p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-medal ms-1"></i>اسم الرتبة
                            </label>
                            <div class="form-control-plaintext">
                                <strong class="fs-5 text-danger">@Model.RankName</strong>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-sort-numeric-up ms-1"></i>ترتيب العرض
                            </label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Model.DisplayOrder</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on ms-1"></i>الحالة
                            </label>
                            <div class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-times-circle me-1"></i>غير نشط
                                    </span>
                                }
                            </div>
                        </div>


                    </div>



                    <form asp-action="Delete" method="post" id="deleteRankForm">
                        <input type="hidden" asp-for="RankId" />
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                            </a>
                            <div>
                                <a asp-action="Edit" asp-route-id="@Model.RankId" class="btn btn-warning me-2">
                                    <i class="fas fa-edit me-1"></i>تعديل بدلاً من الحذف
                                </a>
                                <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الرتبة؟')">
                                    <i class="fas fa-trash me-1"></i>تأكيد الحذف
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 
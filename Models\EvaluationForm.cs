using System.ComponentModel.DataAnnotations;
using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Models
{
    public class EvaluationForm
    {
        [Key]
        public int EvaluationFormId { get; set; }

        // Removed CommitteeId - forms are now independent of committees

        [Required(ErrorMessage = "عنوان النموذج مطلوب")]
        [Display(Name = "عنوان النموذج")]
        [StringLength(200, ErrorMessage = "عنوان النموذج لا يمكن أن يتجاوز 200 حرف")]
        public string Title { get; set; } = string.Empty;

        [Display(Name = "وصف الفورم")]
        [StringLength(1000, ErrorMessage = "الوصف لا يمكن أن يتجاوز 1000 حرف")]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<EvaluationFormItem> Items { get; set; } = new List<EvaluationFormItem>();
        public virtual ICollection<CandidateEvaluation> CandidateEvaluations { get; set; } = new List<CandidateEvaluation>();
    }


} 
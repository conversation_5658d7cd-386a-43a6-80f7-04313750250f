using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.ViewModels;
using Microsoft.AspNetCore.Authorization;
using System.Linq;
using System.Threading.Tasks;

namespace RafoEvaluation.Controllers
{
    [Authorize(Roles = "Admin")]
    public class AirbaseController : Controller
    {
        private readonly ApplicationDbContext _context;

        public AirbaseController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Airbase
        public async Task<IActionResult> Index(string searchTerm = "", int page = 1)
        {
            const int pageSize = 10;
            var query = _context.Airbases.AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(a => a.AirbaseName.Contains(searchTerm));
            }

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

            var airbases = await query
                .OrderBy(a => a.AirbaseName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(a => new AirbaseViewModel
                {
                    AirbaseId = a.AirbaseId,
                    AirbaseName = a.AirbaseName,
                    TotalCandidates = a.Candidates.Count()
                })
                .ToListAsync();

            var viewModel = new AirbaseListViewModel
            {
                Airbases = airbases,
                SearchTerm = searchTerm,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount,
                PageSize = pageSize
            };

            return View(viewModel);
        }

        // GET: Airbase/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var airbase = await _context.Airbases
                .Include(a => a.Candidates)
                .FirstOrDefaultAsync(m => m.AirbaseId == id);

            if (airbase == null)
            {
                return NotFound();
            }

            var viewModel = new AirbaseViewModel
            {
                AirbaseId = airbase.AirbaseId,
                AirbaseName = airbase.AirbaseName,
                TotalCandidates = airbase.Candidates.Count()
            };

            return View(viewModel);
        }

        // GET: Airbase/Create
        public IActionResult Create()
        {
            return View(new AirbaseCreateViewModel());
        }

        // POST: Airbase/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(AirbaseCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Check for duplicate name
                if (await _context.Airbases.AnyAsync(a => a.AirbaseName == model.AirbaseName))
                {
                    ModelState.AddModelError("AirbaseName", "An airbase with this name already exists.");
                    return View(model);
                }

                var airbase = new Airbase
                {
                    AirbaseName = model.AirbaseName
                };

                _context.Add(airbase);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Airbase created successfully.";
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }

        // GET: Airbase/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var airbase = await _context.Airbases.FindAsync(id);
            if (airbase == null)
            {
                return NotFound();
            }

            var viewModel = new AirbaseEditViewModel
            {
                AirbaseId = airbase.AirbaseId,
                AirbaseName = airbase.AirbaseName
            };

            return View(viewModel);
        }

        // POST: Airbase/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, AirbaseEditViewModel model)
        {
            if (id != model.AirbaseId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                // Check for duplicate name, excluding the current item
                if (await _context.Airbases.AnyAsync(a => a.AirbaseName == model.AirbaseName && a.AirbaseId != id))
                {
                    ModelState.AddModelError("AirbaseName", "An airbase with this name already exists.");
                    return View(model);
                }

                try
                {
                    var airbaseToUpdate = await _context.Airbases.FindAsync(id);
                    if (airbaseToUpdate == null)
                    {
                        return NotFound();
                    }

                    airbaseToUpdate.AirbaseName = model.AirbaseName;
                    _context.Update(airbaseToUpdate);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await AirbaseExists(model.AirbaseId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                TempData["Success"] = "Airbase updated successfully.";
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }

        // GET: Airbase/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var airbase = await _context.Airbases
                .Include(a => a.Candidates)
                .FirstOrDefaultAsync(m => m.AirbaseId == id);

            if (airbase == null)
            {
                return NotFound();
            }

            var viewModel = new AirbaseViewModel
            {
                AirbaseId = airbase.AirbaseId,
                AirbaseName = airbase.AirbaseName,
                TotalCandidates = airbase.Candidates.Count()
            };

            return View(viewModel);
        }

        // POST: Airbase/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var airbase = await _context.Airbases.Include(a => a.Candidates).FirstOrDefaultAsync(a => a.AirbaseId == id);
            if (airbase == null)
            {
                TempData["Error"] = "Airbase not found.";
                return RedirectToAction(nameof(Index));
            }

            if (airbase.Candidates.Any())
            {
                TempData["Error"] = $"Cannot delete airbase '{airbase.AirbaseName}' because it has associated candidates.";
                return RedirectToAction(nameof(Index));
            }

            _context.Airbases.Remove(airbase);
            await _context.SaveChangesAsync();
            TempData["Success"] = "Airbase deleted successfully.";
            return RedirectToAction(nameof(Index));
        }

        private async Task<bool> AirbaseExists(int id)
        {
            return await _context.Airbases.AnyAsync(e => e.AirbaseId == id);
        }
    }
}

@model RafoEvaluation.ViewModels.AirbaseListViewModel
@{
    ViewData["Title"] = "القواعد الجوية";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-building breadcrumb-icon"></i>
                    إدارة القواعد
                </h1>
            </div>
            <div class="col-md-6 d-flex justify-content-end align-items-center">
                <nav aria-label="breadcrumb" class="flex-grow-1">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-building"></i> القواعد
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building ms-2"></i>إدارة القواعد
                    </h3>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus ms-1"></i> إضافة قاعدة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <!-- جدول القواعد الجوية -->
                    @if (Model.Airbases.Any())
                    {
                        <div class="table-responsive">
                            <table id="airbase-table" class="table table-striped table-hover data-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم القاعدة</th>
                                        <th>عدد المرشحين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.Airbases)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">@item.AirbaseId</span>
                                            </td>
                                            <td>
                                                <strong>@item.AirbaseName</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@item.TotalCandidates</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.AirbaseId" 
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.AirbaseId" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.AirbaseId" 
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لم يتم العثور على قواعد</h4>
                            <p class="text-muted">
                                ابدأ بـ <a asp-action="Create" class="text-decoration-none">إضافة أول قاعدة</a>.
                            </p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

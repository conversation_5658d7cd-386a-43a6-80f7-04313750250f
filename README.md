# RafoEvaluation - Stagewise Development Setup

A comprehensive ASP.NET Core application for evaluation management with stagewise development tools and configurations.

## 🚀 Quick Start

### Prerequisites
- .NET 9.0 SDK
- SQL Server (LocalDB or full instance)
- PowerShell 7+ (for development tools)

### Getting Started
```powershell
# Clone the repository
git clone <repository-url>
cd RafoEvaluation

# Start development environment
.\dev-tools\dev-utils.ps1 start

# Or use the individual script
.\dev-tools\dev-start.ps1
```

## 🏗️ Environment Stages

### Development (`Development`)
- **Database**: `RafoEvaluation_Dev`
- **Logging**: Debug level with console and file output
- **Features**: 
  - Swagger API documentation at `/api-docs`
  - Detailed error pages
  - SQL query logging
  - Database error pages
  - Seed data enabled

### Staging (`Staging`)
- **Database**: `RafoEvaluation_Staging`
- **Logging**: Information level with file output
- **Features**:
  - Production-like settings
  - Limited debugging features
  - Performance monitoring

### Production (`Production`)
- **Database**: `RafoEvaluation`
- **Logging**: Warning level with file output only
- **Features**:
  - Optimized for performance
  - Security-focused
  - Minimal logging

## 🛠️ Development Tools

### Main Utility Script
```powershell
# Quick access to all development tasks
.\dev-tools\dev-utils.ps1 <action> [-Environment <env>]

# Examples:
.\dev-utils.ps1 start                    # Start development environment
.\dev-utils.ps1 build -Environment Production  # Build for production
.\dev-utils.ps1 logs -Environment Staging      # View staging logs
.\dev-utils.ps1 health                   # Check application health
.\dev-utils.ps1 help                     # Show all available commands
```

### Individual Tools

#### Application Management
- **`dev-start.ps1`** - Start the application with proper configuration
- **`dev-build.ps1`** - Build for different environments
- **`health-check.ps1`** - Comprehensive health monitoring

#### Database Management
- **`db-migrate.ps1`** - Manage database migrations
  ```powershell
  .\dev-tools\db-migrate.ps1                    # Update database
  .\dev-tools\db-migrate.ps1 -Action add -MigrationName "AddUserProfile"
  .\dev-tools\db-migrate.ps1 -Action list
  ```

#### Logging and Monitoring
- **`logs-view.ps1`** - View and manage application logs
  ```powershell
  .\dev-tools\logs-view.ps1                     # View latest logs
  .\dev-tools\logs-view.ps1 -Action tail        # Tail logs in real-time
  .\dev-tools\logs-view.ps1 -Filter "ERROR"     # Filter error logs
  .\dev-tools\logs-view.ps1 -Action clear       # Clear logs
  ```

## 📁 Project Structure

```
RafoEvaluation/
├── Controllers/           # MVC Controllers
├── Data/                 # Entity Framework context
├── Models/               # Domain models
├── Services/             # Business logic services
├── ViewModels/           # View models
├── Views/                # Razor views
├── dev-tools/            # Development utilities
│   ├── README.md         # Tools documentation
│   ├── dev-utils.ps1     # Main utility script
│   ├── dev-start.ps1     # Application startup
│   ├── dev-build.ps1     # Build management
│   ├── db-migrate.ps1    # Database migrations
│   ├── logs-view.ps1     # Log management
│   └── health-check.ps1  # Health monitoring
├── logs/                 # Application logs (auto-created)
├── appsettings.json      # Base configuration
├── appsettings.Development.json
├── appsettings.Staging.json
└── appsettings.Production.json
```

## 🔧 Configuration

### Environment Variables
```powershell
# Set environment
$env:ASPNETCORE_ENVIRONMENT = "Development"  # or "Staging", "Production"
```

### Connection Strings
Each environment uses a different database:
- **Development**: `RafoEvaluation_Dev`
- **Staging**: `RafoEvaluation_Staging`
- **Production**: `RafoEvaluation`

### Logging Configuration
- **Development**: Console + File logging with Debug level
- **Staging**: File logging with Information level
- **Production**: File logging with Warning level

## 📊 Monitoring and Health

### Health Check
```powershell
# Quick health check
.\dev-tools\health-check.ps1

# Detailed health information
.\dev-tools\health-check.ps1 -Detailed
```

### Log Monitoring
```powershell
# View logs in real-time
.\dev-tools\logs-view.ps1 -Action tail

# Filter for errors
.\dev-tools\logs-view.ps1 -Filter "ERROR|FATAL"

# View logs from specific environment
.\dev-tools\logs-view.ps1 -Environment Staging
```

## 🚀 Deployment

### Building for Production
```powershell
# Build for production
.\dev-tools\dev-build.ps1 -Environment Production -Publish

# This creates a publish folder at: publish/Production/
```

### Environment-Specific Builds
```powershell
# Development build
.\dev-tools\dev-build.ps1 -Environment Development

# Staging build
.\dev-tools\dev-build.ps1 -Environment Staging -Configuration Release

# Production build
.\dev-tools\dev-build.ps1 -Environment Production -Configuration Release -Publish
```

## 🔍 API Documentation

When running in development mode, Swagger documentation is available at:
```
https://localhost:5001/api-docs
```

## 📝 Logging

Logs are automatically created in the `logs/` directory:
- `dev-YYYY-MM-DD.log` - Development logs
- `staging-YYYY-MM-DD.log` - Staging logs
- `production-YYYY-MM-DD.log` - Production logs

### Log Levels
- **Development**: Debug, Information, Warning, Error, Fatal
- **Staging**: Information, Warning, Error, Fatal
- **Production**: Warning, Error, Fatal

## 🛡️ Security

### Development
- Detailed error pages enabled
- SQL query logging enabled
- Swagger documentation available

### Staging
- Limited error details
- Performance monitoring
- Production-like security

### Production
- Minimal error details
- Security-focused configuration
- Optimized performance

## 🔄 Database Management

### Migrations
```powershell
# Add new migration
.\dev-tools\db-migrate.ps1 -Action add -MigrationName "AddNewFeature"

# Update database
.\dev-tools\db-migrate.ps1 -Environment Staging

# List migrations
.\dev-tools\db-migrate.ps1 -Action list
```

### Environment-Specific Databases
- Each environment uses a separate database
- Migrations are applied per environment
- Seed data is only applied in development

## 🧪 Testing

```powershell
# Run tests
.\dev-tools\dev-utils.ps1 test

# Or directly
dotnet test --configuration Debug
```

## 🧹 Maintenance

### Cleaning Build Artifacts
```powershell
# Clean and restore
.\dev-tools\dev-utils.ps1 clean

# Or with build
.\dev-tools\dev-build.ps1 -Clean
```

### Log Management
```powershell
# Clear logs for specific environment
.\dev-tools\logs-view.ps1 -Environment Development -Action clear
```

## 📚 Best Practices

1. **Always use the appropriate environment** for your development stage
2. **Use the provided scripts** for common development tasks
3. **Monitor logs** during development and testing
4. **Test in staging** before deploying to production
5. **Never commit sensitive data** to version control
6. **Use health checks** to monitor application status
7. **Keep databases separate** for each environment

## 🆘 Troubleshooting

### Common Issues

#### Application Won't Start
```powershell
# Check health
.\dev-tools\health-check.ps1 -Detailed

# Check logs
.\dev-tools\logs-view.ps1 -Lines 100
```

#### Database Connection Issues
```powershell
# Run migrations
.\dev-tools\db-migrate.ps1

# Check database health
.\dev-tools\health-check.ps1
```

#### Build Failures
```powershell
# Clean and rebuild
.\dev-tools\dev-build.ps1 -Clean

# Check for missing packages
dotnet restore
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Use the development tools for all common tasks
2. Follow the stagewise development approach
3. Test in staging before production
4. Monitor logs and health checks
5. Document any new features or changes

---

**Note**: This project uses AI-generated code that has been reviewed, tested, and documented with the same rigor as human-written code. All AI-generated components are explicitly marked in the version control system for traceability. 
# IIS Deployment Guide for RafoEvaluation

This guide provides step-by-step instructions for deploying the RafoEvaluation application to IIS Server.

## Prerequisites

1. **Windows Server** with IIS installed
2. **ASP.NET Core Hosting Bundle** installed
3. **SQL Server** (RAFO\SQLEXPRESS)
4. **Administrator privileges** for IIS configuration

## Installation Steps

### 1. Install ASP.NET Core Hosting Bundle

Download and install the ASP.NET Core Hosting Bundle from Microsoft:
- [ASP.NET Core 9.0 Hosting Bundle](https://dotnet.microsoft.com/download/dotnet/9.0)

### 2. Build the Application

#### Option A: Use the Automated Build Script (Recommended)

```powershell
# Navigate to project directory
cd C:\path\to\RafoEvaluation

# Build and publish with clean (removes previous builds)
.\build_for_iis.ps1 -Clean

# Or build without cleaning
.\build_for_iis.ps1
```

#### Option B: Manual Build Commands

```powershell
# Navigate to project directory
cd C:\path\to\RafoEvaluation

# Restore packages
dotnet restore

# Build the project (not the solution to avoid warnings)
dotnet build RafoEvaluation.csproj -c Release

# Publish the application
dotnet publish RafoEvaluation.csproj -c Release -o C:\inetpub\wwwroot\RafoEval
```

**Note**: Use `dotnet build RafoEvaluation.csproj` instead of `dotnet build RafoEvaluation.sln` to avoid the NETSDK1194 warning.

### 3. Configure IIS

#### Option A: Use the Automated Script

```powershell
# Run as Administrator
.\deploy_to_iis.ps1
```

#### Option B: Manual Configuration

1. **Open IIS Manager**
2. **Create Application Pool**:
   - Name: `RafoEvaluation`
   - .NET CLR Version: `No Managed Code`
   - Managed Pipeline Mode: `Integrated`

3. **Create Website**:
   - Site Name: `RafoEvaluation`
   - Physical Path: `C:\inetpub\wwwroot\RafoEval`
   - Port: `80` (or your preferred port)
   - Application Pool: `RafoEvaluation`

4. **Set Permissions**:
   - Right-click on `C:\inetpub\wwwroot\RafoEval`
   - Properties → Security → Edit
   - Add `IIS_IUSRS` with Full Control
   - Add `IUSR` with Read & Execute

### 4. Database Configuration

Ensure your SQL Server connection string is correct in `appsettings.Production.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=RAFO\\SQLEXPRESS;Database=RafoEvaluation;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

### 5. Environment Variables

Set the following environment variables in IIS:
- `ASPNETCORE_ENVIRONMENT`: `Production`

## Configuration Files

### appsettings.Production.json
- Optimized for production performance
- Reduced logging verbosity
- IIS-specific settings enabled

### web.config
- Configured for in-process hosting
- Custom error pages
- Static file caching
- Compression enabled

## Troubleshooting

### Common Issues

1. **NETSDK1194 Warning**: "--output" option isn't supported when building a solution
   - **Solution**: Use `dotnet build RafoEvaluation.csproj` instead of `dotnet build RafoEvaluation.sln`
   - **Alternative**: Use the provided `build_for_iis.ps1` script

2. **500.19 Error**: Missing ASP.NET Core Module
   - Install ASP.NET Core Hosting Bundle

3. **500.30 Error**: Application failed to start
   - Check application pool configuration
   - Verify .NET Core installation

4. **403 Forbidden**: Permission issues
   - Ensure IIS_IUSRS has proper permissions
   - Check file system permissions

5. **Database Connection**: Connection timeout
   - Verify SQL Server is running
   - Check connection string
   - Ensure Windows Authentication is enabled

6. **Build Failures**: Package restore issues
   - Run `dotnet restore` before building
   - Check internet connection for NuGet packages
   - Clear NuGet cache: `dotnet nuget locals all --clear`

### Log Files

Application logs are stored in:
- `C:\inetpub\wwwroot\RafoEval\logs\`
- IIS logs: `%SystemDrive%\inetpub\logs\LogFiles\`

### Performance Optimization

1. **Enable Compression** (already configured in web.config)
2. **Static File Caching** (already configured)
3. **Application Pool Recycling**: Set to recycle during low-traffic hours
4. **Database Connection Pooling**: Already enabled in Entity Framework

## Security Considerations

1. **HTTPS**: Configure SSL certificate for production
2. **Windows Authentication**: Configure if needed
3. **File Permissions**: Minimal required permissions
4. **Error Pages**: Custom error pages to avoid information disclosure

## Monitoring

1. **Application Insights**: Consider adding for production monitoring
2. **IIS Logs**: Monitor for errors and performance
3. **Database Monitoring**: Monitor connection pool and query performance

## Backup and Recovery

1. **Application Files**: Backup `C:\inetpub\wwwroot\RafoEval`
2. **Database**: Regular SQL Server backups
3. **Configuration**: Backup IIS configuration and appsettings files

## Support

For issues related to:
- **IIS Configuration**: Check Windows Event Logs
- **Application Errors**: Check application logs in `logs\` directory
- **Database Issues**: Check SQL Server logs and connection strings 
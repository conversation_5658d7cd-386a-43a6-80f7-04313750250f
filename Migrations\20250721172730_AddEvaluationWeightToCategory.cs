﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class AddEvaluationWeightToCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EvaluationCriteria");

            migrationBuilder.AddColumn<decimal>(
                name: "EvaluationWeight",
                table: "Categories",
                type: "decimal(5,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "Categories",
                keyColumn: "CategoryId",
                keyValue: 1,
                column: "EvaluationWeight",
                value: 30.00m);

            migrationBuilder.UpdateData(
                table: "Categories",
                keyColumn: "CategoryId",
                keyValue: 2,
                column: "EvaluationWeight",
                value: 30.00m);

            migrationBuilder.UpdateData(
                table: "Categories",
                keyColumn: "CategoryId",
                keyValue: 3,
                column: "EvaluationWeight",
                value: 30.00m);

            migrationBuilder.UpdateData(
                table: "Categories",
                keyColumn: "CategoryId",
                keyValue: 4,
                column: "EvaluationWeight",
                value: 30.00m);

            migrationBuilder.UpdateData(
                table: "Categories",
                keyColumn: "CategoryId",
                keyValue: 5,
                column: "EvaluationWeight",
                value: 30.00m);

            migrationBuilder.UpdateData(
                table: "Categories",
                keyColumn: "CategoryId",
                keyValue: 6,
                column: "EvaluationWeight",
                value: 30.00m);

            migrationBuilder.UpdateData(
                table: "Categories",
                keyColumn: "CategoryId",
                keyValue: 7,
                column: "EvaluationWeight",
                value: 30.00m);

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(3161));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4416));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4420));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4421));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4422));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 21, 17, 27, 29, 332, DateTimeKind.Utc).AddTicks(4424));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EvaluationWeight",
                table: "Categories");

            migrationBuilder.CreateTable(
                name: "EvaluationCriteria",
                columns: table => new
                {
                    CriteriaId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    CriteriaName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    MaxScore = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationCriteria", x => x.CriteriaId);
                });

            migrationBuilder.InsertData(
                table: "EvaluationCriteria",
                columns: new[] { "CriteriaId", "CreatedAt", "CriteriaName", "Description", "DisplayOrder", "IsActive", "MaxScore", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "المظهر العام واللياقة البدنية", "تقييم المظهر العام واللياقة البدنية للمرشح", 1, true, 10, null },
                    { 2, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الثقافة العامة", "تقييم مستوى الثقافة العامة والمعرفة", 2, true, 15, null },
                    { 3, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "المهارات اللغوية", "تقييم مهارات اللغة العربية والإنجليزية", 3, true, 15, null },
                    { 4, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "المهارات التقنية", "تقييم المهارات التقنية والمعلوماتية", 4, true, 20, null },
                    { 5, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الشخصية والقيادة", "تقييم الشخصية ومهارات القيادة", 5, true, 20, null },
                    { 6, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الدوافع والطموح", "تقييم دوافع المرشح وطموحاته المهنية", 6, true, 10, null },
                    { 7, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "الخبرة العملية", "تقييم الخبرة العملية السابقة", 7, true, 10, null }
                });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6233));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6747));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6749));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6750));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6750));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 19, 8, 9, 9, 150, DateTimeKind.Utc).AddTicks(6751));

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationCriteria_DisplayOrder",
                table: "EvaluationCriteria",
                column: "DisplayOrder");
        }
    }
}

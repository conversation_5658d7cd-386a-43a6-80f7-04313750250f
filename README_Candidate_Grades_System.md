# نظام عرض درجات المرشحين

## نظرة عامة

تم إنشاء نظام متكامل لعرض درجات المرشحين مع تفاصيل المقييمين، متاح فقط للمستخدمين المصرح لهم:
- **المدير** - يمكنه رؤية جميع درجات المرشحين
- **رئيس اللجنة** - يمكنه رؤية درجات المرشحين في لجنته فقط
- **منسق اللجنة** - يمكنه رؤية درجات المرشحين في لجنته فقط

## الميزات الرئيسية

### 1. عرض قائمة درجات المرشحين
- **البحث والفلترة**: البحث بالاسم، رقم الخدمة، اللجنة
- **فلترة حسب اللجنة**: عرض المرشحين في لجنة محددة
- **فلترة حسب الحالة**: في الانتظار، قيد التقييم، مكتمل
- **ترقيم الصفحات**: عرض النتائج بصفحات
- **عرض مرئي للدرجات**: أشرطة تقدم ملونة

### 2. تفاصيل درجات مرشح معين
- **معلومات المرشح**: الاسم، الرتبة، الفئة، اللجنة
- **الدرجة الإجمالية**: مع متوسط الدرجات
- **تفاصيل المقيمين**: اسم كل مقيم، رتبته، دوره في اللجنة، درجته
- **تفاصيل المعايير**: درجات كل معيار لكل مقيم
- **الجدول الزمني**: تواريخ بدء وانتهاء التقييم

### 3. طباعة التقارير
- **تنسيق مناسب للطباعة**: مع فواصل صفحات
- **شعار المؤسسة**: قوات سلاح الجو الملكي السعودي
- **توقيعات**: رئيس اللجنة، منسق اللجنة، المدير
- **معلومات شاملة**: جميع التفاصيل في تقرير واحد

## الملفات المضافة

### 1. ViewModels
```csharp
// ViewModels/CandidateEvaluationViewModels.cs
- CandidateGradesViewModel
- EvaluatorScoreViewModel  
- CriteriaScoreViewModel
- CandidateGradesListViewModel
- CandidateGradesFilterViewModel
```

### 2. Controller
```csharp
// Controllers/CandidateGradesController.cs
- Index() - عرض قائمة درجات المرشحين
- Details(int id) - عرض تفاصيل مرشح معين
- Print(int id) - طباعة تقرير مرشح معين
```

### 3. Views
```
Views/CandidateGrades/
├── Index.cshtml - قائمة درجات المرشحين
├── Details.cshtml - تفاصيل مرشح معين
└── Print.cshtml - صفحة الطباعة
```

## نظام الصلاحيات

### التحقق من الصلاحيات
```csharp
private async Task<bool> CanUserViewGrades(User user)
{
    // المدير يمكنه رؤية جميع الدرجات
    if (user.IsAdmin)
        return true;

    // التحقق من دور المستخدم في اللجان
    var committeeRoles = await _context.CommitteeMembers
        .Where(cm => cm.ServiceNumber == user.ServiceNumber && cm.IsActive)
        .Select(cm => cm.Role)
        .ToListAsync();

    return committeeRoles.Any(r => r == CommitteeMemberRole.رئيس_اللجنة || r == CommitteeMemberRole.منسق);
}
```

### تطبيق الصلاحيات على البيانات
```csharp
// تطبيق صلاحيات المستخدم على اللجان
if (!currentUser.IsAdmin)
{
    var userCommittees = await GetUserCommittees(currentUser);
    query = query.Where(ce => userCommittees.Contains(ce.CommitteeId));
}
```

## واجهة المستخدم

### 1. صفحة القائمة الرئيسية
- **رأس الصفحة**: عنوان، دور المستخدم، عدد المرشحين
- **فلاتر البحث**: نص، لجنة، حالة التقييم
- **جدول النتائج**: معلومات المرشح، اللجنة، الدرجات، الإجراءات
- **ترقيم الصفحات**: للتنقل بين النتائج

### 2. صفحة التفاصيل
- **معلومات المرشح**: كاملة مع الرتبة والفئة
- **الدرجة الإجمالية**: مع عرض بصري
- **بطاقات المقيمين**: كل مقيم في بطاقة منفصلة
- **جدول المعايير**: تفاصيل كل معيار لكل مقيم
- **الجدول الزمني**: تواريخ الأحداث

### 3. صفحة الطباعة
- **تنسيق احترافي**: مناسب للطباعة
- **شعار المؤسسة**: مع العنوان الرسمي
- **جداول منظمة**: مع فواصل صفحات
- **توقيعات**: للموافقة الرسمية

## الأمان والتحقق

### 1. التحقق من تسجيل الدخول
```csharp
[Authorize]
public class CandidateGradesController : Controller
```

### 2. التحقق من الصلاحيات
```csharp
var canViewGrades = await CanUserViewGrades(currentUser);
if (!canViewGrades)
{
    return RedirectToAction("AccessDenied", "Account");
}
```

### 3. التحقق من الوصول للجنة
```csharp
if (!currentUser.IsAdmin)
{
    var userCommittees = await GetUserCommittees(currentUser);
    if (!userCommittees.Contains(candidateEvaluation.CommitteeId))
    {
        return RedirectToAction("AccessDenied", "Account");
    }
}
```

## التكامل مع النظام الحالي

### 1. استخدام النماذج الموجودة
- `CandidateEvaluation` - التقييمات
- `IndividualEvaluation` - التقييمات الفردية
- `CommitteeMember` - أعضاء اللجان
- `User` - المستخدمين

### 2. إضافة الروابط في القائمة
```html
<!-- للمدير -->
<li class="nav-item">
    <a class="nav-link" href="@Url.Action("Index", "CandidateGrades")">
        <i class="fas fa-chart-line ms-2"></i>
        <span>درجات المرشحين</span>
    </a>
</li>

<!-- لمنسق اللجنة ورئيس اللجنة -->
<li class="nav-item">
    <a class="nav-link" href="@Url.Action("Index", "CandidateGrades")">
        <i class="fas fa-chart-line ms-2"></i>
        <span>درجات المرشحين</span>
    </a>
</li>
```

## الاستخدام

### 1. الوصول للنظام
1. تسجيل الدخول بحساب مصرح له
2. الانتقال إلى "درجات المرشحين" من القائمة
3. استخدام الفلاتر للبحث عن مرشح معين

### 2. عرض التفاصيل
1. الضغط على أيقونة "عرض التفاصيل" في القائمة
2. مراجعة جميع المعلومات والدرجات
3. الضغط على "طباعة" لإنشاء تقرير

### 3. الطباعة
1. الضغط على "طباعة" في صفحة التفاصيل
2. مراجعة التقرير في نافذة جديدة
3. استخدام زر "طباعة" في المتصفح

## الميزات المستقبلية

### 1. تحسينات مقترحة
- **تصدير Excel**: تصدير البيانات بصيغة Excel
- **رسوم بيانية**: عرض الإحصائيات برسوم بيانية
- **إشعارات**: إشعارات عند اكتمال التقييمات
- **مقارنة المرشحين**: مقارنة درجات عدة مرشحين

### 2. تحسينات تقنية
- **Caching**: تخزين مؤقت للبيانات المتكررة
- **Pagination**: تحسين ترقيم الصفحات
- **Search**: بحث متقدم مع اقتراحات
- **Export**: تصدير بصيغ مختلفة

## الدعم والصيانة

### 1. مراقبة الأخطاء
```csharp
_logger.LogError(ex, "خطأ في عرض درجات المرشحين");
```

### 2. التحقق من البيانات
```csharp
if (candidateEvaluation == null)
{
    return NotFound();
}
```

### 3. معالجة الاستثناءات
```csharp
try
{
    // الكود
}
catch (Exception ex)
{
    _logger.LogError(ex, "خطأ في جلب الإحصائيات");
    return Json(new { error = "خطأ في جلب الإحصائيات" });
}
```

---

**تم إنشاء هذا النظام بواسطة AI Assistant**  
**تاريخ الإنشاء**: ديسمبر 2024  
**الإصدار**: 1.0 
# RafoEvaluation System - Requirements Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Functional Requirements](#functional-requirements)
4. [Technical Requirements](#technical-requirements)
5. [Business Rules](#business-rules)
6. [Data Models](#data-models)
7. [Security Requirements](#security-requirements)
8. [User Interface Requirements](#user-interface-requirements)
9. [Performance Requirements](#performance-requirements)
10. [Deployment Requirements](#deployment-requirements)

## Project Overview

### Purpose
RafoEvaluation is a comprehensive candidate evaluation and management system designed for military personnel evaluation and promotion processes. The system manages different categories of candidates with varying requirements and provides role-based access control for administrators and evaluators.

### Target Users
- **Administrators**: Full system access for user management and system configuration
- **Evaluators**: Access to candidate evaluation and management features
- **Candidates**: Subject to evaluation through the system

## System Architecture

### Technology Stack
- **Framework**: ASP.NET Core 9.0 (MVC Pattern)
- **Database**: SQLite with Entity Framework Core 9.0.6
- **Authentication**: Cookie-based authentication with BCrypt password hashing
- **Frontend**: Bootstrap 5 with jQuery for enhanced user experience
- **Development**: .NET 9.0 with nullable reference types enabled

### Core Components
- **Controllers**: MVC controllers for each major entity
- **Models**: Entity models with data annotations
- **Services**: Business logic services (Authentication, etc.)
- **Views**: Razor views with Bootstrap styling
- **Data Access**: Entity Framework Core with SQLite

## Functional Requirements

### 1. Authentication & Authorization System

#### 1.1 User Authentication
- **REQ-AUTH-001**: Users must authenticate using service number and password
- **REQ-AUTH-002**: Passwords must be securely hashed using BCrypt
- **REQ-AUTH-003**: Session management with 30-day sliding expiration
- **REQ-AUTH-004**: Secure cookie configuration with HttpOnly and appropriate security policies

#### 1.2 Role-Based Access Control
- **REQ-AUTH-005**: Support for multiple user roles (Admin, Evaluator, etc.)
- **REQ-AUTH-006**: Role-based authorization for different system features
- **REQ-AUTH-007**: User-role assignment and management

### 2. Candidate Management System

#### 2.1 Candidate Registration
- **REQ-CAND-001**: Support for multiple candidate categories with different requirements
- **REQ-CAND-002**: Dynamic form fields based on selected category
- **REQ-CAND-003**: Validation rules specific to each category
- **REQ-CAND-004**: Required and optional fields based on category type

#### 2.2 Candidate Categories
The system supports the following candidate categories:

| Category Code | Category Name (Arabic) | English Description | Requirements |
|---------------|----------------------|-------------------|--------------|
| CAT-PLT | المرشحين الطيارين | Pilot Candidates | Basic data + contact information only |
| CAT-MUG | المرشحين الجامعيين العسكريين | Military University Graduates | Basic data + educational information |
| CAT-CUG | المرشحين الجامعيين المدنيين | Civilian University Graduates | Basic data + educational information |
| CAT-LSO | ضباط الخدمة المحدودة | Limited Service Officers | Basic data + military information (Service number required) |
| CAT-NCO | ضباط الصف (رقباء/عرفاء) | Non-Commissioned Officers | Basic data + military information (Service number required) |
| CAT-TCN | ضباط الصف الكلية التقنية العسكرية | Technical College NCOs | Basic data + military information (Service number required) |
| CAT-CNP | ضباط الصف المدنيين للترفيع | Civilian NCOs for Promotion | Basic data + military information (Service number required) |

#### 2.3 Candidate Data Fields
- **Basic Information**: Full Name, Service Number, National ID, Date of Birth
- **Category Information**: Category assignment with specific requirements
- **Rank Information**: Military rank assignment
- **Airbase Information**: Assignment to specific airbases
- **Department Information**: Department and job title
- **Educational Information**: University, Major, Graduation Year, Marks Grade (for applicable categories)
- **Military Information**: Service-specific data (for applicable categories)

### 3. Category Management System

#### 3.1 Category Operations
- **REQ-CAT-001**: Create, read, update, and delete categories
- **REQ-CAT-002**: Category code validation and uniqueness
- **REQ-CAT-003**: Category activation/deactivation
- **REQ-CAT-004**: Category description and metadata management

### 4. Airbase Management System

#### 4.1 Airbase Operations
- **REQ-AIR-001**: Create, read, update, and delete airbases
- **REQ-AIR-002**: Airbase name validation and uniqueness
- **REQ-AIR-003**: Candidate assignment to airbases
- **REQ-AIR-004**: Airbase statistics and reporting

### 5. Rank Management System

#### 5.1 Rank Operations
- **REQ-RANK-001**: Military rank hierarchy management
- **REQ-RANK-002**: Rank assignment to users and candidates
- **REQ-RANK-003**: Rank-based access control

### 6. Search and Filtering

#### 6.1 Advanced Search
- **REQ-SEARCH-001**: Search candidates by name, service number, national ID
- **REQ-SEARCH-002**: Filter by category, rank, and airbase
- **REQ-SEARCH-003**: Pagination support for large datasets
- **REQ-SEARCH-004**: Real-time search suggestions

## Technical Requirements

### 1. Development Environment
- **REQ-TECH-001**: .NET 9.0 SDK
- **REQ-TECH-002**: Visual Studio 2022 or later
- **REQ-TECH-003**: SQLite database support
- **REQ-TECH-004**: Entity Framework Core tools

### 2. Dependencies
```xml
<PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6" />
```

### 3. Database Requirements
- **REQ-DB-001**: SQLite database with Entity Framework Core
- **REQ-DB-002**: Automatic database creation and migration support
- **REQ-DB-003**: Data integrity constraints and relationships
- **REQ-DB-004**: Backup and recovery procedures

### 4. Code Quality Requirements
- **REQ-CODE-001**: Nullable reference types enabled
- **REQ-CODE-002**: Implicit usings enabled
- **REQ-CODE-003**: Comprehensive error handling and logging
- **REQ-CODE-004**: Input validation and sanitization

## Business Rules

### 1. Category-Specific Requirements

#### 1.1 Pilot Candidates (CAT-PLT)
- **RULE-CAT-PLT-001**: National ID is required
- **RULE-CAT-PLT-002**: Military service information is not required
- **RULE-CAT-PLT-003**: Educational information is not required
- **RULE-CAT-PLT-004**: Only basic personal information is mandatory

#### 1.2 University Graduate Categories (CAT-MUG, CAT-CUG)
- **RULE-CAT-UG-001**: National ID is required
- **RULE-CAT-UG-002**: Educational information is mandatory
- **RULE-CAT-UG-003**: University, major, graduation year, and marks grade are required
- **RULE-CAT-UG-004**: Military service information is not required

#### 1.3 Military Service Categories (CAT-LSO, CAT-NCO, CAT-TCN, CAT-CNP)
- **RULE-CAT-MIL-001**: Service number is mandatory
- **RULE-CAT-MIL-002**: National ID is optional
- **RULE-CAT-MIL-003**: Military service information is required
- **RULE-CAT-MIL-004**: Educational information is not required

### 2. Data Validation Rules
- **RULE-VAL-001**: Service numbers must be unique across the system
- **RULE-VAL-002**: National ID numbers must be unique (when required)
- **RULE-VAL-003**: Date of birth must be a valid date
- **RULE-VAL-004**: Graduation year must be between 1950 and current year
- **RULE-VAL-005**: Category codes must be unique and follow the defined format

### 3. User Management Rules
- **RULE-USER-001**: Users must have at least one role assigned
- **RULE-USER-002**: Service numbers must be unique for active users
- **RULE-USER-003**: Passwords must meet security requirements
- **RULE-USER-004**: Inactive users cannot authenticate

## Data Models

### 1. User Authentication Models
```csharp
User {
    UserId (Primary Key)
    ServiceNumber (Unique, Required)
    Password (Hashed, Required)
    RankId (Foreign Key)
    IsActive (Boolean)
    CreatedAt (DateTime)
    LastLoginAt (DateTime?)
}

Role {
    RoleId (Primary Key)
    RoleName (Required)
    Description
}

UserRole {
    UserId (Foreign Key)
    RoleId (Foreign Key)
    Composite Primary Key
}

Rank {
    RankId (Primary Key)
    RankName (Required)
    RankLevel (Optional)
}
```

### 2. Business Models
```csharp
Candidate {
    CandidateId (Primary Key)
    FullName (Required)
    CategoryId (Foreign Key, Required)
    ServiceNumber (Required)
    NationalIdNumber (Required/Optional based on category)
    RankId (Foreign Key, Required)
    AirbaseId (Foreign Key, Required)
    Department (Optional)
    Major (Optional)
    University (Optional)
    GraduationYear (Optional)
    MarksGrade (Optional)
    DateOfBirth (Required)
    JobTitle (Optional)
    IsActive (Boolean)
    CreatedAt (DateTime)
    UpdatedAt (DateTime?)
}

Category {
    CategoryId (Primary Key)
    CategoryName (Required)
    CategoryCode (Required, Unique)
    Description (Optional)
    IsActive (Boolean)
    CreatedAt (DateTime)
    UpdatedAt (DateTime?)
}

Airbase {
    AirbaseId (Primary Key)
    AirbaseName (Required, Unique)
    IsActive (Boolean)
    CreatedAt (DateTime)
    UpdatedAt (DateTime?)
}
```

## Security Requirements

### 1. Authentication Security
- **REQ-SEC-001**: BCrypt password hashing with appropriate cost factor
- **REQ-SEC-002**: Secure session management with sliding expiration
- **REQ-SEC-003**: Protection against brute force attacks
- **REQ-SEC-004**: Secure cookie configuration

### 2. Authorization Security
- **REQ-SEC-005**: Role-based access control implementation
- **REQ-SEC-006**: Principle of least privilege enforcement
- **REQ-SEC-007**: Secure routing and controller access

### 3. Data Security
- **REQ-SEC-008**: Input validation and sanitization
- **REQ-SEC-009**: SQL injection prevention through Entity Framework
- **REQ-SEC-010**: XSS protection through proper output encoding
- **REQ-SEC-011**: CSRF protection through anti-forgery tokens

## User Interface Requirements

### 1. General UI Requirements
- **REQ-UI-001**: Responsive design using Bootstrap 5
- **REQ-UI-002**: Arabic language support for category names and descriptions
- **REQ-UI-003**: Consistent navigation and layout
- **REQ-UI-004**: Accessible design following WCAG guidelines

### 2. Form Requirements
- **REQ-UI-005**: Dynamic form fields based on category selection
- **REQ-UI-006**: Real-time validation feedback
- **REQ-UI-007**: Clear error messages and validation indicators
- **REQ-UI-008**: Required field indicators

### 3. Data Display Requirements
- **REQ-UI-009**: Paginated data tables
- **REQ-UI-010**: Search and filter functionality
- **REQ-UI-011**: Sortable columns
- **REQ-UI-012**: Export capabilities (future enhancement)

## Performance Requirements

### 1. Response Time
- **REQ-PERF-001**: Page load times under 3 seconds
- **REQ-PERF-002**: Search results returned within 1 second
- **REQ-PERF-003**: Form submission processing under 2 seconds

### 2. Scalability
- **REQ-PERF-004**: Support for up to 10,000 candidates
- **REQ-PERF-005**: Support for up to 100 concurrent users
- **REQ-PERF-006**: Efficient database queries with proper indexing

### 3. Resource Usage
- **REQ-PERF-007**: Memory usage optimization
- **REQ-PERF-008**: Database connection pooling
- **REQ-PERF-009**: Static file caching

## Deployment Requirements

### 1. Environment Requirements
- **REQ-DEP-001**: .NET 9.0 Runtime
- **REQ-DEP-002**: SQLite database support
- **REQ-DEP-003**: HTTPS support for production
- **REQ-DEP-004**: Proper file permissions

### 2. Configuration Requirements
- **REQ-DEP-005**: Environment-specific configuration files
- **REQ-DEP-006**: Secure connection string management
- **REQ-DEP-007**: Logging configuration
- **REQ-DEP-008**: Error handling configuration

### 3. Maintenance Requirements
- **REQ-DEP-009**: Database backup procedures
- **REQ-DEP-010**: Application monitoring and logging
- **REQ-DEP-011**: Update and patch management
- **REQ-DEP-012**: Disaster recovery procedures

## Future Enhancements

### 1. Planned Features
- **ENH-001**: Advanced reporting and analytics
- **ENH-002**: Email notifications and alerts
- **ENH-003**: Document upload and management
- **ENH-004**: API endpoints for external integrations
- **ENH-005**: Mobile-responsive web application
- **ENH-006**: Multi-language support (English/Arabic)

### 2. Technical Improvements
- **ENH-007**: Migration to SQL Server for larger deployments
- **ENH-008**: Implementation of caching layer
- **ENH-009**: Advanced search with full-text indexing
- **ENH-010**: Real-time updates using SignalR

---

**Document Version**: 1.0  
**Last Updated**: December 2024  
**Maintained By**: Development Team  
**Review Cycle**: Quarterly 
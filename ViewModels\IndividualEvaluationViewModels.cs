using RafoEvaluation.Models;
using RafoEvaluation.Models.Auth;
using RafoEvaluation.Extensions;
using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    public class IndividualEvaluationViewModel
    {
        public int IndividualEvaluationId { get; set; }
        public int CandidateEvaluationId { get; set; }
        public int EvaluatorId { get; set; }
        
        [Display(Name = "اسم المقيم")]
        public string EvaluatorName { get; set; } = string.Empty;
        
        [Display(Name = "الرتبة")]
        public string EvaluatorRank { get; set; } = string.Empty;
        
        [Display(Name = "الدرجة الإجمالية")]
        public decimal TotalScore { get; set; }
        
        [Display(Name = "ملاحظات المقيم")]
        public string? EvaluatorNotes { get; set; }
        
        [Display(Name = "حضر التقييم")]
        public bool IsPresent { get; set; }
        
        [Display(Name = "تاريخ التقييم")]
        public DateTime EvaluatedAt { get; set; }
        
        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }
    }

    public class IndividualEvaluationCreateViewModel
    {
        [Required(ErrorMessage = "معرف التقييم مطلوب")]
        public int CandidateEvaluationId { get; set; }
        
        [Required(ErrorMessage = "معرف المقيم مطلوب")]
        public int EvaluatorId { get; set; }
        
        [Required(ErrorMessage = "الدرجة الإجمالية مطلوبة")]
        [Range(0, 100, ErrorMessage = "الدرجة الإجمالية يجب أن تكون بين 0 و 100")]
        [Display(Name = "الدرجة الإجمالية")]
        public decimal TotalScore { get; set; }
        
        [Display(Name = "ملاحظات المقيم")]
        [StringLength(2000, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 2000 حرف")]
        public string? EvaluatorNotes { get; set; }
        
        [Display(Name = "حضر التقييم")]
        public bool IsPresent { get; set; } = true;
    }

    public class IndividualEvaluationEditViewModel
    {
        public int IndividualEvaluationId { get; set; }
        
        [Required(ErrorMessage = "الدرجة الإجمالية مطلوبة")]
        [Range(0, 100, ErrorMessage = "الدرجة الإجمالية يجب أن تكون بين 0 و 100")]
        [Display(Name = "الدرجة الإجمالية")]
        public decimal TotalScore { get; set; }
        
        [Display(Name = "ملاحظات المقيم")]
        [StringLength(2000, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 2000 حرف")]
        public string? EvaluatorNotes { get; set; }
        
        [Display(Name = "حضر التقييم")]
        public bool IsPresent { get; set; }
    }

    public class MultiEvaluatorCommitteeSessionViewModel
    {
        public int CommitteeId { get; set; }
        public string CommitteeName { get; set; } = string.Empty;
        public int EvaluationFormId { get; set; }
        public string EvaluationFormTitle { get; set; } = string.Empty;
        public int TotalMaxScore { get; set; }
        
        public List<MultiEvaluatorCandidateSessionViewModel> Candidates { get; set; } = new();
        public List<MultiEvaluatorCommitteeMemberViewModel> CommitteeMembers { get; set; } = new();
        
        // إحصائيات الجلسة
        public int TotalCandidates { get; set; }
        public int EvaluatedCandidates { get; set; }
        public int PendingCandidates { get; set; }
        public decimal CompletionPercentage => TotalCandidates > 0 ? (EvaluatedCandidates * 100.0m) / TotalCandidates : 0;
    }

    public class MultiEvaluatorCandidateSessionViewModel
    {
        public int CandidateId { get; set; }
        public int CandidateEvaluationId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string AirbaseName { get; set; } = string.Empty;
        public EvaluationStatus Status { get; set; }
        public string StatusDisplayName => Status.GetDisplayName();
        
        // معلومات التقييم المتعدد
        public decimal? AverageScore { get; set; }
        public int PresentEvaluatorCount { get; set; }
        public int AbsentEvaluatorCount { get; set; }
        public int TotalEvaluatorCount { get; set; }
        public decimal AttendancePercentage => TotalEvaluatorCount > 0 ? (PresentEvaluatorCount * 100.0m) / TotalEvaluatorCount : 0;
        
        // التقييمات الفردية
        public List<IndividualEvaluationViewModel> IndividualEvaluations { get; set; } = new();
        
        // حالات التقييم
        public bool IsEvaluated => Status == EvaluationStatus.Completed;
        public bool IsInProgress => Status == EvaluationStatus.InProgress;
        public bool IsPending => Status == EvaluationStatus.Pending;
        public bool HasEvaluations => IndividualEvaluations.Any();
    }

    public class MultiEvaluatorCommitteeMemberViewModel
    {
        public int UserId { get; set; }
        public string ServiceNumber { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public CommitteeMemberRole Role { get; set; }
        public string RoleDisplayName => Role.GetDisplayName();
        public bool IsActive { get; set; }
        
        // معلومات التقييم
        public bool HasEvaluated { get; set; }
        public DateTime? LastEvaluationDate { get; set; }
        public int EvaluationsCount { get; set; }
    }

    public class EvaluationSummaryViewModel
    {
        public int CandidateEvaluationId { get; set; }
        public string CandidateName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        
        // إحصائيات التقييم
        public decimal? AverageScore { get; set; }
        public decimal? MaxScore { get; set; }
        public decimal? MinScore { get; set; }
        public decimal? ScoreVariance { get; set; }
        public decimal? StandardDeviation { get; set; }
        
        // معلومات الحضور
        public int PresentEvaluatorCount { get; set; }
        public int AbsentEvaluatorCount { get; set; }
        public int TotalEvaluatorCount { get; set; }
        public decimal AttendancePercentage => TotalEvaluatorCount > 0 ? (PresentEvaluatorCount * 100.0m) / TotalEvaluatorCount : 0;
        
        // التقييمات الفردية
        public List<IndividualEvaluationViewModel> IndividualEvaluations { get; set; } = new();
        
        // تحليل التقييمات
        public bool HasOutliers => StandardDeviation.HasValue && StandardDeviation.Value > 10;
        public bool IsConsistent => StandardDeviation.HasValue && StandardDeviation.Value <= 5;
        public string ConsistencyLevel => StandardDeviation switch
        {
            <= 3 => "متناسق جداً",
            <= 5 => "متناسق",
            <= 8 => "متوسط",
            <= 12 => "غير متناسق",
            _ => "متباين جداً"
        };
    }

    public class MultiEvaluatorEvaluationViewModel
    {
        public int CandidateEvaluationId { get; set; }
        public int CandidateId { get; set; }
        public int CommitteeId { get; set; }
        public string CandidateName { get; set; } = string.Empty;
        public string ServiceNumber { get; set; } = string.Empty;
        public string RankName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string AirbaseName { get; set; } = string.Empty;
        public int EvaluationFormId { get; set; }
        public string EvaluationFormTitle { get; set; } = string.Empty;
        public string CommitteeName { get; set; } = string.Empty;
        public EvaluationStatus Status { get; set; }
        public string StatusDisplayName => Status.GetDisplayName();
        
        // النتائج النهائية
        public decimal? AverageScore { get; set; }
        public decimal? TotalScore { get; set; }
        public int PresentEvaluatorCount { get; set; }
        public int AbsentEvaluatorCount { get; set; }
        public int TotalEvaluatorCount { get; set; }
        public decimal AttendancePercentage => TotalEvaluatorCount > 0 ? (PresentEvaluatorCount * 100.0m) / TotalEvaluatorCount : 0;
        
        // التواريخ
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? GeneralNotes { get; set; }
        
        // التقييمات الفردية
        public List<IndividualEvaluationViewModel> IndividualEvaluations { get; set; } = new();
        
        // إحصائيات إضافية
        public decimal? MaxIndividualScore { get; set; }
        public decimal? MinIndividualScore { get; set; }
        public decimal? ScoreRange => MaxIndividualScore.HasValue && MinIndividualScore.HasValue ? 
            MaxIndividualScore.Value - MinIndividualScore.Value : null;
    }

    public class IndividualEvaluationDetailsViewModel
    {
        public int IndividualEvaluationId { get; set; }
        public int CandidateEvaluationId { get; set; }
        public int EvaluatorId { get; set; }
        
        // معلومات المقيم
        [Display(Name = "اسم المقيم")]
        public string EvaluatorName { get; set; } = string.Empty;
        
        [Display(Name = "الرتبة")]
        public string EvaluatorRank { get; set; } = string.Empty;
        
        [Display(Name = "رقم الخدمة")]
        public string EvaluatorServiceNumber { get; set; } = string.Empty;
        
        [Display(Name = "الدرجة الإجمالية")]
        public decimal TotalScore { get; set; }
        
        [Display(Name = "ملاحظات المقيم")]
        public string? EvaluatorNotes { get; set; }
        
        [Display(Name = "حضر التقييم")]
        public bool IsPresent { get; set; }
        
        [Display(Name = "تاريخ التقييم")]
        public DateTime EvaluatedAt { get; set; }
        
        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }
        
        // معلومات المرشح
        [Display(Name = "اسم المرشح")]
        public string CandidateName { get; set; } = string.Empty;
        
        [Display(Name = "رقم الخدمة")]
        public string CandidateServiceNumber { get; set; } = string.Empty;
        
        [Display(Name = "الرتبة")]
        public string CandidateRank { get; set; } = string.Empty;
        
        [Display(Name = "الفئة")]
        public string CandidateCategory { get; set; } = string.Empty;
        
        // معلومات اللجنة
        [Display(Name = "اسم اللجنة")]
        public string CommitteeName { get; set; } = string.Empty;
        
        [Display(Name = "نموذج التقييم")]
        public string EvaluationFormTitle { get; set; } = string.Empty;
        
        // معايير التقييم
        public List<CriteriaScoreViewModel> CriteriaScores { get; set; } = new();
    }
} 
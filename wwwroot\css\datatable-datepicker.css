/* DataTable Date Picker - Simplified Styles */
.datatable-datepicker-input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    direction: ltr;
    text-align: left;
}

.datatable-datepicker-input:focus {
    color: #212529;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.datatable-datepicker-input:hover {
    border-color: #86b7fe;
}

/* Validation Styles */
.datatable-datepicker-input.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='m5.8 4.6 1.4 1.4L5.8 7.4 4.4 6l1.4-1.4z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.datatable-datepicker-input.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 2.54 2.54 3.94-3.94.94.94-4.88 4.88z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Invalid Feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* Age Display */
.age-display {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: #d1e7dd;
    border: 1px solid #badbcc;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #0f5132;
    text-align: center;
}

.age-display.valid {
    background-color: #d1e7dd;
    border-color: #badbcc;
    color: #0f5132;
}

.age-display.invalid {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
}

/* Responsive Design */
@media (max-width: 768px) {
    .datatable-datepicker-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .age-display {
        font-size: 0.8rem;
        padding: 0.375rem;
    }
}
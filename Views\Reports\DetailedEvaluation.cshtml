@model RafoEvaluation.ViewModels.DetailedEvaluationReportViewModel
@{
    ViewData["Title"] = "تقرير تقييم مفصل";
    ViewData["ActivePage"] = "Reports";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title">
                        <i class="fas fa-file-alt me-2 text-dark"></i>
                        نتائج تقييم اللجنة الرئيسية
                    </h6>
                    <div>
                        <a href="@Url.Action("PrintDetailedEvaluation", "Reports", new { candidateEvaluationId = Model.CandidateEvaluation.CandidateEvaluationId })" 
                           class="btn btn-primary btn-sm" target="_blank">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </a>
                        <a href="@Url.Action("CandidateGrades", "Reports")" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>
                            عودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات المرشح -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <th class="bg-light" style="width: 200px;">الرقم العسكري</th>
                                            <td>@Model.CandidateEvaluation.Candidate.ServiceNumber</td>
                                            <th class="bg-light" style="width: 200px;">الرتبة</th>
                                            <td>@(Model.CandidateEvaluation.Candidate.Rank?.RankName ?? "غير محدد")</td>
                                        </tr>
                                        <tr>
                                            <th class="bg-light">الاسم الثلاثي والقبيلة</th>
                                            <td colspan="3">@Model.CandidateEvaluation.Candidate.FullName</td>
                                        </tr>
                                        <tr>
                                            <th class="bg-light">الفئة</th>
                                            <td>@Model.CandidateEvaluation.Candidate.Category.CategoryName</td>
                                            <th class="bg-light">القاعدة الجوية</th>
                                            <td>@(Model.CandidateEvaluation.Candidate.Airbase?.AirbaseName ?? "غير محدد")</td>
                                        </tr>
                                        <tr>
                                            <th class="bg-light">اللجنة</th>
                                            <td>@Model.CandidateEvaluation.Committee.CommitteeName</td>
                                            <th class="bg-light">نموذج التقييم</th>
                                            <td>@Model.CandidateEvaluation.EvaluationForm.Title</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- جدول التقييم المفصل -->
                    <div class="row">
                        <div class="col-12">
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm">
                                    <thead class="table-dark">
                                        <tr>
                                            <th rowspan="2" class="text-center align-middle">ت</th>
                                            <th rowspan="2" class="text-center align-middle">العضو</th>
                                            <th rowspan="2" class="text-center align-middle">الدرجة المخصصة</th>
                                            @foreach (var item in Model.EvaluationFormItems)
                                            {
                                                <th class="text-center">@item.Criteria</th>
                                            }
                                            <th rowspan="2" class="text-center align-middle">المجموع</th>
                                        </tr>
                                        <tr>
                                            @foreach (var item in Model.EvaluationFormItems)
                                            {
                                                <th class="text-center">@item.MaxScore</th>
                                            }
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for (int i = 0; i < Model.IndividualEvaluations.Count; i++)
                                        {
                                            var evaluation = Model.IndividualEvaluations[i];
                                            var evaluatorTitle = GetEvaluatorTitle(evaluation.Evaluator.ServiceNumber, i + 1);
                                            
                                            <tr>
                                                <td class="text-center">@(i + 1)</td>
                                                <td>@evaluatorTitle<br><small class="text-muted">@evaluation.Evaluator.FullName</small></td>
                                                <td class="text-center">50</td>
                                                @foreach (var item in Model.EvaluationFormItems)
                                                {
                                                    var criteria = evaluation.CriteriaScores.FirstOrDefault(iec => iec.EvaluationFormItemId == item.EvaluationFormItemId);
                                                    var score = criteria?.Score ?? 0;
                                                    <td class="text-center">@score</td>
                                                }
                                                <td class="text-center fw-bold">@evaluation.TotalScore</td>
                                            </tr>
                                        }
                                        
                                        <!-- صف المجاميع -->
                                        <tr class="table-info">
                                            <td colspan="2" class="text-center fw-bold">المجموع</td>
                                            <td class="text-center fw-bold">@Model.TotalAllocatedScore</td>
                                            @foreach (var item in Model.EvaluationFormItems)
                                            {
                                                var totalScore = Model.IndividualEvaluations.Sum(ie => 
                                                    ie.CriteriaScores
                                                        .Where(iec => iec.EvaluationFormItemId == item.EvaluationFormItemId)
                                                        .Sum(iec => iec.Score));
                                                <td class="text-center fw-bold">@totalScore</td>
                                            }
                                            <td class="text-center fw-bold">@Model.TotalObtainedScore</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- الملخص والاعتماد -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">الملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="border p-3" style="min-height: 100px;">
                                        <!-- مساحة للملاحظات -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">الملخص</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <th>الدرجة المحصلة من (@Model.TotalAllocatedScore)</th>
                                            <td class="fw-bold">@Model.TotalObtainedScore</td>
                                        </tr>
                                        <tr>
                                            <th>النسبة المئوية (100%)</th>
                                            <td class="fw-bold">%@Model.Percentage.ToString("F2")</td>
                                        </tr>
                                        <tr>
                                            <th>ثقل جانب التقييم (30%)</th>
                                            <td class="fw-bold">@Model.WeightedScore.ToString("F2")</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اعتماد رئيس اللجنة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">اعتماد رئيس اللجنة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="border-bottom border-dark pb-2" style="min-height: 60px;">
                                                <!-- مساحة للتوقيع -->
                                            </div>
                                            <small class="text-muted">التوقيع</small>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="border-bottom border-dark pb-2" style="min-height: 60px;">
                                                <!-- مساحة للختم -->
                                            </div>
                                            <small class="text-muted">الختم</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetArabicNumber(int number)
    {
        var arabicNumbers = new[] { "الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس", "السابع", "الثامن", "التاسع", "العاشر" };
        return number <= arabicNumbers.Length ? arabicNumbers[number - 1] : number.ToString();
    }

    private string GetEvaluatorTitle(string serviceNumber, int index)
    {
        // في التقرير المفصل، نستخدم الترتيب الجديد من Controller
        // حيث تم ترتيب التقييمات حسب MemberNumber
        return index == 1 ? "رئيس اللجنة (العضو الأول)" : $"العضو {GetArabicNumber(index)}";
    }
} 
@model RafoEvaluation.ViewModels.CategoryWeightsManagementViewModel
@{
    ViewData["Title"] = "إدارة أوزان الفئات (%)";
    ViewData["ActivePage"] = "Settings";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-percentage me-2"></i>
                        إدارة أوزان الفئات (%)
                    </h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Index", "Category")" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للفئات
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    

                    @if (!Model.Categories.Any())
                    {
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد فئات متاحة لإدارة الأوزان.
                        </div>
                    }
                    else
                    {
                        <form asp-action="UpdateWeights" method="post">
                            @Html.AntiForgeryToken()
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-responsive-md">
                                    <thead class="table-secondary">
                                        <tr>
                                          
                                            <th>رمز الفئة</th>
                                            <th>اسم الفئة</th>
                                            <th>الوصف</th>
                                            <th>الوزن الحالي (%)</th>
                                            <th>الوزن الجديد (%)</th>
                                        </tr>
                                    </thead>
                                    <tbody style="font-size: 12px;">
                                        @for (int i = 0; i < Model.Categories.Count; i++)
                                        {
                                            var category = Model.Categories[i];
                                            <tr>
                                                <td>
                                                    <span class="badge bg-info">@category.CategoryCode</span>
                                                </td>
                                                <td>
                                                    <strong>@category.CategoryName</strong>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(category.Description))
                                                    {
                                                        <small class="text-muted">@category.Description</small>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">@category.CurrentWeight.ToString("F0")</span>
                                                </td>
                                                <td>
                                                    <input type="hidden" name="[@i].CategoryId" value="@category.CategoryId" />
                                                    <input type="hidden" name="[@i].CategoryName" value="@category.CategoryName" />
                                                    <div class="input-group input-group-sm">
                                                        <input type="number" 
                                                               name="[@i].Weight" 
                                                               value="@category.CurrentWeight" 
                                                               min="0" 
                                                               max="100" 
                                                               step="0.1" 
                                                               class="form-control text-center"
                                                               style="max-width: 100px; font-size: 12px;" />
                                                        <span class="input-group-text">%</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التغييرات
                                    </button>
                                    <a href="@Url.Action("Index", "Category")" class="btn btn-secondary btn-lg ms-2">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // التحقق من صحة القيم
            $('input[type="number"]').on('input', function() {
                var value = parseFloat($(this).val());
                if (value < 0) {
                    $(this).val(0);
                } else if (value > 100) {
                    $(this).val(100);
                }
            });

            // تأكيد الحفظ
            $('form').on('submit', function(e) {
                // لا حاجة للتحقق من مجموع الأوزان لأن كل فئة مستقلة
                return true;
            });
        });
    </script>
} 
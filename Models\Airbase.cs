using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RafoEvaluation.Models
{
    public class Airbase
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int AirbaseId { get; set; }

        [Required]
        [StringLength(100)]
        public string AirbaseName { get; set; } = string.Empty;

        // Navigation Properties
        public virtual ICollection<Candidate> Candidates { get; set; } = new List<Candidate>();
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRanksArabicNames : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 658, DateTimeKind.Utc).AddTicks(9651), "أعلى رتبة في سلاح الجو", "مشير" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(626), "رتبة عالية في سلاح الجو", "فريق" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(628), "رتبة ضابط عالي", "عميد" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(629), "رتبة ضابط عالي", "عقيد" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(630), "رتبة ضابط عالي", "مقدم" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(631), "رتبة ضابط", "رائد" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(632), "رتبة ضابط", "نقيب" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(633), "رتبة ضابط صغير", "ملازم أول" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(633), "رتبة ضابط صغير", "ملازم" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(634), "رتبة وكيل ضابط", "وكيل ضابط" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(635), "رتبة ضابط صف", "رقيب أول" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(636), "رتبة ضابط صف", "رقيب" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(637), "رتبة ضابط صف صغير", "عريف" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(638), "رتبة جندي", "جندي أول" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(638), "رتبة جندي", "جندي" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(639), "بدون رتبة", "---" });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(5609));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6115));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6117));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6118));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6119));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 19, 43, 27, 659, DateTimeKind.Utc).AddTicks(6120));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(2849), "Senior Air Officer", "Air Marshal" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3788), "Senior Air Officer", "Air Vice Marshal" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3791), "Senior Air Officer", "Air Commodore" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3792), "Senior Officer", "Group Captain" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3793), "Senior Officer", "Wing Commander" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3794), "Officer", "Squadron Leader" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3795), "Officer", "Flight Lieutenant" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3796), "Junior Officer", "Flying Officer" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3798), "Junior Officer", "Pilot Officer" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3799), "Senior NCO", "Warrant Officer" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3800), "NCO", "Flight Sergeant" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3801), "NCO", "Sergeant" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3802), "Junior NCO", "Corporal" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3803), "Enlisted", "Senior Aircraftman" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3804), "Enlisted", "Leading Aircraftman" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                columns: new[] { "CreatedAt", "Description", "RankName" },
                values: new object[] { new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(3805), "Enlisted", "Aircraftman" });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 34, 17, 472, DateTimeKind.Utc).AddTicks(9586));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 34, 17, 473, DateTimeKind.Utc).AddTicks(206));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 34, 17, 473, DateTimeKind.Utc).AddTicks(208));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 34, 17, 473, DateTimeKind.Utc).AddTicks(209));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 34, 17, 473, DateTimeKind.Utc).AddTicks(231));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 18, 34, 17, 473, DateTimeKind.Utc).AddTicks(232));
        }
    }
}

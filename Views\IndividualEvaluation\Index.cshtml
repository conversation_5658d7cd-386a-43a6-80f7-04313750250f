@model RafoEvaluation.ViewModels.MultiEvaluatorEvaluationViewModel
@{
    ViewData["Title"] = "التقييمات الفردية";
    var committeeMembers = ViewBag.CommitteeMembers as List<RafoEvaluation.ViewModels.MultiEvaluatorCommitteeMemberViewModel>;
    var standardDeviation = ViewBag.StandardDeviation as decimal?;
    var totalMaxScore = ViewBag.TotalMaxScore as int? ?? 100;
}

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-users"></i>
                        التقييمات الفردية - @Model.CandidateName
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>معلومات المرشح</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>@Model.CandidateName</td>
                                </tr>
                                <tr>
                                    <td><strong>الرقم العسكري:</strong></td>
                                    <td>@Model.ServiceNumber</td>
                                </tr>
                                <tr>
                                    <td><strong>الرتبة:</strong></td>
                                    <td>@Model.RankName</td>
                                </tr>
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>@Model.CategoryName</td>
                                </tr>
                                <tr>
                                    <td><strong>القاعدة الجوية:</strong></td>
                                    <td>@Model.AirbaseName</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>معلومات التقييم</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>اللجنة:</strong></td>
                                    <td>@Model.CommitteeName</td>
                                </tr>
                                <tr>
                                    <td><strong>نموذج التقييم:</strong></td>
                                    <td>@Model.EvaluationFormTitle</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        <span class="badge @(Model.Status == RafoEvaluation.Models.EvaluationStatus.Completed ? "bg-success" : 
                                                          Model.Status == RafoEvaluation.Models.EvaluationStatus.InProgress ? "bg-warning" : "bg-secondary")">
                                            @Model.StatusDisplayName
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>الدرجة القصوى:</strong></td>
                                    <td>@totalMaxScore</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                                                    <h3>@(Model.AverageScore?.ToString("F2") ?? "0")</h3>
                    <p class="mb-0">متوسط الدرجات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>@Model.PresentEvaluatorCount / @Model.TotalEvaluatorCount</h3>
                    <p class="mb-0">الحضور</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                                                    <h3>@(Model.AttendancePercentage.ToString("F2"))%</h3>
                    <p class="mb-0">نسبة الحضور</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                                                    <h3>@(Model.ScoreRange?.ToString("F2") ?? "0")</h3>
                    <p class="mb-0">مدى الدرجات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Individual Evaluations -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i>
                        التقييمات الفردية (@Model.IndividualEvaluations.Count)
                    </h5>
                    @if (Model.Status != RafoEvaluation.Models.EvaluationStatus.Completed)
                    {
                        <a href="@Url.Action("Create", new { candidateEvaluationId = Model.CandidateEvaluationId })" 
                           class="btn btn-success">
                            <i class="fas fa-plus"></i>
                            إضافة تقييم
                        </a>
                    }
                </div>
                <div class="card-body">
                    @if (Model.IndividualEvaluations.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المقيم</th>
                                        <th>الرتبة</th>
                                        <th>الدرجة</th>
                                        <th>النسبة المئوية</th>
                                        <th>الحضور</th>
                                        <th>التاريخ</th>
                                        <th>الملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var evaluation in Model.IndividualEvaluations)
                                    {
                                        var percentage = (evaluation.TotalScore / totalMaxScore) * 100;
                                        <tr>
                                            <td>
                                                <strong>@evaluation.EvaluatorName</strong>
                                            </td>
                                            <td>@evaluation.EvaluatorRank</td>
                                            <td>
                                                <span class="badge @(percentage >= 85 ? "bg-success" : 
                                                                  percentage >= 70 ? "bg-warning" : "bg-danger")">
                                                    @evaluation.TotalScore.ToString("F1")
                                                </span>
                                            </td>
                                            <td>@percentage.ToString("F2")%</td>
                                            <td>
                                                @if (evaluation.IsPresent)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check"></i>
                                                        حاضر
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times"></i>
                                                        غائب
                                                    </span>
                                                }
                                            </td>
                                            <td>@evaluation.EvaluatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(evaluation.EvaluatorNotes))
                                                {
                                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                                            data-bs-toggle="tooltip" data-bs-placement="top" 
                                                            title="@evaluation.EvaluatorNotes">
                                                        <i class="fas fa-eye"></i>
                                                        عرض الملاحظات
                                                    </button>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">لا توجد ملاحظات</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Edit", new { id = evaluation.IndividualEvaluationId })" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(@evaluation.IndividualEvaluationId)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Analysis Section -->
                        @if (standardDeviation.HasValue)
                        {
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-chart-line"></i>
                                                تحليل التقييمات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <strong>الانحراف المعياري:</strong> @standardDeviation.Value.ToString("F2")
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>أعلى درجة:</strong> @Model.MaxIndividualScore?.ToString("F1")
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>أقل درجة:</strong> @Model.MinIndividualScore?.ToString("F1")
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>مدى الدرجات:</strong> @Model.ScoreRange?.ToString("F1")
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تقييمات فردية بعد</h5>
                            <p class="text-muted">قم بإضافة أول تقييم لهذا المرشح</p>
                            <a href="@Url.Action("Create", new { candidateEvaluationId = Model.CandidateEvaluationId })" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                إضافة تقييم
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Committee Members -->
    @if (committeeMembers != null && committeeMembers.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i>
                            أعضاء اللجنة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var member in committeeMembers)
                            {
                                var hasEvaluated = Model.IndividualEvaluations.Any(ie => ie.EvaluatorId == member.UserId);
                                <div class="col-md-4 mb-3">
                                    <div class="card @(hasEvaluated ? "border-success" : "border-warning")">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">@member.FullName</h6>
                                                    <small class="text-muted">
                                                        @member.RankName - @member.RoleDisplayName
                                                    </small>
                                                </div>
                                                <div>
                                                    @if (hasEvaluated)
                                                    {
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check"></i>
                                                            تم التقييم
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-warning">
                                                            <i class="fas fa-clock"></i>
                                                            لم يقيم بعد
                                                        </span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="@Url.Action("EvaluationSession", "CommitteeEvaluation", new { committeeId = Model.CommitteeId, evaluationFormId = Model.EvaluationFormId })" 
               class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                العودة لجلسة التقييم
            </a>
            <a href="@Url.Action("ViewResults", "CommitteeEvaluation", new { committeeId = Model.CommitteeId, evaluationFormId = Model.EvaluationFormId })" 
               class="btn btn-info">
                <i class="fas fa-chart-bar"></i>
                عرض النتائج
            </a>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا التقييم؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id) {
            var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            var form = document.getElementById('deleteForm');
            form.action = '@Url.Action("Delete")/' + id;
            modal.show();
        }

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>
} 
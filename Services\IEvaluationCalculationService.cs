using RafoEvaluation.Models;

namespace RafoEvaluation.Services
{
    public interface IEvaluationCalculationService
    {
        /// <summary>
        /// حساب المتوسط من الأعضاء النشطين فقط
        /// </summary>
        Task<decimal?> CalculateAverageFromActiveMembers(int candidateEvaluationId);
        
        /// <summary>
        /// الحصول على عدد الأعضاء النشطين في اللجنة
        /// </summary>
        Task<int> GetActiveMemberCount(int committeeId);
        
        /// <summary>
        /// تحديث إحصائيات التقييم للمرشح
        /// </summary>
        Task UpdateCandidateEvaluationStatistics(int candidateEvaluationId);
        
        /// <summary>
        /// التحقق من أن العضو نشط ويمكنه التقييم
        /// </summary>
        Task<bool> IsActiveMemberEvaluator(int userId, int committeeId);
    }
}

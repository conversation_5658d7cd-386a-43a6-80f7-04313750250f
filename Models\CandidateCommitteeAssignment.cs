using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RafoEvaluation.Models
{
    public class CandidateCommitteeAssignment
    {
        [Key]
        public int AssignmentId { get; set; }

        [Required(ErrorMessage = "المرشح مطلوب")]
        [Display(Name = "المرشح")]
        public int CandidateId { get; set; }

        [Required(ErrorMessage = "اللجنة مطلوبة")]
        [Display(Name = "اللجنة")]
        public int CommitteeId { get; set; }

        [Required(ErrorMessage = "نموذج التقييم مطلوب")]
        [Display(Name = "نموذج التقييم")]
        public int EvaluationFormId { get; set; }

        [Display(Name = "تاريخ الإسناد")]
        public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تم الإسناد بواسطة")]
        [StringLength(50, ErrorMessage = "اسم المستخدم لا يمكن أن يتجاوز 50 حرف")]
        public string AssignedBy { get; set; } = string.Empty;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CandidateId")]
        public virtual Candidate Candidate { get; set; } = null!;

        [ForeignKey("CommitteeId")]
        public virtual Committee Committee { get; set; } = null!;

        [ForeignKey("EvaluationFormId")]
        public virtual EvaluationForm EvaluationForm { get; set; } = null!;
    }
} 
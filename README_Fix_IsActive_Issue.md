# إصلاح مشكلة عمود IsActive

## المشكلة
كانت هناك مشكلة في صفحة `PrintAllIndividualEvaluations` حيث يظهر خطأ:
```
Internal server error: Invalid column name 'IsActive'.
```

## السبب
جدول `IndividualEvaluationCriteria` لم يكن يحتوي على عمود `IsActive` بينما الكود يحاول استخدامه.

## الحل
تم إضافة عمود `IsActive` إلى جدول `IndividualEvaluationCriteria`:

### 1. تحديث النموذج
تم إضافة الخاصية التالية إلى `Models/IndividualEvaluationCriteria.cs`:
```csharp
[Display(Name = "نشط")]
public bool IsActive { get; set; } = true;
```

### 2. إنشاء Migration
تم إنشاء migration جديد: `20250101000003_AddIsActiveToIndividualEvaluationCriteria.cs`

### 3. تطبيق التحديث على قاعدة البيانات
تم تشغيل script PowerShell `apply_migration.ps1` الذي أضاف العمود إلى قاعدة البيانات.

## التحقق من التطبيق
تم التحقق من أن العمود تم إضافته بنجاح:
- Column Name: IsActive
- Data Type: bit
- Is Nullable: NO
- Default Value: ((1))

## النتيجة
الآن يمكن الوصول إلى صفحة `PrintAllIndividualEvaluations` بدون أخطاء.

## ملاحظات إضافية
- تم أيضاً تحديث أسماء الصلاحيات إلى العربية بنجاح
- تم إصلاح مشكلة الوصول لقائمة المستخدمين للمدير 
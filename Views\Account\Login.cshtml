@model RafoEvaluation.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة تسجيل الدخول - نظام تقييم سلاح الجو السلطاني العماني</title>
    <link href="~/lib/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/lib/font-awesome/css/all.min.css" rel="stylesheet" />
    <link href="~/css/fonts.css" rel="stylesheet">
    <style>
        body {
            background-color: #ffffff; /* White background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 80vh;
            margin: 0;
            font-family: 'Cairo', sans-serif;
        }
        * {
            font-family: 'Cairo', sans-serif;
        }
        .login-container {
            max-width: 400px;
            width: 80%;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for the card */
            background-color: #fff;
        }
        .form-label, .form-check-label {
            color: #000080; /* Dark blue for text */
            font-weight: bold;
        }
        .form-control {
            border-color: #000080; /* Dark blue border for inputs */
            border-radius: 6px;
        }
        .form-control:focus {
            border-color: #000080;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 128, 0.25);
        }
        .btn-primary {
            background-color: #000080; /* Dark blue button */
            border-color: #000080; /* Dark blue button border */
            border-radius: 6px;
            font-weight: bold;
        }
        .btn-primary:hover {
            background-color: #000066; /* Slightly darker blue on hover */
            border-color: #000066;
        }
        .logo-placeholder {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo-placeholder img {
            max-width: 150px; /* Adjust logo size as needed */
            height: auto;
        }
        .alert {
            border-radius: 6px;
            border: none;
        }
        .text-danger {
            color: #dc3545 !important;
        }
        .form-check-input:checked {
            background-color: #000080;
            border-color: #000080;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-placeholder">
            <img src="~/Images/rafo_logo.png" alt="شعار سلاح الجو السلطاني العماني">
        </div>
        <h2 class="text-center mb-4" style="color: #000080;">تسجيل الدخول</h2>
        
        @if (!ViewData.ModelState.IsValid)
        {
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>خطأ في تسجيل الدخول:</strong>
                <ul class="mb-0 mt-2">
                    @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                    {
                        <li>@error.ErrorMessage</li>
                    }
                </ul>
            </div>
        }
        
        <form asp-action="Login" method="post">
            <input type="hidden" name="returnUrl" value="@ViewData["ReturnUrl"]" />
            
            <div class="mb-3">
                <label asp-for="ServiceNumber" class="form-label">الرقم العسكري</label>
                <input asp-for="ServiceNumber" type="text" class="form-control" placeholder="أدخل رقمك العسكري" required autofocus>
                <span asp-validation-for="ServiceNumber" class="text-danger"></span>
            </div>
            
            <div class="mb-3">
                <label asp-for="Password" class="form-label">كلمة المرور</label>
                <input asp-for="Password" type="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
            
            <div class="mb-3 form-check">
                <input asp-for="RememberMe" type="checkbox" class="form-check-input">
                <label asp-for="RememberMe" class="form-check-label">تذكرني؟</label>
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </div>
        </form>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                <i class="fas fa-shield-alt me-1"></i>
                نظام آمن ومحمي
            </small>
        </div>
    </div>

    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

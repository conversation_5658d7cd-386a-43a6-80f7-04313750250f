﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class RemoveWeightFromEvaluationCriteria : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Weight",
                table: "EvaluationCriteria");

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(2906));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3557));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3559));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3559));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3560));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3561));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3562));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3563));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3564));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3565));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3565));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3566));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3567));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3568));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3569));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(3570));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8250));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8751));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8753));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8754));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8755));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 38, 46, 345, DateTimeKind.Utc).AddTicks(8756));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Weight",
                table: "EvaluationCriteria",
                type: "decimal(5,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "EvaluationCriteria",
                keyColumn: "CriteriaId",
                keyValue: 1,
                column: "Weight",
                value: 1.0m);

            migrationBuilder.UpdateData(
                table: "EvaluationCriteria",
                keyColumn: "CriteriaId",
                keyValue: 2,
                column: "Weight",
                value: 1.5m);

            migrationBuilder.UpdateData(
                table: "EvaluationCriteria",
                keyColumn: "CriteriaId",
                keyValue: 3,
                column: "Weight",
                value: 1.5m);

            migrationBuilder.UpdateData(
                table: "EvaluationCriteria",
                keyColumn: "CriteriaId",
                keyValue: 4,
                column: "Weight",
                value: 2.0m);

            migrationBuilder.UpdateData(
                table: "EvaluationCriteria",
                keyColumn: "CriteriaId",
                keyValue: 5,
                column: "Weight",
                value: 2.0m);

            migrationBuilder.UpdateData(
                table: "EvaluationCriteria",
                keyColumn: "CriteriaId",
                keyValue: 6,
                column: "Weight",
                value: 1.0m);

            migrationBuilder.UpdateData(
                table: "EvaluationCriteria",
                keyColumn: "CriteriaId",
                keyValue: 7,
                column: "Weight",
                value: 1.0m);

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8060));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8741));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8743));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8744));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8745));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8746));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8746));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8747));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8748));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8749));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8750));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8751));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8751));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8752));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8753));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 712, DateTimeKind.Utc).AddTicks(8764));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(4502));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5069));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5071));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5072));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5072));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 15, 16, 17, 18, 713, DateTimeKind.Utc).AddTicks(5073));
        }
    }
}

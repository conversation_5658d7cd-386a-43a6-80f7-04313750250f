@model RafoEvaluation.ViewModels.IndividualEvaluationDetailsViewModel
@{
    ViewData["Title"] = "تفاصيل التقييم الفردي";
    ViewData["ActivePage"] = "IndividualEvaluation";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="@Url.Action("Index", "CandidateGrades")">
                                    <i class="fas fa-chart-line me-1"></i>
                                    درجات المرشحين
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="@Url.Action("Details", "CandidateGrades", new { id = Model.CandidateEvaluationId })">
                                    تفاصيل المرشح
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">تفاصيل التقييم الفردي</li>
                        </ol>
                    </nav>
                    <h2 class="mb-0">
                        <i class="fas fa-user-check text-primary me-2"></i>
                        تفاصيل التقييم الفردي
                    </h2>
                </div>
                <div class="d-flex gap-2">
                    <a href="@Url.Action("Details", "CandidateGrades", new { id = Model.CandidateEvaluationId })" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        عودة لتفاصيل المرشح
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Evaluator Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        معلومات المقيم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">اسم المقيم:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <h5 class="mb-0">@Model.EvaluatorName</h5>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">الرتبة:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-info">@Model.EvaluatorRank</span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">رقم الخدمة:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.EvaluatorServiceNumber
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">الدرجة الإجمالية:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-fill me-2" style="height: 12px;">
                                            <div class="progress-bar bg-success" style="width: @(Model.TotalScore)%"></div>
                                        </div>
                                        <span class="fw-bold fs-5">@Model.TotalScore.ToString("F2")%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">الحضور:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-@(Model.IsPresent ? "success" : "warning")">
                                        <i class="fas fa-@(Model.IsPresent ? "check" : "times") me-1"></i>
                                        @(Model.IsPresent ? "حاضر" : "غائب")
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">تاريخ التقييم:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <strong>@Model.EvaluatedAt.ToString("dd/MM/yyyy HH:mm")</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Candidate Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-graduate me-2"></i>
                        معلومات المرشح
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">اسم المرشح:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <h5 class="mb-0">@Model.CandidateName</h5>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">رقم الخدمة:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.CandidateServiceNumber
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">الرتبة:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-secondary">@Model.CandidateRank</span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">الفئة:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-dark">@Model.CandidateCategory</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Committee Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        معلومات اللجنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">اسم اللجنة:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <strong>@Model.CommitteeName</strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">نموذج التقييم:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.EvaluationFormTitle
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Criteria Scores -->
    @if (Model.CriteriaScores.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            تفاصيل معايير التقييم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 25%;">المعيار</th>
                                        <th style="width: 35%;">الوصف</th>
                                        <th style="width: 15%;" class="text-center">الدرجة القصوى</th>
                                        <th style="width: 15%;" class="text-center">الدرجة الممنوحة</th>
                                        <th style="width: 10%;" class="text-center">النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var criteria in Model.CriteriaScores)
                                    {
                                        var percentage = (criteria.Score / criteria.MaxScore) * 100;
                                        <tr>
                                            <td>
                                                <strong>@criteria.CriteriaName</strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">@criteria.CriteriaDescription</small>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-secondary">@criteria.MaxScore</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="fw-bold">@criteria.Score.ToString("F1")</span>
                                            </td>
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center">
                                                    <span class="fw-bold text-@(percentage >= 80 ? "success" : percentage >= 60 ? "warning" : "danger")">
                                                        @percentage.ToString("F2")%
                                                    </span>
                                                    <div class="progress w-100" style="height: 4px;">
                                                        <div class="progress-bar bg-@(percentage >= 80 ? "success" : percentage >= 60 ? "warning" : "danger")" 
                                                             style="width: @percentage%"></div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        @if (!string.IsNullOrEmpty(criteria.Notes))
                                        {
                                            <tr class="table-light">
                                                <td colspan="5">
                                                    <div class="d-flex align-items-start">
                                                        <i class="fas fa-comment text-muted me-2 mt-1"></i>
                                                        <div>
                                                            <small class="text-muted fw-bold">ملاحظات المقيم:</small>
                                                            <p class="mb-0 small">@criteria.Notes</p>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Evaluator Notes -->
    @if (!string.IsNullOrEmpty(Model.EvaluatorNotes))
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            ملاحظات المقيم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-quote-left text-muted me-3 mt-1"></i>
                            <div>
                                <p class="mb-0">@Model.EvaluatorNotes</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Timeline -->
    <div class="row">
        <div class="col-12">
            <div class="card border-dark">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        الجدول الزمني
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تاريخ التقييم</h6>
                                <p class="text-muted mb-0">@Model.EvaluatedAt.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                        </div>
                        @if (Model.UpdatedAt.HasValue && Model.UpdatedAt.Value != Model.EvaluatedAt)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">آخر تحديث</h6>
                                    <p class="text-muted mb-0">@Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</p>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .card-header {
        border-bottom: none;
    }
    
    /* تحسين مظهر الجدول القابل للتمرير */
    .table-responsive {
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    
    .table-responsive::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
    
    /* تثبيت رأس الجدول عند التمرير */
    .table-responsive table thead th {
        position: sticky;
        top: 0;
        background: #f8f9fa;
        z-index: 10;
        border-bottom: 2px solid #dee2e6;
    }
</style> 
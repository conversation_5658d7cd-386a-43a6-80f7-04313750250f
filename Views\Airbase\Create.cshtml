@model RafoEvaluation.ViewModels.AirbaseCreateViewModel
@{
    ViewData["Title"] = "Add New Airbase";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Add New Airbase
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div class="mb-3">
                            <label asp-for="AirbaseName" class="form-label"></label>
                            <input asp-for="AirbaseName" class="form-control" placeholder="Enter airbase name" />
                            <span asp-validation-for="AirbaseName" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a asp-action="Index" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>Save Airbase
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
